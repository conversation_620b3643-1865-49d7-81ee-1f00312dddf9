<template>
  <div
    id="iframeCon"
    v-loading="loadingIframe"
    element-loading-text="拼命加载中..."
    :style="{
      overflow: 'hidden',
      height
    }"
  />
</template>

<script>
import { showDecument } from '@/api/common/index'
export default {
  props: {
    source: {
      required: true,
      type: String,
      default: ''
    },

    height: {
      type: String,
      default: '600px'
    }
  },

  data() {
    return {
      loadingIframe: false
    }
  },

  watch: {
    source: {
      handler(newVal) {
        if (!newVal) return
        this.showDocument(newVal)
      },

      immediate: true
    }
  },

  methods: {
    /** 文档预览 */
    showDocument(url) {
      this.loadingIframe = true

      console.log('显示文档')
      this.$nextTick().then(() => {
        this.addIframe(url)
      })
    },

    // 创建预览
    addIframe(url) {
      showDecument({ url: url }).then((res) => {
        console.log('获取文档连接')
        // 新增清理旧iframe逻辑
        while (this.$el.firstChild) {
          this.$el.removeChild(this.$el.firstChild)
        }
        const iframe = document.createElement('iframe')
        iframe.setAttribute('frameborder', 0)
        iframe.setAttribute('width', '100%')
        iframe.setAttribute('height', '100%')
        iframe.setAttribute('id', 'ifm')
        iframe.setAttribute('src', res.message)
        this.$nextTick().then(() => {
          this.$el.appendChild(iframe)
        })
        if (iframe.attachEvent) {
          // 兼容IE
          iframe.attachEvent('onload', () => {
            console.log('iframe加载完成', iframe)
            // 加载完成
            this.$emit('iframe-loaded')
            this.loadingIframe = false
          })
        } else {
          iframe.onload = () => {
            // 加载完成
            this.$emit('iframe-loaded')
            this.loadingIframe = false
          }
        }
      })
    }
  }
}
</script>
