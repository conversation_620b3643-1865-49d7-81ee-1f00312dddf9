import request, {cloud} from '@/framework/utils/request'

// 查询积分任务管理列表
export function listIntegrationTask(query) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationTask/page',
    method: 'get',
    params: query
  })
}

// 查询积分任务管理列表
export function getUserIntegrationTask(query) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationTask/getUserIntegrationTask',
    method: 'get',
    params: query
  })
}

// 查询积分任务管理详细
export function getIntegrationTask(id) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationTask/detail?id=' + id,
    method: 'get'
  })
}

// 新增积分任务管理
export function addIntegrationTask(data) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationTask/add',
    method: 'post',
    data: data
  })
}

// 修改积分任务管理
export function updateIntegrationTask(data) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationTask/edit',
    method: 'post',
    data: data
  })
}

// 删除积分任务管理
export function delIntegrationTask(id) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationTask/delete',
    method: 'post',
    data: { ids: id }
  })
}

//获取每日积分上限
export function getTodayIntegration(type) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationTask/getTodayIntegration',
    method: 'get',
    params: { type: type }
  })
}

//保存每日积分上限
export function saveTodayIntegration(data) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationTask/saveTodayIntegration',
    method: 'post',
    data: data
  })
}
