import { default as request, cloud } from '@/framework/utils/request'

// 查询模板管理列表
export function listTemplate(query) {
  return request({
    url: `${cloud.notice}/sysMessageTemplate/page/V2`,
    method: 'get',
    params: query
  })
}
// 查询消息类型列表
export function listMessageType(query) {
  return request({
    url: `${cloud.notice}/sysMessageType/list/V2`,
    method: 'get',
    params: query
  })
}

// 查询模板管理详细
export function getTemplate(templateId) {
  return request({
    url: `${cloud.notice}/sysMessageTemplate/detail?templateId=${templateId}`,
    method: 'get'
  })
}

export function getTemplateCode(templateCode) {
  return request({
    url: `${cloud.notice}/sysMessageTemplate/detail?templateCode=${templateCode}`,
    method: 'get'
  })
}
// 新增模板管理
export function addTemplate(data) {
  return request({
    url: `${cloud.notice}/sysMessageTemplate/add`,
    method: 'post',
    data
  })
}

// 修改模板管理
export function updateTemplate(data) {
  return request({
    url: `${cloud.notice}/sysMessageTemplate/edit`,
    method: 'post',
    data
  })
}

// 删除模板管理
export function delTemplate(templateId) {
  return request({
    url: `${cloud.notice}/sysMessageTemplate/delete`,
    method: 'post',
    data: { templateId }
  })
}

// 保存并发送
export function sendTemplate(data) {
  return request({
    url: `${cloud.notice}/sysMessageCenter/testSendMessage`,
    method: 'post',
    data
  })
}

// 根据类型获取渠道

export function getChannelListByTypeCode(typeCode) {
  return request({
    url: `${cloud.notice}/sysMessageTypeChannel/getChannelListByTypeCode?typeCode=${typeCode}`,
    method: 'get'
  })
}
