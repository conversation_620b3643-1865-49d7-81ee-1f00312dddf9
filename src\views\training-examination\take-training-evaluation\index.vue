<template>
  <div class="app-container">
    <div class="mainbox" :loading="loading">
      <div class="exam-title header">
        <div>培训课程评价</div>
      </div>

      <div class="exam-container">
        <div v-for="(item,idx) in evaluationList" :key="item.id" class="exam-multiple">
          <div class="exam-title">
            {{ item.label }}
          </div>

          <div v-if="item.label !== '其他'">
            <div
              v-for="(item1,index) in item.list" :key="index"
              class="exam-item"
            >
              <div class="content">
                <span>{{ querOrder(idx,index) + '、' }}</span>

                <div>{{ item1.content }}</div>
              </div>

              <el-radio-group v-model="item1.userAnswer" :disabled="mode === 'view'">
                <el-radio :label="0">
                  {{ item1.optionA }}
                </el-radio>

                <el-radio :label="1">
                  {{ item1.optionB }}
                </el-radio>

                <el-radio :label="2">
                  {{ item1.optionC }}
                </el-radio>

                <el-radio :label="3">
                  {{ item1.optionD }}
                </el-radio>

                <el-radio :label="4">
                  {{ item1.optionE }}
                </el-radio>
              </el-radio-group>
            </div>
          </div>

          <div v-else>
            <div v-for="(item1,index) in item.list" :key="index" class="exam-short">
              <div class="content">
                <span>{{ querOrder(idx,index) + '、' }}</span>

                {{ item1.content }}
              </div>

              <p v-if="mode === 'view'">
                {{ item1.userAnswer }}
              </p>

              <el-input
                v-else
                v-model="item1.userAnswer"
                type="textarea"
                :rows="4"
                maxlength="500"
                show-word-limit
              />
            </div>
          </div>
        </div>
      </div>

      <div class="submit">
        <el-button v-if="mode === 'view'" @click="goBack">
          返回
        </el-button>

        <template v-else>
          <el-button type="primary" @click="handleSave">
            保存
          </el-button>

          <el-button type="primary" @click="handleSubmit">
            提交
          </el-button>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { MessageBox } from 'element-ui'
import {
  getEvaluationQuestions,
  getTrainingRememberDetailByTrainingId,
  trainingRememberSave
} from '@/api/training-examination/take-training-evaluation'

export default {
  name: 'TakeTrainingEvaluation',

  data() {
    return {
      loading: false,

      detail:{},
      evaluationList:[
        {
          id:1,
          label:'课程内容',
          list:[]
        },
        {
          id:2,
          label:'授课讲师评分',
          list:[]
        },
        {
          id:3,
          label:'培训的组织与服务',
          list:[]
        },
        {
          id:4,
          label:'其他',
          list:[]
        }
      ]
    }
  },

  computed: {
    mode() {
      return this.$route.query.mode
    }
  },

  created() {
    this.getTrainingEvaluationList()
  },

  methods:{
    // 获取培训评价内容
    getTrainingEvaluationList() {
      this.loading = true
      getEvaluationQuestions().then((res) => {
        const list = JSON.parse(res.data)
        getTrainingRememberDetailByTrainingId({ trainingId: this.$route.query.trainId }).then((res) => {
          this.detail = res.data

          this.loading = false
          if (this.detail.evaluationContent) {
            const tempList = JSON.parse(this.detail.evaluationContent)
            if (Array.isArray(tempList)) {
              tempList.forEach((temp) => {
                list.find((item) => {
                  if (temp.id === item.id) {
                    item.userAnswer = temp.userAnswer
                    return true
                  }
                })
              })
            }
          }

          this.evaluationList[0].list = list.filter((item) => item.typeLabel == '课程内容')
          this.evaluationList[1].list = list.filter((item) => item.typeLabel == '授课讲师评分')
          this.evaluationList[2].list = list.filter((item) => item.typeLabel == '培训的组织与服务')
          this.evaluationList[3].list = list.filter((item) => item.typeLabel == '其他')
        }).catch(() => {
          this.loading = false
        })
      }).catch(() => {
        this.loading = false
      })
    },

    // 评价序号
    querOrder(idx, index) {
      switch (idx) {
        case 0:
          return index + 1
        case 1:
          return this.evaluationList[0].list.length + index + 1
        case 2:
          return this.evaluationList[0].list.length + this.evaluationList[1].list.length + index + 1
        case 3:
          return this.evaluationList[0].list.length + this.evaluationList[1].list.length + this.evaluationList[2].list.length + index + 1
      }
    },

    // 判断是否有未填写的评价
    isAllAnswered(list) {
      const tempData = list.find((item) => {
        if (item.userAnswer === '' || item.userAnswer === null || item.userAnswer === undefined) {
          MessageBox.alert(`第 ${item.id} 题未填写`, '提示', {
            confirmButtonText: '确定',
            showClose: false,
            closeOnClickModal: false,
            type: 'error'
          }).then(() => {
          }).catch(() => {})
          return true
        }
      })

      return Boolean(!tempData)
    },

    // 计算培训评价总分
    calculateTotalScore(list) {
      return list.reduce((total, item) => {
        return total + ({
          '0': 5,
          '1': 4,
          '2': 3,
          '3': 2,
          '4': 1
        })[item.userAnswer]
      }, 0)
    },

    // 保存培训评价
    handleSave() {
      const list = [...this.evaluationList[0].list, ...this.evaluationList[1].list, ...this.evaluationList[2].list, ...this.evaluationList[3].list]
      const data = {
        id: this.detail.id,
        evaluationContent:JSON.stringify(list.map((item) => ({
          id: item.id,
          userAnswer: item.userAnswer
        })))
      }

      trainingRememberSave(data).then((res) => {
        this.$message.success('保存成功！')
        this.goBack()
      })
    },

    // 提交培训评价
    handleSubmit() {
      const list = [...this.evaluationList[0].list, ...this.evaluationList[1].list, ...this.evaluationList[2].list, ...this.evaluationList[3].list]

      if (!this.isAllAnswered(list)) return
      const scoreList = [...this.evaluationList[0].list, ...this.evaluationList[1].list, ...this.evaluationList[2].list]

      const data = {
        id: this.detail.id,
        evaluation: this.calculateTotalScore(scoreList),
        evaluationStatus: '1',
        evaluationContent: JSON.stringify(list.map((item) => {
          return {
            id: item.id,
            userAnswer: item.userAnswer
          }
        }))
      }

      trainingRememberSave(data).then((res) => {
        this.$message.success('评价成功!')
        this.goBack()
      })
    },

    goBack() {
      if (this.$route.query.from === 'myTraining') {
        this.$router.push('/trainingExamination/myTraining')
      } else {
        this.$router.push('/trainingExamination/myExam')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container{
  background: #fff;

  .mainbox{
    display: flex;
    flex-direction: column;
    align-items: center;

    .exam-title{
      width: 100%;
      height: 50px;
      background: #f2f2f2;
      padding: 0 20px;
      display: flex;
      align-items: center;
      color: #333;
      font-size: 16px;
      font-weight: 700;

      &.header{
        justify-content: center;
      }
    }

    .exam-container{
      margin-top: 20px;
      width: 96%;

      .exam-item{
        margin: 20px;

        .content{
          color: #333;
          font-size: 16px;
          display: flex;
        }

        &:not(:last-of-type){
          border-bottom: 1px solid #eee;
        }
      }

      .exam-short{
        margin: 20px 0;
        padding: 0 20px;
        color: #333;
        font-size: 16px;

        .short-title{
          margin: 15px 0;
        }
      }
    }

    .submit{
      margin-top: 40px;
      text-align: center;
    }
  }
}

.el-radio-group{
  margin: 15px 0 15px 20px;
}
</style>

