<!--
  * @Author: <PERSON><PERSON>yan <EMAIL>
  * @Date: 2024-07-10 16:31:59
  * @LastEditors: jiadongjin <EMAIL>
  * @LastEditTime: 2024-09-03 16:23:13
  * @FilePath: \isrmp_vue\src\components\assess-dialog\assess-index.vue
  * @Description:
  * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
-->
<template>
  <div>
    <el-dialog title="评估" :visible.sync="outerVisible" :before-close="handleCancel">
      <div class="outer-con">
        <div class="assess-btn">
          <span @click="handleRecord">
            评估记录
          </span>
        </div>

        <el-form
          ref="assessRef"
          :model="assessForm"
          label-width="100px"
          :rules="rules"
        >
          <el-form-item label="评估记录" prop="evaluateContent">
            <el-input
              v-model="assessForm.evaluateContent"
              type="textarea"
              :rows="4"
              maxlength="500"
              show-word-limit
              class="limit"
            />
          </el-form-item>

          <el-form-item label="评估时间" prop="evaluateTime">
            <el-date-picker
              v-model="assessForm.evaluateTime"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="评估时间"
            />
          </el-form-item>
        </el-form>
      </div>

      <el-dialog
        width="40%"
        title="评估记录"
        :visible.sync="innerVisible"
        append-to-body
      >
        <el-table
          :data="tableData"
          style="width: 100%"
        >
          <el-table-column
            prop="evaluateTime"
            label="评估日期"
          />

          <el-table-column
            prop="evaluateContent"
            label="评估记录"
          />
        </el-table>
      </el-dialog>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="saveLoading" @click="addAssess">
          保 存
        </el-button>

        <el-button @click="handleCancel">
          取 消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCompanyEvaluate, addCompanyEvaluate } from "@/api/related-management/company-evaluate"
export default {
  props: {
    /**
     * 评估类型
     */
    type: {
      type: String,
      default: ''
    }
  },

  data(){
    return{
      saveLoading: false,
      innerVisible: false,
      outerVisible: false,
      assessForm: {
        evaluateContent: '',
        evaluateTime: ''
      },

      tableData: [],
      rules: {
        evaluateContent: [
          {
            required: true,
            message: "请输入评估内容",
            trigger: "blur"
          }
        ],

        evaluateTime: [
          {
            required: true,
            trigger: "change",
            validator: (rule, value, callback) => {
              if (!value || value.length == 0) {
                callback(new Error('请选择评估时间'))
              }  else {
                callback()
              }
            }
          }
        ]
      },

      currentId: ''
    }
  },

  mounted(){

  },

  methods:{
    // 打开新增弹窗
    open(id, type) {
      this.$nextTick().then(() => {
        this.outerVisible = type
        this.currentId = id
      })
    },

    /** 新增评估 */
    addAssess(){
      this.saveLoading = true
      this.$refs.assessRef.validate( (valid) => {
        if (valid) {
          this.assessForm.targetId = this.currentId
          this.assessForm.target = this.type
          addCompanyEvaluate(this.assessForm).then(res=>{
            this.$message.success('操作成功!')
            this.saveLoading = false
            this.outerVisible = false
            this.assessForm = {
              evaluateContent:'',
              evaluateTime: ''
            }
          })
        }
      })

    },

    /** 查看评估记录 */
    handleRecord(){
      this.innerVisible = true
      listCompanyEvaluate({targetId: this.currentId,target: this.type}).then(res=>{
        this.tableData = res.data.rows
      })
    },

    handleCancel(){
      this.outerVisible = false
      this.saveLoading = false
      this.assessForm = {
        evaluateContent: '',
        evaluateTime:''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
 .assess-btn{
    display: flex;
    justify-content: end;
    flex: 1;
    margin-bottom: 20px;
    cursor: pointer;
    color: #3461FF;
  }
</style>
