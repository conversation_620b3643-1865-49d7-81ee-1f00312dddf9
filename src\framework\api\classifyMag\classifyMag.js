import { default as request, cloud } from '@/framework/utils/request'

// 查询列表
export function listclassifyMag(query) {
  return request({
    url: `${cloud.dqbasic}/sysProcessCategory/page`,
    method: 'get',
    params: query
  })
}

// 详情
export function getclassifyMag(categoryId) {
  return request({
    url: `${cloud.dqbasic}/sysProcessCategory/detail?categoryId=${categoryId}`,
    method: 'get'
  })
}

// 新增
export function addclassifyMag(data) {
  return request({
    url: `${cloud.dqbasic}/sysProcessCategory/add`,
    method: 'post',
    data
  })
}

// 修改
export function updateclassifyMag(data) {
  return request({
    url: `${cloud.dqbasic}/sysProcessCategory/edit`,
    method: 'post',
    data
  })
}

// 删除
export function delclassifyMag(categoryId) {
  return request({
    url: `${cloud.dqbasic}/sysProcessCategory/delete`,
    method: 'post',
    data: { categoryId }
  })
}
