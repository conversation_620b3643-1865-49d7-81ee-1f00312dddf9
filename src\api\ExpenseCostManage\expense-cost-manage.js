/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-04-22 17:04:45
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-04-26 10:33:39
 * @FilePath: \isrmp_vue\src\api\ExpenseCostManage\expense-cost-manage.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import { default as request, cloud } from '@/framework/utils/request'

// 查询费用支出管理列表
export function listExpenseCostManage(query) {
  return request({
    url: cloud.dqbasic + '/expenseCostManage/page',
    method: 'get',
    params: query
  })
}

// 查询费用支出管理详细
export function getExpenseCostManage(id) {
  return request({
    url: cloud.dqbasic + '/expenseCostManage/detail?id=' + id,
    method: 'get'
  })
}

// 新增费用支出管理
export function addExpenseCostManage(data) {
  return request({
    url: cloud.dqbasic + '/expenseCostManage/add',
    method: 'post',
    data: data
  })
}

// 修改费用支出管理
export function updateExpenseCostManage(data) {
  return request({
    url: cloud.dqbasic + '/expenseCostManage/edit',
    method: 'post',
    data: data
  })
}

// 删除费用支出管理
export function delExpenseCostManage(id) {
  return request({
    url: cloud.dqbasic + '/expenseCostManage/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 修改状态
export function changeStatus(data) {
  return request({
    url: cloud.dqbasic + '/expenseCostManage/updateEnable',
    method: 'post',
    data:  data
  })
}
