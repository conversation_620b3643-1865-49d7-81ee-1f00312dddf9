<template>
  <AliyunPlayerModal
    :visible="isModalVisible"
    title="答对以下题目，继续视频学习"
    @closed="handleClosed"
  >
    <QuestionItem
      v-model="userAnswer"
      :question="question"
    >
      <template #default="{ isCorrect }">
        <div class="in-video-quiz-action">
          <el-button
            style="margin-left: auto;"
            type="primary"
            @click="handleSubmit(isCorrect)"
          >
            提交
          </el-button>
        </div>
      </template>
    </QuestionItem>
  </AliyunPlayerModal>
</template>

<script>
import QuestionItem from '@/components/question-item'
import AliyunPlayerModal from '@/components/aliyun-player-modal'

export default {
  name: 'InVideoQuizModal',
  components: {
    QuestionItem,
    AliyunPlayerModal
  },

  inheritAttrs: false,

  props: {
    question: {
      type: Object,
      default: () => ({})
    },

    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      userAnswer: ''
    }
  },

  computed: {
    isModalVisible: {
      get() {
        return this.visible
      },

      set(val) {
        return this.$emit('update:visible', val)
      }
    }
  },

  mounted() {},

  methods: {
    handleClosed() {
      this.userAnswer = ''
    },

    handleSubmit(isCorrect) {
      this.$emit('submit', isCorrect)
    }
  }
}
</script>

<style lang="scss" scoped>
.in-video-quiz-mask {
  --player-controlbar-height: 40px;
  --inset-bottom: calc(var(--player-controlbar-height));

  background-color: #00000085;
  backdrop-filter: blur(20px);
  position: absolute;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;

  .in-video-quiz {
    background-color: #fff;
    width: 80%;
    max-width: 700px;

    ::v-deep .el-card__header {
      font-weight: bold;
      font-size: 16px;
    }

    .in-video-quiz-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 20px;
      padding: 20px;
    }

    .in-video-quiz-action {
      margin-top: 20px;
      display: flex;
    }

    .error-text {
      color: #f56c6c;
    }
  }
}
</style>
