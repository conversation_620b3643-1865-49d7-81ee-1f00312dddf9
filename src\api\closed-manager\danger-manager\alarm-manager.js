import request from '@/framework/utils/request'

// 查询报警管理列表
export function listCarAlarm(query) {
  return request({
    url: '/project/hazardCarAlarm/page',
    method: 'get',
    params: query
  })
}

// 查询报警管理详细
export function getCarAlarm(id) {
  return request({
    url: '/project/hazardCarAlarm/detail?id=' + id,
    method: 'get'
  })
}

// 新增报警管理
export function addCarAlarm(data) {
  return request({
    url: '/project/hazardCarAlarm/add',
    method: 'post',
    data: data
  })
}

// 修改报警管理
export function updateCarAlarm(data) {
  return request({
    url: '/project/hazardCarAlarm/edit',
    method: 'post',
    data: data
  })
}

// 删除报警管理
export function delCarAlarm(id) {
  return request({
    url: '/project/hazardCarAlarm/delete',
    method: 'post',
    data: {ids: id}
  })
}

// 消警报警管理
export function cancelCarAlarm(id) {
  return request({
    url: '/project/hazardCarAlarm/cancel',
    method: 'post',
    data: {ids: id}
  })
}
