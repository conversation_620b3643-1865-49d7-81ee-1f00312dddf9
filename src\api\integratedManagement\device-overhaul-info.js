import request, {cloud} from '@/framework/utils/request'

// 查询装置大停车和检修列表
export function listDeviceOverhaulInfo(query) {
  return request({
    url: cloud.dqbasic + '/deviceOverhaulInfo/page',
    method: 'get',
    params: query
  })
}

// 查询装置大停车和检修详细
export function getDeviceOverhaulInfo(id) {
  return request({
    url: cloud.dqbasic + '/deviceOverhaulInfo/detail?id=' + id,
    method: 'get'
  })
}

// 新增装置大停车和检修
export function addDeviceOverhaulInfo(data) {
  return request({
    url: cloud.dqbasic + '/deviceOverhaulInfo/add',
    method: 'post',
    data: data
  })
}

// 修改装置大停车和检修
export function updateDeviceOverhaulInfo(data) {
  return request({
    url: cloud.dqbasic + '/deviceOverhaulInfo/edit',
    method: 'post',
    data: data
  })
}

// 删除装置大停车和检修
export function delDeviceOverhaulInfo(id) {
  return request({
    url: cloud.dqbasic + '/deviceOverhaulInfo/delete',
    method: 'post',
    data: { ids: id }
  })
}
