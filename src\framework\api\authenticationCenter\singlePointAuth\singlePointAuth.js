import { default as request, cloud } from '@/framework/utils/request'

// 查询单点认证列表
export function listApp(query) {
  return request({
    url: `${cloud.auth}/singleAuth/page`,
    method: 'get',
    params: query
  })
}

// 查询单点认证详细
export function getApp(id) {
  return request({
    url: `${cloud.auth}/singleAuth/detail?id=${id}`,
    method: 'get'
  })
}

// 新增单点认证
export function addApp(data) {
  return request({
    url: `${cloud.auth}/singleAuth/add`,
    method: 'post',
    data
  })
}

// 修改单点认证
export function updateApp(data) {
  return request({
    url: `${cloud.auth}/singleAuth/edit`,
    method: 'post',
    data
  })
}

// 删除单点认证
export function delApp(id) {
  return request({
    url: `${cloud.auth}/singleAuth/delete`,
    method: 'post',
    data: { id }
  })
}

// 根据租户获取第三方应用
export function getThirdAppList(query) {
  return request({
    url: `${cloud.manage}/sysApp/getThirdAppList`,
    method: 'get',
    params: query
  })
}
