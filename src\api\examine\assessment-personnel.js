import request, {cloud}  from '@/framework/utils/request'

// 查询人员考核列表
export function listAssessmentPersonnel(query) {
  return request({
    url: cloud.dqbasic +'/assessmentPersonnel/page',
    method: 'get',
    params: query
  })
}

// 查询人员考核详细
export function getAssessmentPersonnel(id) {
  return request({
    url: cloud.dqbasic +'/assessmentPersonnel/detail?id=' + id,
    method: 'get'
  })
}

// 新增人员考核
export function addAssessmentPersonnel(data) {
  return request({
    url: cloud.dqbasic +'/assessmentPersonnel/add',
    method: 'post',
    data: data
  })
}

// 修改人员考核
export function updateAssessmentPersonnel(data) {
  return request({
    url: cloud.dqbasic +'/assessmentPersonnel/edit',
    method: 'post',
    data: data
  })
}

// 删除人员考核
export function delAssessmentPersonnel(id) {
  return request({
    url: cloud.dqbasic +'/assessmentPersonnel/delete',
    method: 'post',
    data: { ids: id }
  })
}
