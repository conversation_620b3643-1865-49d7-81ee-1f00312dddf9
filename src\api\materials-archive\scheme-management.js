import request, {cloud} from '@/framework/utils/request'

// 查询方案管理列表
export function listSchemeManagement(query) {
  return request({
    url: cloud.dqbasic + '/maSchemeManagement/page',
    method: 'get',
    params: query
  })
}

// 查询方案管理详细
export function getSchemeManagement(id) {
  return request({
    url: cloud.dqbasic + '/maSchemeManagement/detail?id=' + id,
    method: 'get'
  })
}

// 新增方案管理
export function addSchemeManagement(data) {
  return request({
    url: cloud.dqbasic + '/maSchemeManagement/add',
    method: 'post',
    data: data
  })
}

// 修改方案管理
export function updateSchemeManagement(data) {
  return request({
    url: cloud.dqbasic + '/maSchemeManagement/edit',
    method: 'post',
    data: data
  })
}
// 去重查询物资管理中的类别
export function queryExistCategory(parms) {
  return request({
    url: '/project/maGoodsManagement/queryExistCategory',
    method: 'get',
    parms:parms
  })
}
// 去重查询物资管理中的
export function queryGoodsByCategoryId(params) {
  return request({
    url: '/project/maGoodsManagement/queryGoodsByCategoryId',
    method: 'get',
    params:params
  })
}
// 删除方案管理
export function delSchemeManagement(id) {
  return request({
    url: cloud.dqbasic + '/maSchemeManagement/delete',
    method: 'post',
    data: { ids: id }
  })
}
