import { default as request, cloud } from '@/framework/utils/request'
// 基础数据分类-分页查询列表
export function basicMaterialTypePage(query) {
  return request({
    url: `${cloud.devcenter}/basicMaterialType/page`,
    method: 'get',
    params: query
  })
}

// 基础数据分类-新增
export function basicMaterialTypeAdd(data) {
  return request({
    url: `${cloud.devcenter}/basicMaterialType/add`,
    method: 'post',
    data
  })
}
// 基础数据分类-修改
export function basicMaterialTypeEdit(data) {
  return request({
    url: `${cloud.devcenter}/basicMaterialType/edit`,
    method: 'post',
    data
  })
}
// 基础数据分类- 删除
export function basicMaterialTypeDelete(data) {
  return request({
    url: `${cloud.devcenter}/basicMaterialType/del`,
    method: 'post',
    data
  })
}
// 导入
export function uploadFile(data) {
  return request({
    url: `${cloud.devcenter}/basicMaterialType/import`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 下载导入失败的数据
// export function downloadFailData(data) {
//   return request({
//     url: `${cloud.usercenter}sysUser/downloadFailData`,
//     method: 'post',
//     data
//   })
// }
// 下载导入模板
export function downloadTemplate(params) {
  return request({
    url: `${cloud.devcenter}/basicMaterialType/import/template`,
    method: 'get',
    params: {
      enclosure: '基础数据导入模板.xls'
    },
    responseType: 'arraybuffer'
  })
}
// 导出部分basicMaterialType/export
export function exportMaterialType(data) {
  return request({
    url: `${cloud.devcenter}/basicMaterialType/export`,
    method: 'post',
    // params,
    data,
    responseType: 'arraybuffer'
    // headers: {
    //   'Content-Type': 'application/x-www-form-urlencoded'
    // }
  })
}
// 导出全部 basicMaterialType/exportAll
export function exportAll(params) {
  return request({
    url: `${cloud.devcenter}/basicMaterialType/exportAll`,
    method: 'post',
    params,
    responseType: 'arraybuffer'
  })
}
//-----------------------------------------------
// 基础数据项-分页查询列表
export function basicMaterialPage(query) {
  return request({
    url: `${cloud.devcenter}/basicMaterial/page`,
    method: 'get',
    params: query
  })
}
// 基础数据项-新增
export function basicMaterialAdd(data) {
  return request({
    url: `${cloud.devcenter}/basicMaterial/add`,
    method: 'post',
    data
  })
}
// 基础数据项-修改
export function basicMaterialEdit(data) {
  return request({
    url: `${cloud.devcenter}/basicMaterial/edit`,
    method: 'post',
    data
  })
}
// 基础数据项- 删除
export function basicMaterialDelete(data) {
  return request({
    url: `${cloud.devcenter}/basicMaterial/del`,
    method: 'post',
    data
  })
}
// 基础数据项- 导入
export function materialUploadFile(data) {
  return request({
    url: `${cloud.devcenter}/basicMaterial/import`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 基础数据项-下载导入模板
export function materialDownloadTemplate(params) {
  return request({
    url: `${cloud.devcenter}/basicMaterial/import/template`,
    method: 'get',
    params: {
      enclosure: '基础数据项导入模板.xls'
    },
    responseType: 'arraybuffer'
  })
}
// 基础数据项-导出部分basicMaterial/export
export function exportMaterial(data) {
  return request({
    url: `${cloud.devcenter}/basicMaterial/export`,
    method: 'post',
    // params,
    data,
    responseType: 'arraybuffer'
    // headers: {
    //   'Content-Type': 'application/x-www-form-urlencoded'
    // }
  })
}
// 基础数据项-导出全部 basicMaterialType/exportAll
export function exportAllMaterial(data) {
  return request({
    url: `${cloud.devcenter}/basicMaterial/exportAll`,
    method: 'post',
    data,
    responseType: 'arraybuffer'
  })
}
// 基础数据分类-查询详情
export function getTypeDetailByBasicId(query) {
  return request({
    url: `${cloud.devcenter}/basicMaterialType/getTypeDetailByBasicId`,
    method: 'get',
    params: query
  })
}

