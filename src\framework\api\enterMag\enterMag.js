import { default as request, cloud } from '@/framework/utils/request'

// 查询列表
export function listenterMag(query) {
  return request({
    url: `${cloud.dqbasic}/sysProcessEntrance/page`,
    method: 'get',
    params: query
  })
}

// 获取详情
export function getenterMag(entranceId) {
  return request({
    url: `${cloud.dqbasic}/sysProcessEntrance/detail?entranceId=${entranceId}`,
    method: 'get'
  })
}

// 修改
export function updateenterMag(data) {
  return request({
    url: `${cloud.dqbasic}/sysProcessEntrance/edit`,
    method: 'post',
    data
  })
}
// 获取流程类型列表
export function categoryList(query) {
  return request({
    url: `${cloud.dqbasic}/sysProcessCategory/list`,
    method: 'get',
    params: query
  })
}

