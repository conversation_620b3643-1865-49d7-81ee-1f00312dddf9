/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-04-16 15:39:54
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-09-14 10:35:16
 * @FilePath: \isrmp_vue\src\api\ExpenseCategoryManage\expense-category-manage.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import { default as request, cloud } from '@/framework/utils/request'

// 查询费用分类管理列表
export function listExpenseCategoryManage(query) {
  return request({
    url: cloud.dqbasic + '/expenseCategoryManage/page',
    method: 'get',
    params: query
  })
}

// 查询费用分类管理详细
export function getExpenseCategoryManage(id) {
  return request({
    url: cloud.dqbasic + '/expenseCategoryManage/selectExpenseCategoryById?id=' + id,
    method: 'get'
  })
}

// 新增费用分类管理
export function addExpenseCategoryManage(data) {
  return request({
    url: cloud.dqbasic + '/expenseCategoryManage/add',
    method: 'post',
    data: data
  })
}

// 修改费用分类管理
export function updateExpenseCategoryManage(data) {
  return request({
    url: cloud.dqbasic + '/expenseCategoryManage/edit',
    method: 'post',
    data: data
  })
}
// 启用停用费用分类管理
export function updateEnable(data) {
  return request({
    url: cloud.dqbasic + '/expenseCategoryManage/updateEnable',
    method: 'post',
    data: data
  })
}

// 删除费用分类管理
export function delExpenseCategoryManage(id) {
  return request({
    url: cloud.dqbasic + '/expenseCategoryManage/delete?id=' + id,
    method: 'get'
  })
}
