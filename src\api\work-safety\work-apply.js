/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-20 16:43:48
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2025-04-29 14:06:42
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, { cloud } from '@/framework/utils/request'
import { getRequest, postRequest } from '@/framework/utils/request'

// // 查询列表
// export function getList(query) {
//   return getRequest(cloud.dqbasic + '/specialWorkReport/page', query)
// }
// 查询列表
export function getList(query) {
  return getRequest(`${cloud.dqbasic}/bizSwEntrance/page`, query)
}
// 查询详细
export function getDetail(id) {
  return getRequest(`${cloud.dqbasic}/specialWorkReport/detail?id=${id}`)
}
// 查询详细
export function getHotWorkDetail(query) {
  return getRequest(`${cloud.dqbasic}/bizSwFire/detail`, query)
}
// 提交验收
export function submit(data) {
  return postRequest(`${cloud.dqbasic}/bizSwFire/submit/check`, data)
}
/**
 * @description: 提交验收(验收通过)
 * @param {*} data
 * @return {*} 提交验收(验收通过)
 */
export function complete(data) {
  return postRequest(`${cloud.dqbasic}/bizSwFire/complete/check`, data)
}
/**
 * @description: 提交验收(验收不通过)
 * @param {*} data
 * @return {*} 提交验收(验收不通过)
 */
export function reject(data) {
  return postRequest(`${cloud.dqbasic}/bizSwFire/reject/check`, data)
}
// 提交验收
export function start(data) {
  return postRequest(`${cloud.dqbasic}/bizSwFire/start/work`, data)
}
// 提交验收
export function stop(data) {
  return postRequest(`${cloud.dqbasic}/bizSwFire/stop/unstarted`, data)
}
// 新增
export function add(data) {
  return postRequest(`${cloud.dqbasic}/specialWorkReport/add`, data)
}
// 新增特殊作业-动火作业数据
export function addBizSwFire(data) {
  return postRequest(`${cloud.dqbasic}/bizSwFire/add`, data)
}
// 修改特殊作业-动火作业数据
export function editBizSwFire(data) {
  return postRequest(`${cloud.dqbasic}/bizSwFire/edit`, data)
}
// 新增特殊作业-动土作业数据
export function addBizSwBreakGround(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBreakGround/add`, data)
}
// 修改特殊作业-动土作业数据
export function editBizSwBreakGround(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBreakGround/edit`, data)
}
// 删除特殊作业-动土作业数据
export function delBizSwBreakGround(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBreakGround/delete`, data)
}
// 详情特殊作业-动土作业数据
export function detailBizSwBreakGround(query) {
  return getRequest(`${cloud.dqbasic}/bizSwBreakGround/detail`, query)
}
// 提交验收
export function checkBizSwBreakGround(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBreakGround/submit/check`, data)
}
// 开始作业
export function workBizSwBreakGround(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBreakGround/start/work`, data)
}
// 停止作业
export function stopBizSwBreakGround(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBreakGround/stop/unstarted`, data)
}

// 验收完成
export function completeBizSwBreakGround(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBreakGround/complete/check`, data)
}
// 新增特殊作业-断路作业数据
export function addBizSwOpenCircuit(data) {
  return postRequest(`${cloud.dqbasic}/bizSwOpenCircuit/add`, data)
}
// 修改特殊作业-断路作业数据
export function editBizSwOpenCircuit(data) {
  return postRequest(`${cloud.dqbasic}/bizSwOpenCircuit/edit`, data)
}
// 删除特殊作业-断路作业数据
export function delBizSwOpenCircuit(data) {
  return postRequest(`${cloud.dqbasic}/bizSwOpenCircuit/delete`, data)
}
// 详情特殊作业-断路作业数据
export function detailBizSwOpenCircuit(query) {
  return getRequest(`${cloud.dqbasic}/bizSwOpenCircuit/detail`, query)
}
// 提交验收
export function checkBizSwOpenCircuit(data) {
  return postRequest(`${cloud.dqbasic}/bizSwOpenCircuit/submit/check`, data)
}
// 开始作业
export function workBizSwOpenCircuit(data) {
  return postRequest(`${cloud.dqbasic}/bizSwOpenCircuit/start/work`, data)
}
// 停止作业
export function stopBizSwOpenCircuit(data) {
  return postRequest(`${cloud.dqbasic}/bizSwOpenCircuit/stop/unstarted`, data)
}
// 验收完成
export function completeBizSwOpenCircuit(data) {
  return postRequest(`${cloud.dqbasic}/bizSwOpenCircuit/complete/check`, data)
}

// 提交验收
export function checkBizSwFire(data) {
  return postRequest(`${cloud.dqbasic}/bizSwFire/submit/check`, data)
}
// 开始作业
export function workBizSwFire(data) {
  return postRequest(`${cloud.dqbasic}/bizSwFire/start/work`, data)
}
// 停止作业
export function stopBizSwFire(data) {
  return postRequest(`${cloud.dqbasic}/bizSwFire/stop/unstarted`, data)
}
// 验收完成
export function completeBizSwFire(data) {
  return postRequest(`${cloud.dqbasic}/bizSwFire/complete/check`, data)
}

// 修改
export function edit(data) {
  return postRequest(`${cloud.dqbasic}/specialWorkReport/edit`, data)
}

// 删除
export function delHotWork(id) {
  return postRequest(`${cloud.dqbasic}/bizSwFire/delete`, { ids: id })
}
/*
 * export function del(id) {
 *   return postRequest(cloud.dqbasic + '/specialWorkReport/delete', { ids: id })
 * }
 */

// 查询已绑设备
export function getDeviceBindingInfo(data) {
  return postRequest(`${cloud.dqbasic}/deviceManager/getBindingWorkInfo`, data)
}
// 安全措施类目所有数据
export function getbizSwMeasureItem(data) {
  return getRequest(`${cloud.dqbasic}/bizSwMeasureItem/list`, data)
}

// 新增特殊作业-盲板作业数据
export function addBizSwBlind(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBlindBlocked/add`, data)
}

// 删除
export function delSwBlindWork(id) {
  return postRequest(`${cloud.dqbasic}/bizSwBlindBlocked/delete`, { ids: id })
}

// 详情特殊作业-盲板作业数据
export function detailBizSwBlind(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBlindBlocked/detail`, data)
}

// 编辑特殊作业-盲板作业数据
export function editBizSwBlind(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBlindBlocked/edit`, data)
}

// 新增特殊作业-高处作业数据
export function addHighBlind(data) {
  return postRequest(`${cloud.dqbasic}/bizSwHighPlace/add`, data)
}

// 删除
export function delHighWork(id) {
  return postRequest(`${cloud.dqbasic}/bizSwHighPlace/delete`, { ids: id })
}

// 详情特殊作业-高处作业数据
export function detailHigh(data) {
  return postRequest(`${cloud.dqbasic}/bizSwHighPlace/detail`, data)
}

// 编辑特殊作业-高处作业数据
export function editHigh(data) {
  return postRequest(`${cloud.dqbasic}/bizSwHighPlace/edit`, data)
}

// 新增特殊作业-吊装作业数据
export function addLiftBlind(data) {
  return postRequest(`${cloud.dqbasic}/bizSwLifting/add`, data)
}

// 删除
export function delLiftWork(id) {
  return postRequest(`${cloud.dqbasic}/bizSwLifting/delete`, { ids: id })
}

// 详情特殊作业-吊装作业数据
export function detailLift(data) {
  return postRequest(`${cloud.dqbasic}/bizSwLifting/detail`, data)
}

// 编辑特殊作业-吊装作业数据
export function editLift(data) {
  return postRequest(`${cloud.dqbasic}/bizSwLifting/edit`, data)
}

// 新增特殊作业-临时用电作业数据
export function addTemporaryBlind(data) {
  return postRequest(`${cloud.dqbasic}/bizSwTemporaryPower/add`, data)
}

// 删除
export function delTemporaryWork(id) {
  return postRequest(`${cloud.dqbasic}/bizSwTemporaryPower/delete`, { ids: id })
}
// 新增特殊作业-受限空间作业数据
export function addBizSwConfinedSpace(data) {
  return postRequest(`${cloud.dqbasic}/bizSwConfinedSpace/add`, data)
}
// 修改特殊作业-受限空间作业数据
export function editBizSwConfinedSpace(data) {
  return postRequest(`${cloud.dqbasic}/bizSwConfinedSpace/edit`, data)
}
// 获取特殊作业-受限空间单条数据详情
export function getBizSwConfinedSpace(data) {
  return getRequest(`${cloud.dqbasic}/bizSwConfinedSpace/detail`, data);
}


// 详情特殊作业-临时用电作业数据
export function detailTemporary(data) {
  return postRequest(`${cloud.dqbasic}/bizSwTemporaryPower/detail`, data)
}

// 编辑特殊作业-临时用电作业数据
export function editTemporary(data) {
  return postRequest(`${cloud.dqbasic}/bizSwTemporaryPower/edit`, data)
}

// 盲板提交
export function mbSubmit(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBlindBlocked/submit/check`, data)
}
// 盲板验收
export function mbComplete(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBlindBlocked/complete/check`, data)
}
// 盲板开始
export function mbStart(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBlindBlocked/start/work`, data)
}
// 盲板暂停
export function mbStop(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBlindBlocked/stop/unstarted`, data)
}


// 高空提交
export function highSubmit(data) {
  return postRequest(`${cloud.dqbasic}/swHighPlace/submit/check`, data)
}
// 高空验收
export function highComplete(data) {
  return postRequest(`${cloud.dqbasic}/swHighPlace/complete/check`, data)
}
// 高空开始
export function highStart(data) {
  return postRequest(`${cloud.dqbasic}/swHighPlace/start/work`, data)
}
// 高空暂停
export function highStop(data) {
  return postRequest(`${cloud.dqbasic}/swHighPlace/stop/unstarted`, data)
}

// 吊装提交
export function dzSubmit(data) {
  return postRequest(`${cloud.dqbasic}/bizSwLifting/submit/check`, data)
}
// 吊装验收
export function dzComplete(data) {
  return postRequest(`${cloud.dqbasic}/bizSwLifting/complete/check`, data)
}
// 吊装开始
export function dzStart(data) {
  return postRequest(`${cloud.dqbasic}/bizSwLifting/start/work`, data)
}
// 吊装暂停
export function dzStop(data) {
  return postRequest(`${cloud.dqbasic}/bizSwLifting/stop/unstarted`, data)
}
// 临时用电作业单条数据详情
export function ydDetail(data) {
  return postRequest(`${cloud.dqbasic}/bizSwTemporaryPower/detail`, data);
}
// 用电提交
export function ydSubmit(data) {
  return postRequest(`${cloud.dqbasic}/bizSwTemporaryPower/submit/check`, data)
}
// 用电验收
export function ydComplete(data) {
  return postRequest(`${cloud.dqbasic}/bizSwTemporaryPower/complete/check`, data)
}
// 用电开始
export function ydStart(data) {
  return postRequest(`${cloud.dqbasic}/bizSwTemporaryPower/start/work`, data)
}
// 用电暂停
export function ydStop(data) {
  return postRequest(`${cloud.dqbasic}/bizSwTemporaryPower/stop/unstarted`, data)
}
// 作业票信息推送接口
export function sendBizSwData(id) {
  return getRequest(`${cloud.dqbasic}/hazmatData/sendByTicketCodeAndTicketType?ticketCode=${id}`)
}
