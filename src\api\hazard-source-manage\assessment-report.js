/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-29 15:13:22
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-30 11:46:11
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */

import request from '@/framework/utils/request'

// 查询评估评价报告列表
export function listAssessmentReport(query) {
  return request({
    url: '/project/assessmentReport/page',
    method: 'get',
    params: query
  })
}

// 查询评估评价报告详细
export function getAssessmentReport(id) {
  return request({
    url: '/project/assessmentReport/detail?id=' + id,
    method: 'get'
  })
}

// 新增评估评价报告
export function addAssessmentReport(data) {
  return request({
    url: '/project/assessmentReport/add',
    method: 'post',
    data: data
  })
}

// 修改评估评价报告
export function updateAssessmentReport(data) {
  return request({
    url: '/project/assessmentReport/edit',
    method: 'post',
    data: data
  })
}

// 删除评估评价报告
export function delAssessmentReport(id) {
  return request({
    url: '/project/assessmentReport/delete',
    method: 'post',
    data: { ids: id }
  })
}
