<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="60%"
    :before-close="handleClose"
    top="5vh"
  >
    <div class="search">
      <el-input
        v-model.trim="queryParams.paperName"
        prefix-icon="el-icon-search"
        maxlength="30"
        placeholder="请输入"
        clearable
        style="width: 240px;"
        @keyup.enter.native="handleQuery"
      />

      <div class="fr">
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="handleQuery"
        >
          搜索
        </el-button>

        <el-button icon="el-icon-refresh" @click="handleReset">
          重置
        </el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      style="width: 100%;"
      border
      highlight-current-row
      :header-cell-style="{ backgroundColor: '#f2f2f2' }"
      :data="tableData"
    >
      <template slot="empty">
        <p>{{ $store.getters.dataText }}</p>
      </template>

      <el-table-column
        label="名称"
        show-overflow-tooltip
        align="center"
        prop="paperName"
        min-width="300"
      />

      <el-table-column
        label="创建时间"
        show-overflow-tooltip
        align="center"
        prop="createTime"
        min-width="120"
      />

      <el-table-column
        label="试卷类型"
        show-overflow-tooltip
        align="center"
        prop="paperType"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.paperType == 1">
            随机题库
          </span>

          <span v-else-if="scope.row.paperType == 2">
            固定题库
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        width="110"
      >
        <template slot-scope="scope">
          <el-button type="text" @click="handleSelect(scope.row)">
            选择
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <dt-pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </el-dialog>
</template>

<script>
import {
  getTrainingExamMainPaperPage
} from '@/api/exam-manage/paper-manage'

export default {
  data() {
    return {
      // 弹框
      dialogVisible: false,

      // 标题
      title:'选择试卷',

      // 表格
      loading:false,
      tableData:[],
      total:0,
      queryParams:{
        paperName:'',
        pageNo:1,
        pageSize:10
      }
    }
  },

  methods: {
    // 初始化
    init() {
      this.queryParams.paperName = ''
      this.handleQuery()
      this.dialogVisible = true
    },

    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 重置
    handleReset() {
      this.queryParams = {
        paperName:'',
        pageNo:1,
        pageSize:10
      }
      this.getList()
    },

    // 获取列表
    getList() {
      const query = JSON.parse(JSON.stringify(this.queryParams))
      this.loading = true
      getTrainingExamMainPaperPage(query).then((data) => {
        this.loading = false
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      }).catch(() => {
        this.loading = false
      })
    },

    // 选择试卷
    handleSelect(row) {
      this.$emit('selectPaper', row)
      this.dialogVisible = false
    },

    // 关闭弹框
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.search{
    display: flex;
    margin-bottom: 20px;
}
</style>
