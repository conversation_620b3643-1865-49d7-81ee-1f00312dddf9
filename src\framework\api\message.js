import { default as request, cloud } from '@/framework/utils/request'
// 工作台通知
const getUnReadList = (params) => request({ url: `${cloud.notice}/sysMessageCenter/mySystemNotice`, method: 'get', params })
const getPageList = (params) => request({ url: `${cloud.notice}/sysMessageCenter/myMessage`, method: 'get', params })
// 清空
const getAllReadList = (params) => request({ url: `${cloud.notice}/sysMessageCenter/clear`, method: 'get', params })
const getReadDetail = (params) => request({ url: `${cloud.notice}/sysMessageCenter/read`, method: 'get', params })
const yesOrNotUnReadMessage = (params) => request({ url: `${cloud.notice}/sysMessageCenter/yesOrNotUnReadMessage`, method: 'get', params })
const getDeleteRead = (params) => request({ url: `${cloud.dqbasic}/sysMessage/delete`, method: 'post', data: params })
const sysMessageExport = (params) => request({ url: `${cloud.dqbasic}/sysMessage/export`, method: 'post', params, responseType: 'arraybuffer' })
const getMessageType = (params) => request({ url: `${cloud.notice}/sysMessageType/list/V2`, method: 'get', params })
// 平台通知
const getMyPlatNotice = (params) => request({ url: `${cloud.notice}/sysMessageCenter/myPlatNotice`, method: 'get', params })

export {
  getUnReadList,
  getPageList,
  getAllReadList,
  getReadDetail,
  getDeleteRead,
  yesOrNotUnReadMessage,
  sysMessageExport,
  getMessageType,
  getMyPlatNotice
}

