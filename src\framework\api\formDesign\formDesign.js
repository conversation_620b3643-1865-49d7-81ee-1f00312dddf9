import { default as request, cloud } from '@/framework/utils/request'

// 表单列表 /workflow/flow/sysForm/getPage
export function listformDesign(query) {
  return request({
    url: `${cloud.process}/flow/sysForm/getPage`,
    method: 'get',
    params: query
  })
}

// 表单列表 /workflow/flow/sysForm/getPage
export function getBizTableNameFormDesign(query) {
  return request({
    url: `${cloud.process}/flow/sysForm/detailByCode`,
    method: 'get',
    params: query
  })
}

// 新增
export function addformDesign(data) {
  return request({
    url: `${cloud.process}/flow/sysForm/add`,
    method: 'post',
    data
  })
}
// 修改详情
export function getformDesign(formId) {
  return request({
    url: `${cloud.process}/flow/sysForm/detail?formId=${formId}`,
    method: 'get'
  })
}
// 修改
export function editformDesign(data) {
  return request({
    url: `${cloud.process}/flow/sysForm/update`,
    method: 'post',
    data
  })
}

// 设计
// export function updateDesign(data) {
//   return request({
//     url: cloud.process + '/sysForm/design',
//     method: 'post',
//     data: data
//   })
// }

// 复制
export function copyformDesign(data) {
  return request({
    url: `${cloud.process}/flow/sysForm/copy`,
    method: 'post',
    data
  })
}

// 删除
export function delformDesign(formId) {
  return request({
    url: `${cloud.process}/flow/sysForm/delete`,
    method: 'post',
    data: { formId }
  })
}

// 设计表单保存 flow/sysForm/savePageDesign
export function savePageDesign(data) {
  return request({
    url: `${cloud.process}/flow/sysForm/savePageDesign`,
    method: 'post',
    data
  })
}
// 获取设计的表单结构
export function getFormJson(query) {
  return request({
    url: `${cloud.process}/flow/sysForm/queryPageDesign`,
    method: 'get',
    params: query
  })
}
// 获取表单类型列表 不分页
export function categoryList(query) {
  return request({
    url: `${cloud.process}/flow/flowCategory/list`,
    method: 'get',
    params: query
  })
}
// 获取表单类型列表  分页  workflow/flow/flowCategory/getPage
// export function categoryList(query) {
//   return request({
//     url: cloud.process + '/flow/flowCategory/getPage',
//     method: 'get',
//     params: query
//   })
// }
// 新增表单类型
export function addCategory(data) {
  return request({
    url: `${cloud.process}/flow/flowCategory/add`,
    method: 'post',
    data
  })
}
// 详情表单类型
export function getCategory(categoryId) {
  return request({
    url: `${cloud.process}/flow/flowCategory/detail?categoryId=${categoryId}`,
    method: 'get'
  })
}
// 修改表单类型
export function editCategory(data) {
  return request({
    url: `${cloud.process}/flow/flowCategory/update`,
    method: 'post',
    data
  })
}
// 删除表单类型
export function delCategory(data) {
  return request({
    url: `${cloud.process}/flow/flowCategory/delete`,
    method: 'post',
    data
  })
}

// 判断设计从表名重复
export function existTableCode(query) {
  return request({
    url: `${cloud.process}/flow/sysForm/existTableCode`,
    method: 'get',
    params: query
  })
}

// // 分页查询列表
// export function listformDesign(query) {
//   return request({
//     url: cloud.process + '/sysForm/page',
//     method: 'get',
//     params: query
//   })
// }

// 新增
// export function addformDesign(data) {
//   return request({
//     url: cloud.process + '/sysForm/add',
//     method: 'post',
//     data: data
//   })
// }

// 修改详情
// export function getformDesign(formId) {
//   return request({
//     url: cloud.process + '/sysForm/detail?formId=' + formId,
//     method: 'get'
//   })
// }
// 修改
// export function editformDesign(data) {
//   return request({
//     url: cloud.process + '/sysForm/edit',
//     method: 'post',
//     data: data
//   })
// }

// 设计
export function updateDesign(data) {
  return request({
    url: `${cloud.process}/sysForm/design`,
    method: 'post',
    data
  })
}

// 复制
// export function copyformDesign(data) {
//   return request({
//     url: cloud.process + '/sysForm/copy',
//     method: 'post',
//     data: data
//   })
// }

// 删除
// export function delformDesign(formId) {
//   return request({
//     url: cloud.process + '/sysForm/delete',
//     method: 'post',
//     data: { formId: formId }
//   })
// }

// 获取表单类型列表
// export function categoryList(query) {
//   return request({
//     url: cloud.process + '/sysProcessCategory/list',
//     method: 'get',
//     params: query
//   })
// }

// ///统一填报-表单数据
// 获取表单结构
// export function getFormJson(query) {
//   return request({
//     url: cloud.process + '/customFormInfo/queryPageDesign',
//     method: 'get',
//     params: query
//   })
// }
// // 表单数据-列表
// export function getList(query) {
//   return request({
//     url: cloud.process + '/customFormData/getList',
//     method: 'get',
//     params: query
//   })
// }
// // 表单数据 -新增
// export function add(url, method, query) {
//   return request({
//     url: cloud.process + url,
//     method: method,
//     params: query
//   })
// }
// // 表单数据 -编辑
// export function edit(url, method, query) {
//   return request({
//     url: cloud.process + url,
//     method: method,
//     params: query
//   })
// }
// // 表单数据 -删除
// export function deletes(url, method, query) {
//   return request({
//     url: cloud.process + url,
//     method: method,
//     params: query
//   })
// }
