import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 根据表单id查询启用的流程定义
  getProcdefByFormId(params) {
    return request({
      url: `${cloud.process}/flow/procdef/getProcdefByFormId`,
      method: 'get',
      params
    })
  },
  // 低代码分类接口
  getLowCodeCategory(params) {
    return request({
      url: `${cloud.process}/flow/flowCategory/getLowCodeCategory`,
      method: 'get',
      params
    })
  },
  // 插件删除
  customFormPluginDelete(data) {
    return request({
      url: `${cloud.devcenter}/customFormPlugin/delete`,
      method: 'POST',
      data
    })
  },
  // 插件新增
  customFormPluginAdd(data) {
    return request({
      url: `${cloud.devcenter}/customFormPlugin/add`,
      method: 'POST',
      data
    })
  },
  // 插件修改
  customFormPluginEdit(data) {
    return request({
      url: `${cloud.devcenter}/customFormPlugin/edit`,
      method: 'POST',
      data
    })
  },
  // 查询详细信息
  customFormPluginDetail(params) {
    return request({
      url: `${cloud.devcenter}/customFormPlugin/detail`,
      method: 'get',
      params
    })
  },
  // 查询表单及表单字段信息(开发中心)
  getAppFormBaseInfo(params) {
    return request({
      url: `${cloud.process}/flow/sysForm/getAppFormBaseInfo`,
      method: 'get',
      params
    })
  },
  // 暂存(开发中心)
  stageExt(data) {
    return request({
      url: `${cloud.process}/flow/procdef/stageExt`,
      method: 'post',
      data
    })
  },
  // 查询流程维护人(开发中心)
  getAppDetail(params) {
    return request({
      url: `${cloud.process}/flow/flowLowCode/getAppDetail`,
      method: 'get',
      params
    })
  }
})
