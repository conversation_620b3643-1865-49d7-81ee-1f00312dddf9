/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-07-01 08:59:03
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-10-30 17:32:01
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud}  from '@/framework/utils/request'

// 查询物资申请列表
export function getList(query) {
  return request({
    url: cloud.dqbasic + '/materialsApply/page',
    method: 'get',
    params: query
  })
}

// 查询物资申请详细
export function getDetail(id) {
  return request({
    url: cloud.dqbasic + '/materialsApply/detail?id=' + id,
    method: 'get'
  })
}

// 物资申请新增
export function add(data) {
  return request({
    url: cloud.dqbasic + '/materialsApply/add',
    method: 'post',
    data: data
  })
}

// 物资申请修改
export function edit(data) {
  return request({
    url: cloud.dqbasic + '/materialsApply/edit',
    method: 'post',
    data: data
  })
}

// 物资申请列表删除
export function del(id) {
  return request({
    url: cloud.dqbasic + '/materialsApply/delete',
    method: 'post',
    data: { ids: id }
  })
}

/** 获取物资状态字典 */
export function getStatusDict(query) {
  return request({
    url: cloud.dqbasic + '/materialsApply/statusDict',
    method: 'get',
    params: query
  })
}

/** 查询物资类型 */
export function getMaterialTypes(query) {
  return request({
    url: cloud.dqbasic + '/materialsApply/materialTypes',
    method: 'get',
    params: query
  })
}

/** 根据物资类型查物资名称 */
export function queryMaterialByType(query) {
  return request({
    url: cloud.dqbasic + '/materialsApply/queryMaterialByType',
    method: 'get',
    params: query
  })
}

// 查询物资发放列表
export function listIssue(query) {
  return request({
    url: cloud.dqbasic + '/materialsIssue/page',
    method: 'get',
    params: query
  })
}

// 查询物资发放详细
export function getIssue(id) {
  return request({
    url: cloud.dqbasic + '/materialsIssue/detail?id=' + id,
    method: 'get'
  })
}

// 新增物资发放
export function addIssue(data) {
  return request({
    url: cloud.dqbasic + '/materialsIssue/add',
    method: 'post',
    data: data
  })
}

// 修改物资发放
export function updateIssue(data) {
  return request({
    url: cloud.dqbasic + '/materialsIssue/edit',
    method: 'post',
    data: data
  })
}

// 删除物资发放
export function delIssue(id) {
  return request({
    url: cloud.dqbasic + '/materialsIssue/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 查询申请-方案列表
export function getSourceList(query) {
  return request({
    url: cloud.dqbasic + '/materialsIssue/source',
    method: 'get',
    params: query
  })
}

// 查询申请-方案详情
export function getSourceDetail(query) {
  return request({
    url: cloud.dqbasic + '/materialsIssue/madetail',
    method: 'get',
    params: query
  })
}

// 查询发放类型
export function getSourceType() {
  return request({
    url: cloud.dqbasic + '/materialsIssue/type',
    method: 'get'
  })
}
// 库存统计
export function ventoryStatistics(params) {
  return request({
    url: cloud.dqbasic + '/maInOutHouse/ventoryStatistics',
    method: 'get',
    params
  })
}

