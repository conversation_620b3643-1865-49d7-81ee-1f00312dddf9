import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 查询
  getManagingOrgUserList(params) {
    return request({
      url: `${cloud.permission}/managerGroup/getManagingOrgUserList`,
      method: 'get',
      params
    })
  },
  // 根据用户IDs查询用户详情列表
  getUserListByUserIds(ids) {
    return request({
      url: `${cloud.permission}/managerGroup/getUserListByUserIds`,
      method: 'get',
      params: {
        userIds: ids
      }
    })
  },
  // 在全量组织树中搜索组织名称，返回平级列表
  searchOrgName(params) {
    return request({
      url: `${cloud.permission}/managerGroup/searchOrgName`,
      method: 'get',
      params: {
        orgName: params.name
      }
    })
  },
  // 全局搜索用户姓名
  searchUserName(params) {
    return request({
      url: `${cloud.permission}/managerGroup/searchUserName`,
      method: 'get',
      params: {
        nameOrAccount: params.name
      }
    })
  }

})
