import request from "@/framework/utils/request"

// 查询四色图所有数据列表（不分页）
export function queryFourColorRiskImageList(params) {
  return request({
    url: "/project/bizRiskImage/list",
    method: "get",
    params
  })
}

// 查询四色图所有数据列表（分页）
export function queryFourColorRiskImagePage(params) {
  return request({
    url: "/project/bizRiskImage/page",
    method: "get",
    params
  })
}

// 新增四色图
export function addFourColorRiskImage(data) {
  return request({
    url: "/project/bizRiskImage/add",
    method: "post",
    data
  })
}

// 编辑四色图
export function editFourColorRiskImage(data) {
  return request({
    url: "/project/bizRiskImage/edit",
    method: "post",
    data
  })
}

// 删除四色图
export function deleteFourColorRiskImage(data) {
  return request({
    url: "/project/bizRiskImage/delete",
    method: "post",
    data
  })
}

// 查询单条四色图详情
export function queryFourColorRiskImageDetailById(params) {
  return request({
    url: "/project/bizRiskImage/detail",
    method: "get",
    params
  })
}

// 查询风险分级管控清单列表
export function queryRiskGradingControlList(params) {
  return request({
    url: "/project/bizRiskExternal/riskControlPage",
    method: "get",
    params
  })
}
