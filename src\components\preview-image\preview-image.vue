<!--
  * @Author: jiadongjin <EMAIL>
  * @Date: 2023-12-14 08:37:09
  * @LastEditors: jiadongjin <EMAIL>
  * @LastEditTime: 2024-05-16 11:57:53
  * @Description: 
  * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
-->
<template>
  <div class="demo-image__preview">
    <!-- 改为使用本地组件 修改了缩放阻尼 -->
    <MyImage
      v-if="fileLink || src"
      id="image"
      ref="image"
      :show-mark-box="$attrs['show-mark-box']"
      :src="fileId ? fileLink : src"
      :preview-src-list="fileId ? [fileLink] : (srcList && srcList.length > 0 ? srcList : [src]) "
      :lazy="lazy"
      v-on="$listeners"
    />
  </div>
</template>

<script>
import MyImage from './my-image'
import { getFileDetail } from '@/api/common/index'

export default {
  components: { MyImage },
  props: {
    /**
     * 文件id
     */
    fileId: {
      type: String,
      default: ''
    },

    /**
     * 图片url
     */
    src: {
      type: String,
      default: ''
    },

    /**
     * 全部图片url
     */
    srcList: {
      type: Array,
      default: () => {
        return []
      }
    },

    /**
     * 懒加载
     */
    lazy: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      fileLink: ''
    }
  },

  watch: {
    fileId: {
      handler: function(newVal, oldVal) {
        if(newVal) {
          getFileDetail(newVal).then(res => {
            this.fileLink = res.data.fileLink
          })
        }
      },

      immediate: true
    }
  },

  methods: {
  }
}
</script>