/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-07-09 09:39:21
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 16:59:13
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import {getRequest, postRequest} from '@/framework/utils/request'

// 分页查询危险品车辆路径规划所有数据 
export function getPage(query) {
  return getRequest(`/project/hazardCarArea/page`, query)
}

// 获取危险品车辆路径规划单条数据详情 
export function getDetail(query) {
  return getRequest(`/project/hazardCarArea/detail`, query)
}

// 新增危险品车辆路径规划数据
export function add(data) {
  return postRequest(`/project/hazardCarArea/add`, data)
}

// 修改危险品车辆路径规划数据
export function edit(data) {
  return postRequest(`/project/hazardCarArea/edit`, data)
}

// 删除危险品车辆路径规划数据
export function del(data) {
  return postRequest(`/project/hazardCarArea/delete`, data)
}