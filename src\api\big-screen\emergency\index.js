import { default as request, cloud } from '@/framework/utils/request'
/** 资源统计 */
export function getResourcesStatistics(params) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergencyResourcesStatistics',
    method: 'get',
    params
  })
}

/** *应急预案* */
export function getEmergencyPlan(params) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergencyPlanStatistics',
    method: 'get',
    params
  })
}
/** *预案来源分布* */
export function getPlanSources(params) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getPlanSourcesStatistics',
    method: 'get',
    params
  })
}
/** *应急专家-大屏* */
export function geExpertData(params) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergencyExpertDataStatistics',
    method: 'get',
    params
  })
}
/** *园区应急事件* */
export function getEventsLevel(params) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergEventsLevelStatistics',
    method: 'get',
    params
  })
}


/** *应急物资预警* */
export function getRescuSuppliesWarning(params) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getRescuSuppliesWarningStatistics',
    method: 'get',
    params
  })
}
/** *评估* */
export function getEmergSuppliesAssess(params) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergSuppliesAssessStatistics',
    method: 'get',
    params
  })
}
//
// /** *大屏中间事件详情* */
// export function getgetEmergEventInfo(params) {
//   return request({
//     url: cloud.dqbasic + '/emergencyResources/getEmergEventSelectOneStatistics',
//     method: 'get',
//     params
//   })
// }
/** *大屏中间事件详情* */
export function getgetEmergEventInfo(params) {
  return request({
    url: cloud.dqbasic + '/emergEventsArchiving/detail',
    method: 'get',
    params
  })
}
/** *应急专家统计-后台* */
export function getEmergencyExpert(params) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergencyExpertStatistics',
    method: 'get',
    params
  })
}

/** *企业应急物资预警统计-后台* */
export function getEmergSuppliesAffiliated(params) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergSuppliesAffiliatedStatistics',
    method: 'get',
    params
  })
}
/** **应急物资类型统计-后台 */
export function getEmergSuppliesType(params) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergSuppliesTypeStatistics',
    method: 'get',
    params
  })
}
// 【队伍清单页】分页查询应急救援某队伍清单所有数据
export function getTeamList(TeamId) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergRescuTeamListPage',
    method: 'get',
    params: {
      TeamId
    }
  })
}

// 【队伍清单页】新增单条数据
export function addTeam(data) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/addEmergRescuTeamList',
    method: 'post',
    data
  })
}

// 【队伍清单页】修改单条数据
export function editTeam(data) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/editEmergRescuTeamList',
    method: 'post',
    data
  })
}
// 【队伍清单页】删除队伍
export function deleteTeam(data) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/deleteEmergRescuTeamList',
    method: 'post',
    data
  })
}

// 【应急指挥调度中心】根据应急事件ID查询关联的应急物资数据
export function getSuppliesListByEventId(eventId) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getSuppliesListByEventId',
    method: 'get',
    params: {
      eventId
    }
  })
}

// 【应急指挥调度中心】根据应急事件ID查询关联的应急物资数据
export function getStakeholderAndTeamListByEventId(eventId) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getStakeholderAndTeamListByEventId',
    method: 'get',
    params: {
      eventId
    }
  })
}
// 【应急指挥调度中心】查询应急预案列表
export function getPlanNameListStatistics(params) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getPlanNameListStatistics',
    method: 'get',
    params
  })
}

// 【应急指挥调度中心】更换应急预案
export function updatePlanNameByEventId(data) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/updatePlanNameByEventId',
    method: 'post',
    data
  })
}

// 【应急指挥调度中心】查询应急物资列表
export function getSuppliesNameListStatistics() {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getSuppliesNameListStatistics',
    method: 'get'
  })
}
// 【应急指挥调度中心】批量新增应急事件与物资关联关系数据
export function addEmergEventsSuppliesAssoc(data) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/addEmergEventsSuppliesAssoc',
    method: 'post',
    data
  })
}

// 【应急指挥调度中心】查询从业人员列表
export function getEmployeeNameListStatistics() {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmployeeNameListStatistics',
    method: 'get'
  })
}
// 【应急指挥调度中心】查询应急专家列表
export function getExpertNameListStatistics() {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getExpertNameListStatistics',
    method: 'get'
  })
}

// 【应急指挥调度中心】批量新增应急事件与干系人关联关系数据
export function addEmergEventsStakeholderAssoc(data) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/addEmergEventsStakeholderAssoc',
    method: 'post',
    data
  })
}

// 【队伍清单页】队伍清单管理-下载模板
export function downTemplate() {
  return request({
    url: cloud.dqbasic + '/emergRescuTeamList/template',
    method: 'get',
    responseType: 'arraybuffer'
  })
}

// 【队伍清单页】队伍清单管理-导入
export function importTeamExcel(data) {
  return request({
    url: cloud.dqbasic + '/emergRescuTeamList/importExcel',
    method: 'post',
    data
  })
}

// 【三维仿真应急演练】根据id返回播放视频url
export function getVideoUrlById(id) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getVideoUrlById',
    method: 'get',
    params: {
      id
    }
  })
}

// 【敏捷应急一张图】按类型查询预案列表
export function getPlanListByOrgType(data) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getPlanListByOrgType',
    method: 'post',
    data
  })
}

// 【敏捷应急一张图】查询应急队伍技术水平统计
export function getTechnicalLevelStatistics() {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getTechnicalLevelStatistics',
    method: 'get'
  })
}

// 【敏捷应急一张图】查询技术水平相关的队伍信息
export function getTeamInfoByTechnicalLevel(data) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getTeamInfoByTechnicalLevel',
    method: 'post',
    data
  })
}

// 【敏捷应急一张图】获取应急救援队伍清单单条数据详情
export function getTeamListInfoByTeamId(data) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getTeamListInfoByTeamId',
    method: 'post',
    data
  })
}

// 【敏捷应急一张图】按id查询应急专家详细信息
export function getEmergencyExpertDataById(data) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergencyExpertDataById',
    method: 'post',
    data
  })
}

// 【敏捷应急一张图】查询资源统计（应急物资）
export function getEmergencyResourcesSuppliesStatistics() {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergencyResourcesSuppliesStatistics',
    method: 'get'
  })
}

// 【敏捷应急一张图】查询资源统计（应急专家）
export function getEmergencyResourcesExperStatistics() {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergencyResourcesExperStatistics',
    method: 'get'
  })
}

// 【敏捷应急一张图】查询资源统计（应急队伍）
export function getEmergencyResourcesTeamStatistics() {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergencyResourcesTeamStatistics',
    method: 'get'
  })
}

// 【敏捷应急一张图】查询资源统计（应急预案）
export function getEmergencyResourcesPlanStatistics() {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergencyResourcesPlanStatistics',
    method: 'get'
  })
}

// 【敏捷应急一张图】查询资源统计（应急仓库）
export function getEmergencyResourcesStoreStatistics() {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergencyResourcesStoreStatistics',
    method: 'get'
  })
}

// 【敏捷应急一张图】查询资源统计（应急避难所）
export function getEmergencyResourcesShelterStatistics() {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergencyResourcesShelterStatistics',
    method: 'get'
  })
}

// 【敏捷应急一张图】查询应急物资信息（数量正常）
export function getSuppliesNormal() {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getSuppliesNormal',
    method: 'get'
  })
}

// 【敏捷应急一张图】查询应急物资信息（数量不足）
export function getSuppliesInsufficient() {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getSuppliesInsufficient',
    method: 'get'
  })
}

// 【敏捷应急一张图】查询应急物资信息（过期）
export function getSuppliesOverdue() {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getSuppliesOverdue',
    method: 'get'
  })
}
// 【敏捷应急一张图】敏捷应急大屏几类数据的名称
export function getNameLimitSix() {
  return request({
    url: '/project/emergencyResources/getNameLimitSix',
    method: 'get'
  })
}
