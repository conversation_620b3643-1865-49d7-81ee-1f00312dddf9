import request from '@/framework/utils/request'

// 查询应用二次认证列表
export function listApp(query) {
  return request({
    url: '/appSecondaryAuth/page',
    method: 'get',
    params: query
  })
}

// 查询应用二次认证详细
export function getApp(appId) {
  return request({
    url: `/appSecondaryAuth/detail?appId=${appId}`,
    method: 'get'
  })
}

// 新增应用二次认证
export function addApp(data) {
  return request({
    url: '/appSecondaryAuth/add',
    method: 'post',
    data
  })
}

// 修改应用二次认证
export function updateApp(data) {
  return request({
    url: '/appSecondaryAuth/edit',
    method: 'post',
    data
  })
}

// 删除应用二次认证
export function delApp(appId) {
  return request({
    url: '/appSecondaryAuth/delete',
    method: 'post',
    data: { appId }
  })
}
