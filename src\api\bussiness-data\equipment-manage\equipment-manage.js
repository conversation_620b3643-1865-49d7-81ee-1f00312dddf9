import request from '@/framework/utils/request'

// 查询设备设施维护列表
export function bizEquipmentPage(query) {
  return request({
    url: '/project/bizEquipment/page',
    method: 'get',
    params: query
  })
}

// 查询设备设施维护详细
export function bizEquipmentDetail(params) {
  return request({
    url: '/project/bizEquipment/detail',
    method: 'get',
    params
  })
}

// 新增设备设施维护
export function bizEquipmentAdd(data) {
  return request({
    url: '/project/bizEquipment/add',
    method: 'post',
    data: data
  })
}

// 修改设备设施维护
export function bizEquipmentEdit(data) {
  return request({
    url: '/project/bizEquipment/edit',
    method: 'post',
    data: data
  })
}

// 删除设备设施维护
export function bizEquipmentDel(equipmentId) {
  return request({
    url: '/project/bizEquipment/delete',
    method: 'post',
    data: { equipmentIds: equipmentId }
  })
}
