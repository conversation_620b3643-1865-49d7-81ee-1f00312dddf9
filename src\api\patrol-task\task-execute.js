/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-20 16:43:48
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-05-29 18:55:54
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud}  from '@/framework/utils/request'

// 分页查询业务
export function getList(query) {
  return request({
    url: cloud.dqbasic + '/bizInspectionTask/page',
    method: 'get',
    params: query
  })
}

// 巡检任务点击执行之前的准备
export function beforeExecute(query) {
  return request({
    url: cloud.dqbasic + '/bizInspectionTask/beforeExecute',
    method: 'get',
    params: query
  })
}

// 获取业务-巡检任务单条数据详情
export function getDetail(query) {
  return request({
    url: cloud.dqbasic + '/bizInspectionTask/detail',
    method: 'get',
    params: query
  })
}

// 任务查看执行情况的列表
export function getTaskFrequencyList(query) {
  return request({
    url: cloud.dqbasic + '/bizInspectionTaskFrequency/list',
    method: 'get',
    params: query
  })
}

// 新增业务-巡检任务检查数据
export function addCheck(data) {
  return request({
    url: cloud.dqbasic + '/bizInspectionTaskCheck/add',
    method: 'post',
    data: data
  })
}

// 修改业务-巡检任务检查数据
export function editCheck(data) {
  return request({
    url: cloud.dqbasic + '/bizInspectionTaskCheck/edit',
    method: 'post',
    data: data
  })
}

// 修改业务-巡检任务完成
export function edit(data) {
  return request({
    url: cloud.dqbasic + '/bizInspectionTask/edit',
    method: 'post',
    data: data
  })
}
