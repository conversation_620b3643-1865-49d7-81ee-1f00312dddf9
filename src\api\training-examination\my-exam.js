import request, { cloud } from '@/framework/utils/request'

// 我的考试查询
export function getMyExamVOList(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamUserPaper/getMyExamVOList`,
    method: 'get',
    params: query
  })
}

// 考试结果查询
export function getTrainingExamUserPaperPage(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamUserPaper/page`,
    method: 'get',
    params: query
  })
}

// 答题详情查询
export function getExamPaperQuestionPage(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamUserPaper/paperQuestionPage`,
    method: 'get',
    params: query
  })
}

// 考试-开始答题
export function trainingExamUserPaperAdd(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamUserPaper/add`,
    method: 'get',
    params: query
  })
}

// 考试-交卷
export function commitExamPaperAndScore(data) {
  return request({
    url: `${cloud.dqbasic}/trainingExamUserPaper/commitPaperAndScore`,
    method: 'post',
    data
  })
}

// 用户答题缓存
export function userAnswerCache(data) {
  return request({
    url: `${cloud.dqbasic}/trainingExamUserPaper/userAnswerCache`,
    method: 'post',
    data
  })
}

// 试卷提交状态查询
export function getIsSubmit(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamUserPaper/getIsSubmit`,
    method: 'get',
    params: query
  })
}

