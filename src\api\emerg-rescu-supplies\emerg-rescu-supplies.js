/*
 * @Author: zzp
 * @Date: 2024-04-22 15:53:40
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-23 18:19:41
 * @FilePath: \isrmp_vue\src\api\HealthExaminePlan\health-examine-plan.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud} from '@/framework/utils/request'

// 查询实战应急演练列表
export function emergRescuSuppliesGetPage(query) {
  return request({
    url: cloud.dqbasic + '/emergRescuSupplies/getListPage',
    method: 'get',
    params: query
  })
}

// 查询实战应急演练详细
export function getEmergRescuSupplies(id) {
  return request({
    url: cloud.dqbasic + '/emergRescuSupplies/detail?id=' + id,
    method: 'get'
  })
}

// 新增实战应急演练数据
export function addEmergRescuSupplies(data) {
  return request({
    url: cloud.dqbasic + '/emergRescuSupplies/add',
    method: 'post',
    data: data
  })
}
// 获取应急救援物资检验记录单条数据详情
export function getEmergRescuSupplieDetail(params) {
  return request({
    url: '/project/emergRescuSuppliesInspection/detail',
    method: 'get',
    params:params
  })
}
// 分页查询应急救援物资检验记录所有数据
export function getEmergRescuSuppliePage(params) {
  return request({
    url: '/project/emergRescuSuppliesInspection/page',
    method: 'get',
    params:params
  })
}
// 新增应急救援物资检验记录数据
export function addEmergRescuSuppliesInspection(data) {
  return request({
    url: '/project/emergRescuSuppliesInspection/add',
    method: 'post',
    data: data
  })
}
// 修改应急救援物资检验记录数据
export function editEmergRescuSuppliesInspection(data) {
  return request({
    url: '/project/emergRescuSuppliesInspection/edit',
    method: 'post',
    data: data
  })
}

// 修改实战应急演练数据
export function updateEmergRescuSupplies(data) {
  return request({
    url: cloud.dqbasic + '/emergRescuSupplies/edit',
    method: 'post',
    data: data
  })
}

// 删除职业健康体检计划
export function delEmergRescuSupplies(id) {
  return request({
    url: cloud.dqbasic + '/emergRescuSupplies/delete',
    method: 'post',
    data: { ids: id }
  })
}
