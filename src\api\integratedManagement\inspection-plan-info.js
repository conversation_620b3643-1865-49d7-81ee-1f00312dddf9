import request, {cloud} from '@/framework/utils/request'

// 查询执法检查计划列表
export function listInspectionPlanInfo(query) {
  return request({
    url: cloud.dqbasic + '/inspectionPlanInfo/page',
    method: 'get',
    params: query
  })
}

// 查询执法检查计划详细
export function getInspectionPlanInfo(id) {
  return request({
    url: cloud.dqbasic + '/inspectionPlanInfo/detail?id=' + id,
    method: 'get'
  })
}

// 新增执法检查计划
export function addInspectionPlanInfo(data) {
  return request({
    url: cloud.dqbasic + '/inspectionPlanInfo/add',
    method: 'post',
    data: data
  })
}

// 修改执法检查计划
export function updateInspectionPlanInfo(data) {
  return request({
    url: cloud.dqbasic + '/inspectionPlanInfo/edit',
    method: 'post',
    data: data
  })
}

// 删除执法检查计划
export function delInspectionPlanInfo(id) {
  return request({
    url: cloud.dqbasic + '/inspectionPlanInfo/delete',
    method: 'post',
    data: { ids: id }
  })
}
