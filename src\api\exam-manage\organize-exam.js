import request, { cloud } from '@/framework/utils/request'

// 考试查询
export function getTrainingExamPlanPage(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/page`,
    method: 'get',
    params: query
  })
}

// 考试详情
export function getTrainingExamPlanDetail(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/detail`,
    method: 'get',
    params: query
  })
}

// 考试新增
export function trainingExamPlanAdd(data) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/add`,
    method: 'post',
    data
  })
}

// 考试修改
export function trainingExamPlanEdit(data) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/edit`,
    method: 'post',
    data
  })
}

// 考试删除
export function trainingExamPlanDelete(data) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/delete`,
    method: 'post',
    data
  })
}

// 考试人员导入模板下载
export function trainingBaseInfoTemplate(query) {
  return request({
    url: `${cloud.dqbasic}/trainingBaseInfo/downloadTemplateFile`,
    method: 'get',
    params: query,
    responseType: 'arraybuffer'
  })
}

// 考试人员导入
export function trainingBaseInfoImport(data) {
  return request({
    url:  `${cloud.dqbasic}/trainingBaseInfo/importTemplateFile`,
    method: 'post',
    data
  })
}

// 考试人员预览
export function getTrainingExamUsersPreview(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/getUsersPreview`,
    method: 'get',
    params: query
  })
}

// 结束考试
export function trainingExamPlanEnd(data) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/endExam`,
    method: 'post',
    data
  })
}

// 线下考试结果导入模板下载
export function trainingExamPlanTemplate(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/downloadTemplateFile`,
    method: 'get',
    params: query,
    responseType: 'arraybuffer'
  })
}

// 线下考试结果导入
export function uploadOfflineResult(data) {
  return request({
    url:  `${cloud.dqbasic}/trainingExamPlan/uploadOfflineResult`,
    method: 'post',
    data
  })
}

// 线下考试结果查询
export function selectExamOfflineResult(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/selectExamOfflineResult`,
    method: 'get',
    params: query
  })
}

// 线上考试结果查询
export function getExamOnlineResult(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/getExamResult`,
    method: 'get',
    params: query
  })
}

// 线上考试结果答题详情
export function getPaperQuestionPage(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/paperQuestionPage`,
    method: 'get',
    params: query
  })
}

// 线上考试结果导出
export function exportExamOnlineResult(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/exportExamResult`,
    method: 'get',
    params: query,
    responseType: 'arraybuffer'
  })
}

// 统计表
export function getExamOrgStatistics(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/getExamOrgStatistics`,
    method: 'get',
    params: query
  })
}

// 统计表导出
export function exportExamOrgStatistics(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/exportExamOrgStatistics`,
    method: 'get',
    params: query,
    responseType: 'arraybuffer'
  })
}
