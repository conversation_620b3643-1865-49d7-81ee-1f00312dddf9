/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-30 14:54:32
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-30 16:09:27
 * @Description: 危化品管理
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */

import { default as request, cloud } from '@/framework/utils/request'
// 查询危险化工工艺列表
export function listChemicalProcesses(query) {
  return request({
    url: cloud.dqbasic + '/hazardousChemicalProcesses/page',
    method: 'get',
    params: query
  })
}

// 查询危险化工工艺详细
export function getChemicalProcesses(id) {
  return request({
    url:  cloud.dqbasic +'/hazardousChemicalProcesses/detail?id=' + id,
    method: 'get'
  })
}

// 新增危险化工工艺
export function addChemicalProcesses(data) {
  return request({
    url:  cloud.dqbasic +'/hazardousChemicalProcesses/add',
    method: 'post',
    data: data
  })
}

// 修改危险化工工艺
export function updateChemicalProcesses(data) {
  return request({
    url:  cloud.dqbasic +'/hazardousChemicalProcesses/edit',
    method: 'post',
    data: data
  })
}

// 删除危险化工工艺
export function delChemicalProcesses(id) {
  return request({
    url:  cloud.dqbasic +'/hazardousChemicalProcesses/delete',
    method: 'post',
    data: { ids: id }
  })
}
