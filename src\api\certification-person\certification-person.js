/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-07-01 08:59:03
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 17:54:19
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud}  from '@/framework/utils/request'
import {getRequest, postRequest} from '@/framework/utils/request'

// 查询列表
export function getList(query) {
  return getRequest(cloud.dqbasic + '/enterprisePersionCertificates/page', query)
}

// 查询详细
export function getDetail(id) {
  return getRequest(cloud.dqbasic + '/enterprisePersionCertificates/detail?id=' + id)
}

// 新增
export function add(data) {
  return postRequest(cloud.dqbasic + '/enterprisePersionCertificates/add', data)
}

// 修改
export function edit(data) {
  return postRequest(cloud.dqbasic + '/enterprisePersionCertificates/edit', data)
}

// 删除
export function del(id) {
  return postRequest(cloud.dqbasic + '/enterprisePersionCertificates/delete', { ids: id })
}

// 下载模板
export function downTemplate() {
  return request({
    url: cloud.dqbasic + '/enterprisePersionCertificates/exportEnterprisePersionCertificatesTemplate',
    method: 'get',
    responseType: 'arraybuffer'
  })
}

// 导入
export function importTeamExcel(data) {
  return postRequest(cloud.dqbasic + '/enterprisePersionCertificates/importExcel', data)
}

// 导出
export function exportExcel(query) {
  return request({
    url: cloud.dqbasic + '/enterprisePersionCertificates/exportEnterprisePersionCertificates',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 查询公司下部门/人员
export function getRelateDeptByCompany(query) {
  return getRequest(cloud.dqbasic + '/relatePerson/getRelatePersonList', query)
}

// 查询公司下所有人员
export function getUserByOrg(query) {
  return getRequest(cloud.dqbasic + '/enterprisePersionCertificates/getUserByOrg', query)
}