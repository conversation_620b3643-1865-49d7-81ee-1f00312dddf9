/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-07-26 09:47:16
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 17:17:18
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud} from '@/framework/utils/request'
import {getRequest, postRequest} from '@/framework/utils/request'

// 查询园区安全管理架构列表
export function getList(query) {
  return getRequest(cloud.dqbasic + '/industrialParkSecurityAdminOrg/page', query)
}

// 查询园区安全管理架构详细
export function getDetail(id) {
  return getRequest(cloud.dqbasic + '/industrialParkSecurityAdminOrg/detail?id=' + id)
}

// 新增园区安全管理架构
export function add(data) {
  return postRequest(cloud.dqbasic + '/industrialParkSecurityAdminOrg/add', data)
}

// 修改园区安全管理架构
export function edit(data) {
  return postRequest(cloud.dqbasic + '/industrialParkSecurityAdminOrg/edit', data)
}

// 删除园区安全管理架构
export function del(id) {
  return postRequest(cloud.dqbasic + '/industrialParkSecurityAdminOrg/delete', { ids: id })
}
