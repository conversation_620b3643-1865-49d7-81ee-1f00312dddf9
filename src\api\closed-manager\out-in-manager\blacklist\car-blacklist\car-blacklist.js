/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-07-12 08:31:14
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 17:08:56
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import {getRequest, postRequest} from '@/framework/utils/request'

// 查询车辆黑名单管理列表
export function listBlacklist(query) {
  return getRequest('/project/carBlacklist/page', query)
}

// 查询车辆黑名单管理详细
export function getBlacklist(id) {
  return getRequest('/project/carBlacklist/detail?id=' + id)
}

// 新增车辆黑名单管理
export function addBlacklist(data) {
  return postRequest('/project/carBlacklist/add', data)
}

// 修改车辆黑名单管理
export function updateBlacklist(data) {
  return postRequest('/project/carBlacklist/edit', data)
}


export function cancelBlacklist(id) {
  return postRequest('/project/carBlacklist/cancel', { ids: id })
}
