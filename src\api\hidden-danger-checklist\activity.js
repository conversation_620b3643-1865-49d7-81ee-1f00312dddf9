/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-23 10:53:33
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-05-23 11:07:04
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */

import request, {cloud}  from '@/framework/utils/request'

// 查询列表
export function getList(query) {
  return request({
    url: cloud.dqbasic + '/bizRiskExternal/activityMeasurePage',
    method: 'get',
    params: query
  })
}
