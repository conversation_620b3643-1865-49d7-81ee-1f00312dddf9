/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-30 11:44:28
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-30 14:40:12
 * @Description: 危险化学品管理
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */

import { default as request, cloud } from '@/framework/utils/request'
// 查询危险化学品管理列表
export function listChemicalProcesses(query) {
  return request({
    url: cloud.dqbasic +'/hazardousChemicalManage/page',
    method: 'get',
    params: query
  })
}

// 查询危险化学品管理详细
export function getChemicalProcesses(id) {
  return request({
    url:cloud.dqbasic + '/hazardousChemicalManage/detail?id=' + id,
    method: 'get'
  })
}

// 新增危险化学品管理
export function addChemicalProcesses(data) {
  return request({
    url: cloud.dqbasic +'/hazardousChemicalManage/add',
    method: 'post',
    data: data
  })
}

// 修改危险化学品管理
export function updateChemicalProcesses(data) {
  return request({
    url: cloud.dqbasic +'/hazardousChemicalManage/edit',
    method: 'post',
    data: data
  })
}

// 删除危险化学品管理
export function delChemicalProcesses(id) {
  return request({
    url: cloud.dqbasic +'/hazardousChemicalManage/delete',
    method: 'post',
    data: { ids: id }
  })
}
