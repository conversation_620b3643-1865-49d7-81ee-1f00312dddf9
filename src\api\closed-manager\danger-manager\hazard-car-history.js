/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-07-08 15:37:48
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 17:01:58
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import {getRequest, postRequest} from '@/framework/utils/request'

// 分页查询危险品车辆历史轨迹所有数据 
export function getPage(query) {
  return getRequest(`/project/hazardCarHistory/page`, query)
}

// 获取危险品车辆历史轨迹单条数据详情 
export function getDetail(query) {
  return getRequest(`/project/hazardCarHistory/detail`, query)
}

// 新增危险品车辆历史轨迹数据
export function add(data) {
  return postRequest(`/project/hazardCarHistory/add`, data)
}

// 修改危险品车辆历史轨迹数据
export function edit(data) {
  return postRequest(`/project/hazardCarHistory/edit`, data)
}

// 删除危险品车辆历史轨迹数据
export function del(data) {
  return postRequest(`/project/hazardCarHistory/delete`, data)
}