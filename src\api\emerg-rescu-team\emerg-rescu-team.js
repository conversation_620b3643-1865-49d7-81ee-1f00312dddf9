/*
 * @Author: zzp
 * @Date: 2024-04-22 15:53:40
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-05-28 13:52:52
 * @FilePath: \isrmp_vue\src\api\emerg-rescu-team\emerg-rescu-team.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud} from '@/framework/utils/request'

// 查询实战应急演练列表
export function emergRescuTeamGetPage(query) {
  return request({
    url: cloud.dqbasic + '/emergRescuTeam/getListPage',
    method: 'get',
    params: query
  })
}

// 查询实战应急演练详细
export function getEmergRescuTeam(id) {
  return request({
    url: cloud.dqbasic + '/emergRescuTeam/detail?id=' + id,
    method: 'get'
  })
}

// 新增实战应急演练数据
export function addEmergRescuTeam(data) {
  return request({
    url: cloud.dqbasic + '/emergRescuTeam/add',
    method: 'post',
    data: data
  })
}

// 修改实战应急演练数据
export function updateEmergRescuTeam(data) {
  return request({
    url: cloud.dqbasic + '/emergRescuTeam/edit',
    method: 'post',
    data: data
  })
}

// 删除职业健康体检计划
export function delEmergRescuTeam(id) {
  return request({
    url: cloud.dqbasic + '/emergRescuTeam/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 【队伍清单页】分页查询应急救援某队伍清单所有数据
export function getTeamList(TeamId) {
  return request({
    url: cloud.dqbasic + '/emergRescuTeamList/page',
    method: 'get',
    params: {
      TeamId
    }
  })
}

// 【队伍清单页】新增单条数据
export function addTeam(data) {
  return request({
    url: cloud.dqbasic + '/emergRescuTeamList/add',
    method: 'post',
    data
  })
}

// 【队伍清单页】修改单条数据
export function editTeam(data) {
  return request({
    url: cloud.dqbasic + '/emergRescuTeamList/edit',
    method: 'post',
    data
  })
}
// 【队伍清单页】删除队伍
export function deleteTeam(data) {
  return request({
    url: cloud.dqbasic + '/emergRescuTeamList/delete',
    method: 'post',
    data
  })
}
// 【队伍清单页】获取队伍清单单条数据详情
export function getTeamDetail(TeamId) {
  return request({
    url: cloud.dqbasic + '/emergRescuTeamList/detail',
    method: 'get',
    params: {
      TeamId
    }
  })
}
// 【队伍清单页】队伍清单管理-下载模板
export function downTemplate() {
  return request({
    url: cloud.dqbasic + '/emergRescuTeamList/template',
    method: 'get',
    responseType: 'arraybuffer'
  })
}

// 【队伍清单页】队伍清单管理-导入
export function importTeamExcel(data) {
  return request({
    url: cloud.dqbasic + '/emergRescuTeamList/importExcel',
    method: 'post',
    data
  })
}
