import request, { cloud } from '@/framework/utils/request'

// 我的错题查询
export function getTrainingMyErrorQuestionPage(query) {
  return request({
    url: `${cloud.dqbasic}/trainingMyErrorQuestion/page`,
    method: 'get',
    params: query
  })
}

// 我的错题删除
export function trainingMyErrorQuestionDelete(data) {
  return request({
    url: `${cloud.dqbasic}/trainingMyErrorQuestion/delete`,
    method: 'post',
    data
  })
}
