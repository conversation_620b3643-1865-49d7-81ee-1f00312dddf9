import request, { cloud } from '@/framework/utils/request'

// 试卷查询
export function getTrainingExamMainPaperPage(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamMainPaper/page`,
    method: 'get',
    params: query
  })
}

// 试卷详情
export function getTrainingExamMainPaperDetail(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamMainPaper/detail`,
    method: 'get',
    params: query
  })
}

// 试卷新增
export function trainingExamMainPaperAdd(data) {
  return request({
    url: `${cloud.dqbasic}/trainingExamMainPaper/add`,
    method: 'post',
    data
  })
}

// 判断试卷是否可编辑
export function trainingExamMainPaperEditCheck(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamMainPaper/editCheck`,
    method: 'get',
    params: query
  })
}

// 试卷修改
export function trainingExamMainPaperEdit(data) {
  return request({
    url: `${cloud.dqbasic}/trainingExamMainPaper/edit`,
    method: 'post',
    data
  })
}

// 试卷删除
export function trainingExamMainPaperDelete(data) {
  return request({
    url: `${cloud.dqbasic}/trainingExamMainPaper/delete`,
    method: 'post',
    data
  })
}

// 获取系统选题
export function getRandomExamPaperQuestionList(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamMainPaper/getRandomExamPaperQuestionList`,
    method: 'get',
    params: query
  })
}
