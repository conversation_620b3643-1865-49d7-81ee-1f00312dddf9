/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-07-26 09:46:16
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 17:06:59
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud} from '@/framework/utils/request'
import {getRequest, postRequest} from '@/framework/utils/request'

// 查询园区简介列表
export function getList(query) {
  return getRequest(cloud.dqbasic + '/industrialParkInfo/page', query)
}

// 查询园区简介详细
export function getDetail(id) {
  return getRequest(cloud.dqbasic + '/industrialParkInfo/detail?id=' + id)
}

// 新增园区简介
export function add(data) {
  return postRequest(cloud.dqbasic + '/industrialParkInfo/add', data)
}

// 修改园区简介
export function edit(data) {
  return postRequest(cloud.dqbasic + '/industrialParkInfo/edit', data)
}

// 删除园区简介
export function del(id) {
  return postRequest(cloud.dqbasic + '/industrialParkInfo/delete', { ids: id })
}
