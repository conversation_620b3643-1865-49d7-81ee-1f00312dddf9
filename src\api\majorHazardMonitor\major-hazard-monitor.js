/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-14 14:51:15
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 17:37:53
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud}  from '@/framework/utils/request'
import {getRequest, postRequest} from '@/framework/utils/request'

// 查询健康数据在线监测列表
export function listhazardDataMonitor(query) {
  return getRequest(cloud.dqbasic + '/hazardDataMonitor/page', query)
}

// 查询健康数据在线监测详细
export function getHealthDataMonitor(id) {
  return getRequest(cloud.dqbasic + '/healthDataMonitor/detail?id=' + id)
}

// 新增健康数据在线监测
export function addHealthDataMonitor(data) {
  return postRequest(cloud.dqbasic + '/healthDataMonitor/add', data)
}

// 修改健康数据在线监测
export function updateHealthDataMonitor(data) {
  return postRequest(cloud.dqbasic + '/healthDataMonitor/edit', data)
}

// 删除健康数据在线监测
export function delHealthDataMonitor(id) {
  return postRequest(cloud.dqbasic + '/healthDataMonitor/delete', { ids: id })
}
