/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-07-01 08:59:03
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-07-03 08:44:01
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud}  from '@/framework/utils/request'

// 查询列表
export function getList(query) {
  return request({
    url: cloud.dqbasic + '/lawsRegulations/page',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getDetail(id) {
  return request({
    url: cloud.dqbasic + '/lawsRegulations/detail?id=' + id,
    method: 'get'
  })
}

// 新增
export function add(data) {
  return request({
    url: cloud.dqbasic + '/lawsRegulations/add',
    method: 'post',
    data: data
  })
}

// 修改
export function edit(data) {
  return request({
    url: cloud.dqbasic + '/lawsRegulations/edit',
    method: 'post',
    data: data
  })
}

// 删除
export function del(id) {
  return request({
    url: cloud.dqbasic + '/lawsRegulations/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 新增法律法规评估记录数据
export function addEvaluate(data) {
  return request({
    url: cloud.dqbasic + '/lawsRegulationsEvaluate/add',
    method: 'post',
    data: data
  })
}

// 分页查询法律法规评估记录所有数据
export function getEvaluateList(query) {
  return request({
    url: cloud.dqbasic + '/lawsRegulationsEvaluate/page',
    method: 'get',
    params: query
  })
}