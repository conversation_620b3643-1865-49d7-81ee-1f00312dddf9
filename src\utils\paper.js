export function getTotalScore(paper) {
  const judgeCount = parseInt(paper.judgeCount)
  const multipleChoiceCount = parseInt(paper.multipleChoiceCount)
  const singleChoiceCount = parseInt(paper.singleChoiceCount)
  const judgeScore = parseFloat(paper.judgeScore)
  const multipleChoiceScore = parseFloat(paper.multipleChoiceScore)
  const singleChoiceScore = parseFloat(paper.singleChoiceScore)

  const arr = [
    [judgeCount, judgeScore],
    [multipleChoiceCount, multipleChoiceScore],
    [singleChoiceCount, singleChoiceScore]
  ]

  // 如果score或count为NaN，则返回0
  return arr.reduce((totalScore, [score, count]) => {
    if (isNaN(score) || isNaN(count)) {
      return totalScore + 0
    } else {
      return totalScore + score * count
    }
  }, 0)
}

export function getTotalQuestion(paper) {
  const judgeCount = parseInt(paper.judgeCount)
  const multipleChoiceCount = parseInt(paper.multipleChoiceCount)
  const singleChoiceCount = parseInt(paper.singleChoiceCount)

  const arr = [judgeCount, multipleChoiceCount, singleChoiceCount]

  return arr.reduce((totalQuestion, count) => {
    if (isNaN(count)) {
      return totalQuestion + 0
    } else {
      return totalQuestion + count
    }
  }, 0)
}

export function getFormattedUseTime(totalSeconds) {
  if (totalSeconds === 0) return '0秒'

  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60

  // 根据存在的单位类型构建结果
  if (hours > 0) {
    if (minutes > 0) {
      return seconds > 0
        ? `${hours}时${minutes}分${seconds}秒`
        : `${hours}时${minutes}分`
    } else {
      return seconds > 0
        ? `${hours}时${seconds}秒`
        : `${hours}时`
    }
  }

  if (minutes > 0) {
    return seconds > 0
      ? `${minutes}分${seconds}秒`
      : `${minutes}分`
  }

  return `${seconds}秒`
}
