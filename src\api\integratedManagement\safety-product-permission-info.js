import request, {cloud} from '@/framework/utils/request'

// 查询安全生产许可列表
export function listSafetyProductPermissionInfo(query) {
  return request({
    url: cloud.dqbasic + '/safetyProductPermissionInfo/page',
    method: 'get',
    params: query
  })
}

// 查询安全生产许可详细
export function getSafetyProductPermissionInfo(id) {
  return request({
    url: cloud.dqbasic + '/safetyProductPermissionInfo/detail?id=' + id,
    method: 'get'
  })
}

// 新增安全生产许可
export function addSafetyProductPermissionInfo(data) {
  return request({
    url: cloud.dqbasic + '/safetyProductPermissionInfo/add',
    method: 'post',
    data: data
  })
}

// 修改安全生产许可
export function updateSafetyProductPermissionInfo(data) {
  return request({
    url: cloud.dqbasic + '/safetyProductPermissionInfo/edit',
    method: 'post',
    data: data
  })
}

// 删除安全生产许可
export function delSafetyProductPermissionInfo(id) {
  return request({
    url: cloud.dqbasic + '/safetyProductPermissionInfo/delete',
    method: 'post',
    data: { ids: id }
  })
}
