/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-17 15:30:17
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-17 15:34:47
 * @Description: 安全文化
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'
// 分页查询查看考试所有数据
export function getSafetyCultureList(query) {
  return request({
    url: '/project/safetyCulture/page',
    method: 'get',
    params: query
  })
}

// 获取安全文化单条数据详情
export function getCultureDetailInfo(query) {
  return request({
    url: '/project/safetyCulture/detail',
    method: 'get',
    params: query
  })
}

// 新增安全文化数据
export function addCultureInfo(data) {
  return request({
    url: '/project/safetyCulture/add',
    method: 'post',
    data: data
  })
}
// 浏览量请求数量加一
export function addNum(data) {
  return request({
    url: '/project/safetyCulture/addNum',
    method: 'get',
    params: query
  })
}
// 删除安全文化数据
export function deleteCultureInfo(data) {
  return request({
    url: '/project/safetyCulture/delete',
    method: 'post',
    data: data
  })
}
// 修改安全文化数据
export function editCultureInfo(data) {
  return request({
    url: '/project/safetyCulture/edit',
    method: 'post',
    data: data
  })
}