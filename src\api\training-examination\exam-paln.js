/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-11 10:53:57
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-12 16:20:21
 * @Description: =考试计划
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'
// 分页查询考试计划所有数据
export function getExamPlanInfoList(query) {
  return request({
    url: '/project/trainingExamPlan/page',
    method: 'get',
    params: query
  })
}
  
// 新增考试计划数据
export function addExamPlanInfo(data) {
  return request({
    url: '/project/trainingExamPlan/add',
    method: 'post',
    data: data
  })
}
// 删除考试计划数据
export function deleteExamPlanInfo(data) {
  return request({
    url: '/project/trainingExamPlan/delete',
    method: 'post',
    data: data
  })
}
// 获取考试计划单条数据详情
export function getExamPlanInfoDetail(query) {
  return request({
    url: '/project/trainingExamPlan/detail',
    method: 'get',
    params: query
  })
}
// 修改考试计划数据
export function editExamPlanInfo(data) {
  return request({
    url: '/project/trainingExamPlan/edit',
    method: 'post',
    data: data
  })
}

// 启用-停用操作
export function updateStatus(data) {
  return request({
    url: '/project/trainingExamPlan/updateStatus',
    method: 'post',
    data: data
  })
}
// 根据题目类型查询该题目类型总数
export function countQuestionByType(query) {
  return request({
    url: '/project/trainingQuestion/countQuestionByType',
    method: 'get',
    params: query
  })
}
