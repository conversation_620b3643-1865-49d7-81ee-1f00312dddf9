import {
  default as request,
  cloud
} from '@/framework/utils/request'

export default ({
  // 查询管理员组树
  getGroupTree() {
    return request({
      url: `${cloud.process}/flow/flowCategory/list`,
      method: 'get'
    })
  },
  // 新增管理员组
  add(data) {
    return request({
      url: `${cloud.process}/flow/flowCategory/add`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    })
  },
  // 修改管理员组
  edit(data) {
    return request({
      url: `${cloud.process}/flow/flowCategory/update`,
      method: 'POST',
      data,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    })
  },
  // 删除管理员组
  delGroup(data) {
    return request({
      url: `${cloud.process}/flow/flowCategory/delete`,
      method: 'post',
      data
    })
  },
  // 查询某个管理员组下的人员列表
  getManagerUserPage(params) {
    return request({
      url: `${cloud.process}/managerGroup/getManagerUserPage`,
      method: 'get',
      params
    })
  },
  // 批量移除管理员组中的用户
  delMember(data) {
    return request({
      url: `${cloud.process}/managerGroup/batchRemoveUsers`,
      method: 'post',
      data
    })
  },
  // 查询当前被授权的管理员组已经绑定的可授权范围和管理范围
  getCurrentGroupDataScope(params) {
    return request({
      url: `${cloud.permission}/managerGroup/getCurrentGroupDataScope`,
      method: 'get',
      params
    })
  },
  // 查询当前被授权的管理员组上一级的可授权范围和管理范围
  getParentGroupDataScope(params) {
    return request({
      url: `${cloud.permission}/managerGroup/getParentGroupDataScope`,
      method: 'get',
      params
    })
  },
  // 查询当前被授权的管理员组父级可授权功能和管理功能
  getParentGroupFunction(params) {
    return request({
      url: `${cloud.permission}/managerGroup/getParentGroupFunction`,
      method: 'get',
      params
    })
  },
  // 查询当前被授权的管理员组已绑定的可授权功能和管理功能
  getCurrentGroupFunction(params) {
    return request({
      url: `${cloud.process}/managerGroup/getCurrentGroupFunction`,
      method: 'get',
      params
    })
  },
  // 保存管理员组用户
  saveManagerUser(data) {
    return request({
      url: `${cloud.process}/managerGroup/saveManagerUser`,
      method: 'post',
      data
    })
  },
  // 保存管理员组权限
  saveGroupAuthority(data) {
    return request({
      url: `${cloud.process}/managerGroup/saveGroupAuthority`,
      method: 'post',
      data
    })
  },
  // 据用户ID查询用户详情
  getUserDetail(params) {
    return request({
      url: `${cloud.process}/managerGroup/getUserDetail`,
      method: 'get',
      params
    })
  }
})

