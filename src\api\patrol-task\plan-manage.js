/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-20 16:43:48
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-05-23 15:33:34
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud}  from '@/framework/utils/request'

// 新增业务-巡检计划数据
export function add(data) {
  return request({
    url: cloud.dqbasic + '/bizInspectionPlan/add',
    method: 'post',
    data: data
  })
}

// 删除业务-巡检计划数据
export function del(id) {
  return request({
    url: cloud.dqbasic + '/bizInspectionPlan/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 获取业务-巡检计划单条数据详情
export function getDetail(id) {
  return request({
    url: cloud.dqbasic + '/bizInspectionPlan/detail?id=' + id,
    method: 'get'
  })
}

// 修改业务-巡检计划数据
export function edit(data) {
  return request({
    url: cloud.dqbasic + '/bizInspectionPlan/edit',
    method: 'post',
    data: data
  })
}

// 计划周期刷新判断
export function judgeTimeCollect(data) {
  return request({
    url: cloud.dqbasic + '/bizInspectionTask/judgeTimeCollect',
    method: 'post',
    data: data
  })
}

// 分页查询业务-巡检计划所有数据
export function getList(query) {
  return request({
    url: cloud.dqbasic + '/bizInspectionPlan/page',
    method: 'get',
    params: query
  })
}
