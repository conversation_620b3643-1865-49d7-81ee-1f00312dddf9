<template>
  <el-dialog
    title="答题详情"
    :visible.sync="dialogVisible"
    width="60%"
    :before-close="handleClose"
    top="5vh"
  >
    <div class="total">
      答题结果：总计 {{ total }} 道试题，得分 {{ highestScore }} 分
    </div>

    <el-table
      v-loading="loading"
      style="width: 100%;"
      border
      highlight-current-row
      :header-cell-style="{ backgroundColor: '#f2f2f2' }"
      :data="tableData"
    >
      <template slot="empty">
        <p>{{ $store.getters.dataText }}</p>
      </template>

      <el-table-column
        label="题目"
        show-overflow-tooltip
        align="center"
        prop="content"
        min-width="220"
      />

      <el-table-column
        label="创建时间"
        show-overflow-tooltip
        align="center"
        prop="createTime"
        min-width="100"
      />

      <el-table-column
        label="类型"
        show-overflow-tooltip
        align="center"
        prop="quesType"
        min-width="60"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.quesType == 0">
            单选题
          </span>

          <span v-else-if="scope.row.quesType == 1">
            多选题
          </span>

          <span v-else-if="scope.row.quesType == 2">
            判断题
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="结果"
        show-overflow-tooltip
        align="center"
        prop="userResult"
        min-width="60"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.userResult == 1">
            <i class="el-icon-check" style="color:#67C23A;font-weight: 700;" />
          </span>

          <span v-else-if="scope.row.userResult == 0">
            <i class="el-icon-close" style="color: #F56C6C;font-weight: 700;" />
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="得分"
        align="center"
        prop="userResult"
        min-width="60"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.userResult == 1 ? scope.row.score : 0 }}</span>
        </template>
      </el-table-column>
    </el-table>

    <dt-pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </el-dialog>
</template>

<script>
import {
  getPaperQuestionPage
} from '@/api/exam-manage/organize-exam'

export default {
  data() {
    return {
      // 弹框
      dialogVisible: false,

      // 表格
      total:0,
      tableData:[],
      loading:false,
      queryParams:{
        userId:'',
        examUserPaperId:'',
        pageNo:1,
        pageSize:10
      },

      highestScore:0
    }
  },

  methods: {
    // 初始化
    init(row) {
      this.queryParams.userId = row.userId
      this.highestScore = row.highestScore
      this.queryParams.examUserPaperId = row.examUserPaperId
      this.handleQuery()
      this.dialogVisible = true
    },

    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 获取列表
    getList() {
      const query = JSON.parse(JSON.stringify(this.queryParams))
      this.loading = true
      getPaperQuestionPage(query).then((data) => {
        this.loading = false
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      }).catch(() => {
        this.loading = false
      })
    },

    // 关闭弹框
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.total{
  margin-bottom: 10px;
  font-size: 14px;
  color: #333;
  font-weight: 700;
}
</style>
