
<!DOCTYPE html>
<html lang="en">
<head title="">
  <meta charset="UTF-8">
  <link rel="stylesheet" href="./index.css">
  <style>
  </style>
  <script src="./vue.js"></script>
     <script src="./index.js"></script>
</head>
<body>
  <div id="app" :loading="loading">
    <!-- 表单 -->
    <el-form :model="formData" ref="form" :disabled="disabled" :rules="rules" ref="form" label-width="100px" class="demo-formData">
      <el-form-item label="岗位名称" prop="positionName" v-show="getAuth('positionName','show')" :rules="getAuth('positionName','required')">
        <el-input v-model="formData.positionName" :disabled="getAuth('positionName','edit')"></el-input>
      </el-form-item>
      <el-form-item label="岗位编码" prop="positionCode" v-show="getAuth('positionCode','show')" :rules="getAuth('positionCode','required')">
        <el-input v-model="formData.positionCode" :disabled="getAuth('positionCode','edit')"></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="positionSort" v-show="getAuth('positionSort','show')" :rules="getAuth('positionSort','required')">
        <el-input v-model="formData.positionSort" :disabled="getAuth('positionSort','edit')"></el-input>
      </el-form-item>
        <el-form-item label="备注" prop="positionRemark" v-show="getAuth('positionRemark','show')" :rules="getAuth('positionRemark','required')">
          <el-input v-model="formData.positionRemark" type="textarea" :disabled="getAuth('positionRemark','edit')"></el-input>
        </el-form-item>
      </el-form>
  </div>
</body>

  <script>
    // 自定义表单需要注意内容 
    // 1. 添加iframe监听，获取父页面权限数据
    // 2. v-show、rules、disabled 根据权限数据改造
    // 3. 提交表单，将表单数据通过iframe监听回传到父页面

    /*
    formData 数据格式
    {"positionName":"办公室专员","positionCode":"office","positionSort":"3","positionRemark":""}

    authData 数据格式
    [{"columnId":-1,"dbColumnCode":"all","dbColumnName":"全部","indeterminate":false,"show":true,"edit":true,"required":false},
    {"columnId":"1702501272133074946","dbColumnCode":"name","dbColumType":"varchar","dbColumnName":"活动名称","dbColumnLength":11,"tagType":null,"childrenColumns":null,"show":true,"edit":true,"required":false},
    {"columnId":"1702501272330207233","dbColumnCode":"region","dbColumType":"varchar","dbColumnName":"活动区域","dbColumnLength":30,"tagType":"text","childrenColumns":null,"show":true,"edit":true,"required":false},
    {"columnId":"1702501272527339522","dbColumnCode":"date","dbColumType":"datetime","dbColumnName":"活动时间","dbColumnLength":255,"tagType":null,"childrenColumns":null,"show":true,"edit":true,"required":false},
    {"columnId":"1702501272527339523","dbColumnCode":"delivery","dbColumType":"varchar","dbColumnName":"即时配送","dbColumnLength":255,"tagType":null,"childrenColumns":null,"show":true,"edit":true,"required":false},
    {"columnId":"1702501272527331234","dbColumnCode":"resource","dbColumType":"varchar","dbColumnName":"特殊资源","dbColumnLength":255,"tagType":null,"childrenColumns":null,"show":true,"edit":true,"required":false},
    {"columnId":"1702501272527331235","dbColumnCode":"desc","dbColumType":"varchar","dbColumnName":"活动形式","dbColumnLength":255,"tagType":null,"childrenColumns":null,"show":true,"edit":true,"required":false},
    {"columnId":"1702501272527331236","dbColumnCode":"imgUrl","dbColumType":"varchar","dbColumnName":"头像","dbColumnLength":255,"tagType":null,"childrenColumns":null,"show":true,"edit":true,"required":false}]
    */
    new Vue({
      el: '#app',
      data: function() {
        return {
          loading:true,
          // 定义表单数据
          formData: {
            positionName:'',
            positionCode:'',
            positionSort:'',
            positionRemark:''
          },
          // 定义权限数据
          authData:{},
          // 表单是否校验
          disabled:false,
          // 校验规则
          rules: {
            positionName: [
              { required: true, message: '请输入岗位名称', trigger: 'change' },
            ],
            positionCode: [
              { required: true, message: '请输入岗位编码', trigger: 'change' }
            ],
            positionSort: [
            { required: true, message: '请输入排序', trigger: 'change' }
            ],
            positionRemark:[
            { required: true, message: '请输入备注', trigger: 'change' }
            ]
          },
         }
      },
      // 监听事件
      mounted(){
        // iframe监听
        window.addEventListener('message', this.handleMessage)
      },
      methods: {
        handleMessage(event) {
          if(event.data.cmd == 'initialization'){
             // 获取父页面数据
            this.getData(event)
          }else{
            // 提交数据回父页面
            this.handleSubmit(event)
          }
        },
        getData(event){
          // 获取数据权限
          this.authData = event.data.data.authData?event.data.data.authData:{}
          // 获取表单数据
          this.formData = event.data.data.formData?event.data.data.formData:this.formData
          // 全表单是否禁用
          this.disabled = event.data.data.customerDisabled?event.data.data.customerDisabled:false
          this.loading = false
        },
        // 通过数据权限处理表单
        getAuth(prop,auth){
          if(JSON.stringify(this.authData) !== '{}'){
            if(auth === 'show'){
              return this.authData[prop]?(this.authData[prop][auth]!==false?true:false):true
            }else if(auth === 'required'){
              return this.authData[prop]?(this.authData[prop][auth]!==false?this.rules.prop:[]):[]
            }else{
              return this.authData[prop]?(this.authData[prop][auth]!==false?false:true):false
            }
          }
          // 当数据权限为空时，会赋予表单默认权限
          else{
            if(auth === 'show'){
              return true
            }else if(auth === 'required'){
              return []
            }else{
              return false
            }
          }
        },
        handleSubmit(event){
          this.$refs['form'].validate((valid) => {
            // 校验通过，发送表单数据
            if (valid && event.data.data.isSubmit === 1) {
                window.parent.postMessage({
                  message: 'success',
                  data:this.formData
                }, '*');
            }else{
              // 处理表单校验失败的情况
              window.parent.postMessage({
                  message: 'error',
                  data:{}
                }, '*');
            }
          })
        }
      },
    })
  </script>
</html>
