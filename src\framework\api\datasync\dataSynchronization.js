import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 查询列表
  dataSyncJobPage(params) {
    return request({
      url: `${cloud.datasync}/dataSyncJob/page`,
      method: 'get',
      params
    })
  },
  // 获取任务名称、任务编码
  generateJobIdAndJobName(params) {
    return request({
      url: `${cloud.datasync}/generateJobIdAndJobName`,
      method: 'get',
      params
    })
  },
  // 同步应用
  dataSyncApps(params) {
    return request({
      url: `${cloud.manage}/sysApp/getThirdAppList`,
      method: 'get',
      params
    })
  },
  // 配置应用topic分页列表
  appTopicPage(params) {
    return request({
      url: `${cloud.datasync}/dataSyncJob/appTopicPage`,
      method: 'get',
      params
    })
  },
  // 获取同步应用
  dataSyncTopicApps(params) {
    return request({
      url: `${cloud.datasync}/dataSyncJob/dataSyncTopicApps`,
      method: 'get',
      params
    })
  },
  // 同步任务详情
  detail(params) {
    return request({
      url: `${cloud.datasync}/dataSyncJob/detail`,
      method: 'get',
      params
    })
  },
  // 同步任务删除
  delete(data) {
    return request({
      url: `${cloud.datasync}/dataSyncJob/delete`,
      method: 'POST',
      data
    })
  },
  // 配置数据自动同步策略
  configDataAutoSyncStrategy(data) {
    return request({
      url: `${cloud.datasync}/configDataAutoSyncStrategy`,
      method: 'POST',
      data
    })
  },
  // 生成topic
  generateTopic(params) {
    return request({
      url: `${cloud.datasync}/dataSyncJob/generateTopic`,
      method: 'get',
      params
    })
  },
  // 配置Ttopic
  disposeTopic(data) {
    return request({
      url: `${cloud.datasync}/dataSyncJob/disposeTopic`,
      method: 'POST',
      data
    })
  },
  // 配置Ttopic
  topicDetail(params) {
    return request({
      url: `${cloud.datasync}/dataSyncJob/topicDetail`,
      method: 'get',
      params
    })
  },
  // 删除Ttopic
  deleteTopic(data) {
    return request({
      url: `${cloud.datasync}/dataSyncJob/deleteTopic`,
      method: 'POST',
      data
    })
  },
  // 编辑Ttopic
  editTopic(data) {
    return request({
      url: `${cloud.datasync}/dataSyncJob/editTopic`,
      method: 'POST',
      data
    })
  },
  // 获取应用名称
  getStatusThirdAppList(params) {
    return request({
      url: `${cloud.manage}/sysApp/getStatusThirdAppList`,
      method: 'get',
      params
    })
  },
  // 手动生成任务
  generateManualJob(data) {
    return request({
      url: `${cloud.datasync}/dataSyncJob/generateManualJob`,
      method: 'post',
      data
    })
  },
  // 手动执行任务
  executeJob(params) {
    return request({
      url: `${cloud.datasync}/dataSyncJob/executeJob`,
      method: 'get',
      params
    })
  },
  // 配置自动任务策略反显
  // 手动执行任务
  getDataAutoSyncStrategy(params) {
    return request({
      url: `${cloud.datasync}/getDataAutoSyncStrategy`,
      method: 'get',
      params
    })
  },
  // 自动策略获取列表
  getDataAutoSyncStrategyList(params) {
    return request({
      url: `${cloud.datasync}/getDataAutoSyncStrategyList`,
      method: 'get',
      params
    })
  },
  // 删除数据自动同步策略
  delDataAutoSyncStrategy(data) {
    return request({
      url: `${cloud.datasync}/delDataAutoSyncStrategy`,
      method: 'post',
      data
    })
  },
  // 获取配置api列表
  dataSyncAppApiList(params) {
    return request({
      url: `${cloud.datasync}/dataSyncAppApi/list`,
      method: 'get',
      params
    })
  },
  // 配置api
  disposeApi(data) {
    return request({
      url: `${cloud.datasync}/dataSyncJob/disposeApi`,
      method: 'post',
      data
    })
  }

})
