import { default as request, cloud } from '@/framework/utils/request'

// 查询账号解锁列表
export function listApp(query) {
  return request({
    url: `${cloud.usercenter}/sysUser/lockedUserPage/V2`,
    method: 'get',
    params: query
  })
}

// 解锁
export function unLock(data) {
  return request({
    url: `${cloud.usercenter}/unlock`,
    method: 'post',
    data
  })
}

// 批量解锁
export function unLockMore(data) {
  return request({
    url: `${cloud.usercenter}/batchUnlock`,
    method: 'post',
    data
  })
}
