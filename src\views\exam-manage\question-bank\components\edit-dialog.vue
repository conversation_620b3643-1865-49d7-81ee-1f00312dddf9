<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="45%"
    :before-close="handleClose"
    top="10vh"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="90px"
      label-position="left"
    >
      <el-form-item
        label="试题"
        prop="content"
      >
        <el-input
          v-model="form.content"
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 3 }"
          maxlength="200"
          show-word-limit
          placeholder="请输入"
        />
      </el-form-item>

      <div v-if="form.quesType == 0 || form.quesType == 1">
        <el-form-item
          v-for="(option, index) in form.options"
          :key="option.index"
          :label="'选项' + String.fromCharCode(65 + index)"
          :prop="'options.' + index + '.value'"
          :rules="{
            required: true,
            message: '请输入选项',
            trigger: 'blur'
          }"
        >
          <div style="display: flex; align-items: center">
            <el-input
              v-model="option.value"
              placeholder="请输入"
              maxlength="200"
              show-word-limit
              clearable
            />

            <el-button
              style="margin-left: 20px"
              type="text"
              @click.prevent="removeOption(option)"
            >
              删除
            </el-button>
          </div>
        </el-form-item>
      </div>


      <el-form-item
        v-if="form.quesType == 0"
        label="正确答案"
        prop="monoRight"
      >
        <el-radio-group v-model="form.monoRight">
          <el-radio
            v-for="(option, index) in form.options"
            :key="index"
            :label="index"
          >
            {{ String.fromCharCode(65 + index) }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        v-if="form.quesType == 1"
        label="正确答案"
        prop="multiRightArr"
      >
        <el-checkbox-group v-model="form.multiRightArr">
          <el-checkbox
            v-for="(option, index) in form.options"
            :key="index"
            :label="index"
          >
            {{ String.fromCharCode(65 + index) }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item
        v-if="form.quesType == 2"
        label="正确答案"
        prop="judgeRight"
      >
        <el-radio-group v-model="form.judgeRight">
          <el-radio label="0">
            对
          </el-radio>

          <el-radio label="1">
            错
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="解析" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          maxlength="200"
          :autosize="{ minRows: 4, maxRows: 4 }"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button
        v-if="form.quesType == 0 || form.quesType == 1"
        type="primary"
        size="small"
        @click="addOption"
      >
        添 加 选 项
      </el-button>

      <el-button size="small" @click="handleClose">
        取 消
      </el-button>

      <el-button type="primary" size="small" @click="handleSave">
        保 存
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  trainingQuestionAdd
} from '@/api/exam-manage/question-bank'

export default {
  data() {
    return {
      // 弹框
      dialogTitle: '',
      dialogVisible: false,

      form: {
        quesType:'',
        classification: '',

        // 试题
        content: '',
        // 选项
        options:[
          { value: '' },
          { value: '' },
          { value: '' },
          { value: '' }
        ],

        optionA:'',
        optionB:'',
        optionC:'',
        optionD:'',
        optionE:'',
        optionF:'',

        // 正确答案
        monoRight: '', // 单选
        multiRightArr: [], // 多选
        multiRight: '', // 多选
        judgeRight: '', // 判断题

        // 解析
        remark: ''
      },

      // 表单校验规则
      rules: {
        content: [{ required: true, message: '请输入试题', trigger: 'blur' }],

        monoRight: [
          { required: true, message: '请选择正确答案', trigger: 'change' }
        ],

        multiRightArr: [
          { required: true, message: '请选择正确答案', trigger: 'change' }
        ],

        judgeRight: [
          { required: true, message: '请选择正确答案', trigger: 'change' }
        ]

      }
    }
  },

  methods: {
    // 初始化
    init(row) {
      this.dialogTitle = `新增${['单选题', '多选题', '判断题'][this.form.quesType]}`
      this.dialogVisible = true
    },

    // 添加选项
    addOption() {
      if (this.form.options.length >= 6) {
        this.$message({
          type: 'warning',
          message: '最多只能添加六个选项'
        })
        return
      }
      this.form.options.push({
        value: ''
      })
    },

    // 删除选项
    removeOption(item) {
      if (this.form.options.length <= 1) {
        this.$message({
          type: 'warning',
          message: '至少保留一个选项'
        })
        return
      }
      const index = this.form.options.indexOf(item)
      if (index !== -1) {
        this.form.options.splice(index, 1)
      }
    },

    // 保存
    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.form.id) {
            // 新增
            if (this.form.quesType == 1) {
              this.form.multiRight = this.form.multiRightArr.join(',')
            }
            if (this.form.quesType == 0 || this.form.quesType == 1) {
              this.form.options.forEach((item, index) => {
                this.form[`option${String.fromCharCode(65 + index)}`] = item.value
              })
            }

            trainingQuestionAdd(this.form).then((res) => {
              this.$message.success('添加成功')
              this.handleClose()
              this.$emit('update')
            })
          }
        } else {
          return false
        }
      })
    },

    // 取消
    handleClose() {
      this.$refs.form.resetFields()
      this.form = {
        quesType:'',
        classification: '',

        // 试题
        content: '',
        // 选项
        options:[
          { value: '' },
          { value: '' },
          { value: '' },
          { value: '' }
        ],

        optionA:'',
        optionB:'',
        optionC:'',
        optionD:'',
        optionE:'',
        optionF:'',

        // 正确答案
        monoRight: '', // 单选
        multiRightArr: [], // 多选
        multiRight: '', // 多选
        judgeRight: '', // 判断题

        // 解析
        remark: ''
      }
      this.dialogVisible = false
    }
  }
}
</script>
