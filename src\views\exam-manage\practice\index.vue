<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="70px"
          style="width: 100%;display: flex;align-items: center;justify-content: space-between;"
          @submit.native.prevent
        >
          <el-form-item label="练习名称" prop="exerciseName">
            <el-input
              v-model.trim="queryParams.exerciseName"
              maxlength="30"
              placeholder="请输入"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <div class="fr">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">
              搜索
            </el-button>

            <el-button icon="el-icon-refresh" @click="handleReset">
              重置
            </el-button>
          </div>
        </el-form>
      </div>

      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleAdd"
          >
            新增
          </el-button>

          <div class="flex-1" />
        </div>

        <el-table
          ref="table"
          v-loading="loading"
          style="width: 100%;"
          border
          highlight-current-row
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            type="index"
            label="序号"
            width="70"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            label="练习名称"
            show-overflow-tooltip
            align="center"
            prop="exerciseName"
            min-width="300"
          >
            <template slot-scope="scope">
              <span style="color: #409EFF;cursor: pointer;" @click="handleDetail(scope.row)">
                {{ scope.row.exerciseName }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="题库栏目"
            show-overflow-tooltip
            align="center"
            prop="classificationName"
            min-width="100"
          />


          <el-table-column
            label="单选题"
            align="center"
            prop="singleChoiceCount"
            min-width="60"
          />

          <el-table-column
            label="多选题"
            align="center"
            prop="multipleChoiceCount"
            min-width="60"
          />

          <el-table-column
            label="判断题"
            align="center"
            prop="judgeCount"
            min-width="60"
          />

          <el-table-column
            label="出题时间"
            show-overflow-tooltip
            align="center"
            prop="createTime"
            min-width="120"
          />

          <el-table-column
            label="状态"
            show-overflow-tooltip
            align="center"
            prop="status"
            min-width="60"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.status == 1">
                启用
              </span>

              <span v-else-if="scope.row.status == 2">
                停用
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            min-width="110"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.status == 2"
                type="text"
                @click="handleEnable(scope.row)"
              >
                启用
              </el-button>

              <el-button
                v-if="scope.row.status == 1"
                type="text"
                @click="handleDisable(scope.row)"
              >
                停用
              </el-button>

              <el-button
                v-if="scope.row.status == 2"
                type="text"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>

              <el-button
                v-if="scope.row.status == 2"
                type="text"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 新增编辑弹框 -->
    <EditDialog ref="editRef" @update="handleQuery" />
  </div>
</template>

<script>
import {
  getTrainingExercisePage,
  trainingExerciseDelete,
  updateStatus
} from '@/api/exam-manage/practice'
import EditDialog from './components/edit-dialog.vue'

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name:'Practice',
  components: {
    EditDialog
  },

  data() {
    return {
      queryParams:{
        exerciseName:'',
        pageNo:1,
        pageSize:10
      },

      loading:false,
      tableData:[],
      total:0
    }
  },

  created() {
    this.handleQuery()
  },

  methods: {
    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 重置
    handleReset() {
      this.queryParams = {
        exerciseName:'',
        pageNo:1,
        pageSize:10
      }
      this.getList()
    },

    // 获取列表
    getList() {
      const query = JSON.parse(JSON.stringify(this.queryParams))
      this.loading = true
      getTrainingExercisePage(query).then((data) => {
        this.loading = false
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      }).catch(() => {
        this.loading = false
      })
    },

    // 新增
    handleAdd() {
      this.$refs.editRef.init()
    },

    // 编辑
    handleEdit(row) {
      this.$refs.editRef.init(JSON.parse(JSON.stringify(row)))
    },

    // 详情
    handleDetail(row) {
      this.$refs.editRef.disabled = true
      this.$nextTick().then(() => {
        this.$refs.editRef.init(JSON.parse(JSON.stringify(row)))
      })
    },

    // 删除
    handleDelete(row) {
      this.$dtModal
        .confirm(`是否确认删除练习名称为"${row.exerciseName}"的数据项？`)
        .then(() => {
          row.ids = []
          row.ids.push(row.id)
          return trainingExerciseDelete(row)
        })
        .then((res) => {
          if (res.success == true) {
            this.getList()
            this.$dtModal.msgSuccess('删除成功')
          }
        })
        .catch(() => {})
    },

    // 启用
    handleEnable(row) {
      this.$dtModal
        .confirm(`是否确认启用练习名称为"${row.exerciseName}"的数据项？`)
        .then(() => {
          return updateStatus({
            id: row.id,
            status: 1
          })
        })
        .then((res) => {
          if (res.success) {
            this.getList()
            this.$dtModal.msgSuccess('启用成功')
          }
        })
        .catch(() => {})
    },

    // 停用
    handleDisable(row) {
      this.$dtModal
        .confirm(`是否确认停用练习名称为"${row.exerciseName}"的数据项？`)
        .then(() => {
          return updateStatus({
            id: row.id,
            status: 2
          })
        })
        .then((res) => {
          if (res.success) {
            this.getList()
            this.$dtModal.msgSuccess('停用成功')
          }
        })
        .catch(() => {})
    }
  }
}
</script>
