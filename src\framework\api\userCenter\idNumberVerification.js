import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 查询配置接口 返回true/false
  getAuthidcard: () => {
    return request({
      url: `${cloud.usercenter}/sysUser/authidcard/queryconfig`,
      method: 'get'
    })
  },
  // 配置接口 ，请求参数 auth = true/false
  updateAuthidcard: (params) => {
    return request({
      url: `${cloud.usercenter}/sysUser/authidcard/config`,
      method: 'get',
      params
    })
  }
})
