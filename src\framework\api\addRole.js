import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 业务用户授权-基于角色授权-查询用户角色列表
  userRoleList(params) {
    return request({
      url: `${cloud.permission}/businessUser/userRoleList`,
      method: 'get',
      params: {
        userId: params.userId,
        roleName: params.roleName,
        pageNo: params.pageNo,
        pageSize: params.pageSize
      }
    })
  }
})
