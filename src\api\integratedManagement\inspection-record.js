import request, {cloud} from '@/framework/utils/request'

// 查询执法记录列表
export function listInspectionRecord(query) {
  return request({
    url: cloud.dqbasic + '/inspectionRecord/page',
    method: 'get',
    params: query
  })
}

// 查询执法记录详细
export function getInspectionRecord(id) {
  return request({
    url: cloud.dqbasic + '/inspectionRecord/detail?id=' + id,
    method: 'get'
  })
}

// 新增执法记录
export function addInspectionRecord(data) {
  return request({
    url: cloud.dqbasic + '/inspectionRecord/add',
    method: 'post',
    data: data
  })
}

// 修改执法记录
export function updateInspectionRecord(data) {
  return request({
    url: cloud.dqbasic + '/inspectionRecord/edit',
    method: 'post',
    data: data
  })
}

// 删除执法记录
export function delInspectionRecord(id) {
  return request({
    url: cloud.dqbasic + '/inspectionRecord/delete',
    method: 'post',
    data: { ids: id }
  })
}
