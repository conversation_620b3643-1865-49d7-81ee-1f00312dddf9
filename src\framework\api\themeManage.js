import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 条件查询
  pageList(params) {
    return request({
      url: `${cloud.manage}/theme/pageList`,
      method: 'get',
      params
    })
  },
  add(data) {
    return request({
      url: `${cloud.manage}/theme/add`,
      method: 'post',
      data
    })
  },
  edit(data) {
    return request({
      url: `${cloud.manage}/theme/edit`,
      method: 'post',
      data
    })
  },
  //   主题更新状态
  updateStatus(data) {
    return request({
      url: `${cloud.manage}/theme/updateStatus`,
      method: 'post',
      data
    })
  },
  delete(params) {
    return request({
      url: `${cloud.manage}/theme/del`,
      method: 'get',
      params
    })
  },
  // 查看详情
  detail(params) {
    return request({
      url: `${cloud.manage}/theme/detail`,
      method: 'get',
      params
    })
  },
  // 获取当前主题
  currentThemeInfo(params) {
    return request({
      url: `${cloud.manage}/themeApi/currentThemeInfo`,
      method: 'get',
      params
    })
  }
})
