<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <div class="flex-1"></div>
          <dt-dialog-column v-model="isShowTable" :columns="showColumns" :table-ref="$refs.table"
                            @queryTable="getList"/>
        </div>
        <el-table v-if="isShowTable" v-loading="loading" ref="table" border highlight-current-row
                  :header-cell-style="{ backgroundColor: '#f2f2f2'}" :data="IntegrationDetailList">
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>
          <el-table-column
            fixed="left"
            type="index"
            label="序号"
            width="70"
            :index="(index)=>(queryParams.pageNo - 1) * queryParams.pageSize + index + 1"
          />
          <el-table-column v-for="(item,index) in showColumns" :label="item.label" v-if="item.show" show-overflow-tooltip :key="item.prop"
                           align="center" :prop="item.prop">
            <template slot-scope="scope">
              <span v-if="item.prop === 'type'">{{ scope.row.type == 1 ? '自主学习' :  '答题练习'  }}</span>
              <span v-else-if="item.prop === 'createTime'">{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              <span v-else-if="item.prop  === 'getScore'">{{ '+' + scope.row.getScore }}</span>
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {
  listIntegrationDetail,
  getIntegrationDetail,
  delIntegrationDetail,
  addIntegrationDetail,
  updateIntegrationDetail
} from "@/api/training-examination/integration/IntegrationDetail";

export default {
  name: "IntegrationDetail",
  data() {
    return {
      // 遮罩层
      loading: true,
      //显隐表格
      isShowTable: true,
      // 选中数组
      ids: [],
      //列显隐数组
      showColumns: [
        {prop: "type", label: "行为", show: true},
        {prop: "name", label: "名称", show: true},
        {prop: "createTime", label: "时间", show: true},
        {prop: "getScore", label: "积分", show: true},

      ],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 积分明细表格数据
      IntegrationDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {}
    };
  },
  computed: {
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询积分明细列表 */
    getList() {
      this.loading = true;
      listIntegrationDetail(this.queryParams).then(({data: response}) => {
        this.IntegrationDetailList = response.rows;
        this.total = response.totalRows;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    handleReset() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('integrationDetail/IntegrationDetail/export', {
        ...this.queryParams
      }, `integrationDetail_IntegrationDetail.xlsx`)
    }
  }
};
</script>
