import request from "@/framework/utils/request";

// 根据培训id查询培训评价
export function getTrainingPlanEvaluation(id) {
    return request({
        url: '/project/trainingRemember/evaluationInfo',
        method: 'GET',
        params: { trainingId: id }
    })
}

//根据培训ID查询培训基础信息
export function getTrainingPlanById(id) {
  return request({
    url: "/project/trainingBaseInfo/recordDetail",
    method: "GET",
    params: { id },
  });
}

// 分页查询培训报告列表所有数据
export function getTrainRecordPage(data) {
    return request({
      url: "/project/trainingBaseInfo/getTrainRecordPage",
      method: "POST",
      data: data,
    });
}
  
  // 根据培训ID查询培训人员列表
export function getPageByTrainingId(data) {
    return request({
      url: "/project/trainingRemember/getPageByTrainingId",
      method: "GET",
      params: data,
    });
}

// 分页查询学习记录列表所有数据
export function getCourseRecordPage(data) {
  return request({
    url: "/project/trainingCourseRecord/getCourseRecordPage",
    method: "POST",
    data: data,
  });
}

// 分页查询学习时长列表所有数据
export function getCourseDurationPage(data) {
  return request({
    url: "/project/trainingCourseRecord/getCourseRecordList",
    method: "POST",
    data: data,
  });
}