import request from '@/framework/utils/request'

// 查询停车场车位管理列表
export function listCarCarbarn(query) {
  return request({
    url: '/project/hazardCarCarbarn/page',
    method: 'get',
    params: query
  })
}

export function listParking(query) {
  return request({
    url: '/project/hazardParking/list',
    method: 'get',
    params: query
  })
}

// 查询停车场车位管理详细
export function getCarCarbarn(id) {
  return request({
    url: '/project/hazardCarCarbarn/detail?id=' + id,
    method: 'get'
  })
}

// 新增停车场车位管理
export function addCarCarbarn(data) {
  return request({
    url: '/project/hazardCarCarbarn/add',
    method: 'post',
    data: data
  })
}

// 修改停车场车位管理
export function updateCarCarbarn(data) {
  return request({
    url: '/project/hazardCarCarbarn/edit',
    method: 'post',
    data: data
  })
}

// 删除停车场车位管理
export function delCarCarbarn(id) {
  return request({
    url: '/project/hazardCarCarbarn/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 禁用停车场车位管理
export function forbiddenCarCarbarn(id) {
  return request({
    url: '/project/hazardCarCarbarn/forbidden',
    method: 'post',
    data: { ids: id }
  })
}
