/*
 * @Author: wang<PERSON><PERSON> <EMAIL>
 * @Date: 2024-08-11 13:14:44
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-08-12 16:41:43
 * @Description: 数据预警
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */

import request, {cloud}  from '@/framework/utils/request'


// 查询重大危险源数据预警列表
export function listGalleryDataWarning(query) {
  return request({
    url: cloud.dqbasic +'/galleryDataWarning/page',
    method: 'get',
    params: query
  })
}

// 查询重大危险源数据预警详细
export function getGalleryDataWarning(id) {
  return request({
    url: cloud.dqbasic +'/galleryDataWarning/detail?id=' + id,
    method: 'get'
  })
}

// 新增重大危险源数据预警
export function addGalleryDataWarning(data) {
  return request({
    url: cloud.dqbasic +'/galleryDataWarning/add',
    method: 'post',
    data: data
  })
}

// 修改重大危险源数据预警
export function updateGalleryDataWarning(data) {
  return request({
    url: cloud.dqbasic +'/galleryDataWarning/deal',
    method: 'post',
    data: data
  })
}

// 删除重大危险源数据预警
export function delGalleryDataWarning(id) {
  return request({
    url: cloud.dqbasic +'/galleryDataWarning/delete',
    method: 'post',
    data: { ids: id }
  })
}
