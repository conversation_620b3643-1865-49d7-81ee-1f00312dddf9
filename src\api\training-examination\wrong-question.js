/*
 * @Author: gaoyu <EMAIL>
 * @Date: 2025-03-10 20:53:02
 * @LastEditors: gaoyu <EMAIL>
 * @LastEditTime: 2025-03-11 13:10:24
 * @FilePath: /IntelligentSecurityRiskManagementPlatform-VUE/src/api/training-examination/wrong-question.js
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import { default as request, cloud } from '@/framework/utils/request'
/**
 * 分页查询错题排行所有数据
 * @param {Object} params - 查询参数
 * @return {Promise} 返回一个Promise对象
 */
export function getErrorNumRecordsPage(params) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestion/getErrorNumRecordsPage`,
    method: 'get',
    params
  })
}
/**
 * 获取字典表
 * @param {Object} params - 查询参数
 * @return {Promise} 返回一个Promise对象
 */
export function getDictList(params) {
  return request({
    url: `${cloud.dqbasic}/dict/list`,
    method: 'get',
    params
  })
}
