<!--
  * @Author: lize
  * @Date: 2025-05-20 15:13:11
  * @LastEditors: lize
  * @LastEditTime: 2025-05-20 15:13:11
  * @Description: 个人培训档案-包教师傅签字
  * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
-->
<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="table-container">
        <!-- 班组级岗位安全教育 -->
        <div class="three-level-training">
          <div class="train-type-label">
            <span>班组级岗位安全教育</span>
          </div>
          <div class="train-type-content">
            <div class="train-type-item">
              <div class="train-type-item-label" style="min-width: 72px">教育内容：</div>
              <div>
                岗位安全操作规程；岗位之间工作衔接配合的安全与职业卫生事项；有关事故案例；其他需要培训的内容等。
              </div>
            </div>
            <div class="train-type-item">
              <div class="train-type-item-label">课程名称：</div>
              <div>
                {{ form.onlineCourseNames }}
              </div>
            </div>
            <div class="train-type-item">
              <div class="train-type-item-label">分配班组日期：</div>
              <div>
              </div>
            </div>
            <div class="train-type-item">
              <div class="train-type-item-label">教育时间：</div>
              <div>
                {{ form.trainBegin }}
              </div>
            </div>
            <div class="train-type-item">
              <div class="train-type-item-label">考试成绩：</div>
              <div>
                {{ form.userExamScore }}
              </div>
            </div>
            <div class="train-type-item">
              <div class="train-type-item-label">教育人：</div>
              <div>
                {{ form.teacherName }}
              </div>
            </div>
            <div class="train-type-item">
              <div class="train-type-item-label">受教育人（签名）：</div>
              <div>
                <PreviewImage style="display: inline-block;" 
                  :file-id="form.sign" 
                  />
              </div>
            </div>
            <div class="train-type-item">
              <div class="train-type-item-label">包教师傅：</div>
              <div>
                <PreviewImage style="display: inline-block;" 
                  v-if="form.groupMasterSign" 
                  :file-id="form.groupMasterSign" 
                  />
                <div v-else class="sign-text-line"></div>
                <el-button @click="handleSign('companyLevel')">师傅签字</el-button>
              </div>
            </div>
            <div class="train-type-item">
              <div class="train-type-item-label">独立操作前考试成绩：</div>
              <div>
                <el-input-number 
                  v-model="form.groupMasterScore" 
                  :min="0"
                  :max="99999999"
                  :precision="2"
                  :controls="false"
                  placeholder="请输入"
                  >
                </el-input-number>
              </div>
            </div>
          </div>
        </div>

        <div style="display: flex;justify-content: center;margin-top: 20px;">
          <el-button :loading="submitLoading" type="primary" @click="handleSubmit">提交</el-button>
        </div>
      </div>
    </div>  
  </div>
</template>
<script>
import PictureUploadEcho from '@/components/picture-upload-echo/picture-upload-echo.vue'
import { getUserInfo, getCurrentInfo } from '@/api/training-examination/training-archive.js'
import DeptSelect from '@/components/dept-select/dept-select.vue'
import PreviewImage from '@/components/preview-image/preview-image.vue'
import { getTrainGroupDetail, submitMasterScore } from '@/api/training-examination/training-archive.js'
export default {
  name: 'ArchiveTeacherSign',
  components: { PictureUploadEcho, DeptSelect, PreviewImage },
  data() {
    return {
      // 受教育人-用户id
      userId: '',
      // 培训计划-基本信息id
      trainId: '',
      // 班组级岗位安全教育
      form: {
        // 培训计划id
        trainId: '',
        // 培训类型
        trainType: '',
        // 培训课程名称
        onlineCourseNames: '',
        // 培训时间
        trainDate: '',
        // 培训开始时间
        trainBegin: '',
        // 培训结束时间
        trainEnd: '',
        // 培训成绩
        userExamScore: '',
        // 培训教师名称
        teacherName: '',
        // 分配车间（工段、区、队）日期 / 分配班组日期
        assignDate: '',
        // 受教育人（签名）
        sign: '',
        // 班组级-保教师傅-用户id
        groupMasterUserId: '',
        // 班组级-保教师傅-签字图片id
        groupMasterSign: '',
        // 班组级-保教师傅-打分分数
        groupMasterScore: undefined
      },
      submitLoading: false,
      // 校验规则
      rules: {
        'basaInfo.name': [
            { required: true, message: '请输入姓名', trigger: 'blur' },
        ],
      },
      // 签名图片id
      signImgId: null,
    }
  },

  created() {

  },

  mounted() {
    let {userId, trainId} = this.$route.query
    this.userId = userId
    this.trainId = trainId
    getTrainGroupDetail(userId, trainId).then(res => {
      if (res.code === '00000') {
        this.form = res.data
      }
    })
    // 获取签名图片id
    getCurrentInfo().then(res => {
      this.signImgId = res.data.sign
    })
  },

  methods: {
    // 签字
    handleSign() {
      this.$confirm('确定签字?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$set(this.form, 'groupMasterSign', this.signImgId);
      }).catch(() => {
      });
    },

    // 提交
    handleSubmit() {
      this.submitLoading = true
      submitMasterScore(this.form).then(res => {
        if (res.code === '00000') {
          this.$message.success('提交成功')
        }
      }).finally(() => {
        this.submitLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>

.three-level-training{
  margin-left: 20px;
}

.three-level-training:not(:first-child){
  margin-top: 30px;
}

.train-type-label{
  font-size: 18px;
  margin-bottom: 10px;
}

.train-type-content{
  margin-left: 20px;
}
.train-type-item{
  display: flex;
  font-size: 14px;
  line-height: 30px;
}
.train-type-item-label{
  min-width: 72px
}
.sign-text-line{
  height: 0px;
  width: 130px;
  border-bottom: 1px solid rgb(0, 0, 0);
  display: inline-block;
  position: relative;
  top: 10px;
  margin-right: 15px;
}
.train-archive-input-number{
  ::v-deep{
    input{
      width: 100%;
      text-align: left;
    }
  }
}
</style>
