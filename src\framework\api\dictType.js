import { default as request, cloud } from '@/framework/utils/request'

export default ({
  getConfigDictTypeDetail(params) {
    return request({
      url: `${cloud.dqbasic}/dictType/getConfigDictTypeDetail`,
      method: 'get'
    })
  },
  page(params) {
    return request({
      url: `${cloud.dqbasic}/dictType/page`,
      method: 'get',
      params
    })
  },
  detail(id) {
    return request({
      url: `${cloud.dqbasic}/dictType/detail`,
      method: 'get',
      params: {
        dictTypeId: id
      }
    })
  },
  add(data) {
    return request({
      url: `${cloud.dqbasic}/dictType/add`,
      method: 'post',
      data
    })
  },
  edit(data) {
    return request({
      url: `${cloud.dqbasic}/dictType/edit`,
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: `${cloud.dqbasic}/dictType/delete`,
      method: 'post',
      data
    })
  }

})
