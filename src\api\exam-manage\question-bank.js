import request, { cloud } from '@/framework/utils/request'

/**
 * 试题栏目
 */

// 栏目查询
export function getTrainingQuestionClassificationPage(query) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestionClassification/page`,
    method: 'get',
    params: query
  })
}

// 栏目查询-无分页
export function getTrainingQuestionClassificationList(query) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestionClassification/getList`,
    method: 'get',
    params: query
  })
}

// 栏目详情
export function getTrainingQuestionClassificationDetail(query) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestionClassification/detail`,
    method: 'get',
    params: query
  })
}

// 栏目新增
export function trainingQuestionClassificationAdd(data) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestionClassification/add`,
    method: 'post',
    data
  })
}

// 栏目编辑
export function trainingQuestionClassificationEdit(data) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestionClassification/edit`,
    method: 'post',
    data
  })
}


// 栏目删除
export function trainingQuestionClassificationDelete(data) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestionClassification/delete`,
    method: 'post',
    data
  })
}

// 栏目启用、停用
export function trainingQuestionClassificationUpdateStatus(data) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestionClassification/updateStatus`,
    method: 'post',
    data
  })
}


/**
 * 题库管理
 */

// 题库查询
export function getTrainingQuestionPage(query) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestion/page`,
    method: 'get',
    params: query
  })
}

// 题库查询-题型排序
export function getTrainingQuestionSortList(query) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestion/getTypeSortPage`,
    method: 'get',
    params: query
  })
}

// 题库详情
export function getTrainingQuestionDetail(query) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestion/detail`,
    method: 'get',
    params: query
  })
}

// 题库新增
export function trainingQuestionAdd(data) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestion/add`,
    method: 'post',
    data
  })
}

// 题库编辑
export function trainingQuestionEdit(data) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestion/edit`,
    method: 'post',
    data
  })
}

// 题库删除
export function trainingQuestionDelete(data) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestion/delete`,
    method: 'post',
    data
  })
}

// 题库启用、停用
export function trainingQuestionUpdateStatus(data) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestion/updateStatus`,
    method: 'post',
    data
  })
}

// 题型统计
export function getCountQuestionByType(query) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestion/countQuestionByType`,
    method: 'get',
    params: query
  })
}

// 题库导出
export function trainingQuestionExport(query) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestion/export`,
    method: 'get',
    params: query,
    responseType: 'arraybuffer'
  })
}

// 题库导入模板下载
export function trainingQuestionTemplate(query) {
  return request({
    url: `${cloud.dqbasic}/trainingQuestion/exportTemplate`,
    method: 'get',
    params: query,
    responseType: 'arraybuffer'
  })
}

// 题库导入
export function trainingQuestionImport(data) {
  return request({
    url:  `${cloud.dqbasic}/trainingQuestion/import`,
    method: 'post',
    data
  })
}

