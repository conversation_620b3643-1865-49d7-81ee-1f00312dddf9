/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-20 16:43:48
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-26 11:21:09
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud}  from '@/framework/utils/request'
import {getRequest, postRequest} from '@/framework/utils/request'

// 查询列表
export function getList(query) {
  return getRequest(cloud.dqbasic + '/specialWorkDataWarning/page', query)
}

// 查询详细
export function getDetail(id) {
  return getRequest(cloud.dqbasic + '/specialWorkDataWarning/detail?id=' + id)
}

// 新增
export function add(data) {
  return postRequest(cloud.dqbasic + '/specialWorkDataWarning/add', data)
}

// 修改
export function edit(data) {
  return postRequest(cloud.dqbasic + '/specialWorkDataWarning/edit', data)
}

// 处理
export function deal(data) {
  return postRequest(cloud.dqbasic + '/specialWorkDataWarning/deal', data)
}

// 删除
export function del(id) {
  return postRequest(cloud.dqbasic + '/specialWorkDataWarning/delete', { ids: id })
}

// 查询当前报警列表
export function getCurrentList(query) {
  return getRequest(cloud.dqbasic + '/specialWorkDataWarning/current/page', query)
}