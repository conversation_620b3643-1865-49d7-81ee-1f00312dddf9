import request from '@/framework/utils/request'

// 查询车辆异常行为报警列表
export function listAlarm(query) {
  return request({
    url: '/project/personnelAlarm/page',
    method: 'get',
    params: query
  })
}

// 查询车辆异常行为报警详细
export function getAlarm(id) {
  return request({
    url: '/project/personnelAlarm/detail?id=' + id,
    method: 'get'
  })
}

// 消警车辆异常行为报警
export function antiAlarm(id) {
  return request({
    url: '/project/personnelAlarm/antiAlarm',
    method: 'post',
    data: { ids: id }
  })
}
