import { default as request, cloud } from '@/framework/utils/request'

export default ({
  getConfigGroupPage(params) {
    return request({
      url: `${cloud.dqbasic}/dict/getConfigGroupPage`,
      method: 'get',
      params
    })
  },
  add(data) {
    return request({
      url: `${cloud.dqbasic}/dict/add`,
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: `${cloud.dqbasic}/dict/delete`,
      method: 'post',
      data
    })
  },
  fetchPage(params) {
    return request({
      url: `${cloud.dqbasic}/dict/page`,
      method: 'get',
      params
    })
  },
  /**
   * 查询字典列表
   * @param {String} dictTypeCode 字典code
   * @returns 字典值List
   */
  fetchList(dictTypeCode) {
    return request({
      url: `${cloud.dqbasic}/dict/list`,
      method: 'get',
      params: {
        dictTypeCode
      }
    })
  },
  detail(id) {
    return request({
      url: `${cloud.dqbasic}/dict/detail`,
      method: 'get',
      params: {
        dictId: id
      }
    })
  },
  edit(data) {
    return request({
      url: `${cloud.dqbasic}/dict/edit`,
      method: 'post',
      data
    })
  },
  // 在删除组之前先判断是否可删接口
  beforeDelete(groupCode) {
    return request({
      url: `${cloud.dqbasic}/sysConfig/validateSysFlagByGroupCode`,
      method: 'get',
      params: {
        groupCode
      }
    })
  }
})
