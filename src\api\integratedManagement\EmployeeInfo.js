import request, {cloud} from '@/framework/utils/request'

// 查询从业人员档案列表
export function listEmployeeInfo(query) {
  return request({
    url: cloud.dqbasic + '/employeeInfo/page',
    method: 'get',
    params: query
  })
}

// 查询从业人员档案详细
export function getEmployeeInfo(id) {
  return request({
    url: cloud.dqbasic + '/employeeInfo/detail?id=' + id,
    method: 'get'
  })
}

// 新增从业人员档案
export function addEmployeeInfo(data) {
  return request({
    url: cloud.dqbasic + '/employeeInfo/add',
    method: 'post',
    data: data
  })
}
// Excel导入
export function importExcel(data) {
  return request({
    url:  '/project/employeeInfo/importExcel',
    method: 'post',
    data: data
  })
}

// 修改从业人员档案
export function updateEmployeeInfo(data) {
  return request({
    url: cloud.dqbasic + '/employeeInfo/edit',
    method: 'post',
    data: data
  })
}

// 删除从业人员档案
export function delEmployeeInfo(id) {
  return request({
    url:cloud.dqbasic +  '/employeeInfo/delete',
    method: 'post',
    data: { ids: id }
  })
}
