import { default as request, cloud } from '@/framework/utils/request'

// 查询列表
export function listprocessDesign(query) {
  return request({
    url: `${cloud.process}/flow/procdef/page`,
    method: 'get',
    params: query
  })
}

// 新增
export function addprocessDesign(data) {
  return request({
    url: `${cloud.process}/flowableModel/add`,
    method: 'post',
    data
  })
}

// 修改
export function updateprocessDesign(data) {
  return request({
    url: `${cloud.process}/flowableModel/update`,
    method: 'post',
    data
  })
}
// 部署
export function deploy(modelId) {
  return request({
    url: `${cloud.process}/flowable/deploy`,
    method: 'post',
    data: { modelId }
  })
}
// 复制
export function copy(data) {
  return request({
    url: `${cloud.process}/flow/procdef/copy`,
    method: 'post',
    data
  })
}
// 删除
export function delprocessDesign(id) {
  return request({
    url: `${cloud.process}/flow/procdef/delete`,
    method: 'post',
    data: { procdefId: id }
  })
}
// 禁用
export function enable(data) {
  return request({
    url: `${cloud.process}/flow/procdef/enable`,
    method: 'post',
    data
  })
}
