/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-07-15 08:31:36
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 17:15:26
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'
import {getRequest, postRequest} from '@/framework/utils/request'

// 查询人员出入园管理列表
export function listAccessControl(query) {
  return getRequest('/project/personAccessControl/page', query)
}

// 查询人员出入园管理详细
export function getAccessControl(id) {
  return getRequest('/project/personAccessControl/detail?id=' + id)
}

// 新增人员出入园管理
export function addAccessControl(data) {
  return postRequest('/project/personAccessControl/add', data)
}

// 修改人员出入园管理
export function updateAccessControl(data) {
  return postRequest('/project/personAccessControl/edit', data)
}

// 删除人员出入园管理
export function delAccessControl(id) {
  return postRequest('/project/personAccessControl/delete', { ids: id })
}
