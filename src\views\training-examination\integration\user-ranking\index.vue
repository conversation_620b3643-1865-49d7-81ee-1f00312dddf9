<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <div style="padding-bottom: 16px;">
          <div style="font-size: 18px; font-weight: bold;">积分榜前50</div>
          <div style="font-size: 16px;">我的积分排名为：{{userRanking}}</div>
        </div>
      </div>
      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <div class="flex-1"></div>
          <dt-dialog-column v-model="isShowTable" :columns="showColumns" :table-ref="$refs.table" @queryTable="getList"/>
        </div>
        <el-table v-if="isShowTable" v-loading="loading" ref="table" border highlight-current-row
                  :header-cell-style="{ backgroundColor: '#f2f2f2'}" :data="IntegrationRankingList">
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>
          <el-table-column v-for="item in showColumns" :label="item.label" show-overflow-tooltip :key="item.prop" align="center" :prop="item.prop">
            <template slot-scope="scope" v-if="item.show">
              <span v-if="item.prop === 'ranking'" class="ranking-index" :class="scope.row.ranking === 1 ? 'index1' : scope.row.ranking === 2 ? 'index2' : scope.row.ranking === 3 ? 'index3' : 'index4'">
                {{ scope.row.ranking }}
              </span>
              <span v-else-if="item.prop === 'endTime'">{{ scope.row.endTime }}</span>
              <span v-else>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <!-- 查看积分明细弹框 -->
    <el-dialog title="积分明细" :visible.sync="open">
      <div slot="content">
        <el-table v-loading="loading" ref="table" border highlight-current-row
                  :header-cell-style="{ backgroundColor: '#f2f2f2'}" :data="IntegrationDetailList">
          <el-table-column label="积分操作项" align="center" prop="type">
            <template slot-scope="scope">
              <span>{{ scope.row.type == 1 ? '自主学习' :  '答题练习'  }}</span>
            </template>
          </el-table-column>
          <el-table-column label="积分" align="center" prop="getScore">
            <template slot-scope="scope">
              <span>{{ '+' + scope.row.getScore }}</span>
            </template>
          </el-table-column>
          <el-table-column label="剩余积分" align="center" prop="unGetScore"></el-table-column>
          <el-table-column label="获取时间" align="center" prop="createTime">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
          </el-table-column>
        </el-table>
        <dt-pagination
          v-show="detailTotal>0"
          :total="detailTotal"
          :page.sync="detailParams.pageNo"
          :limit.sync="detailParams.pageSize"
          @pagination="handleDetail"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listIntegrationDetail,
  getIntegrationDetail,
  delIntegrationDetail,
  addIntegrationDetail,
  updateIntegrationDetail, getUserRanking, listIntegrationDetailRanking
} from "@/api/training-examination/integration/IntegrationDetail";

export default {
  name: "IntegrationDetail",
  data() {
    return {
      // 遮罩层
      loading: true,
      //显隐表格
      isShowTable: true,
      // 选中数组
      ids: [],
      //列显隐数组
      showColumns: [
        {prop: "ranking", label: "排名", show: true},
        {prop: "ownerUserName", label: "姓名", show: true},
        {prop: "ownerOrgName", label: "部门", show: true},
        {prop: "jobName", label: "岗位", show: true},
        {prop: "getScore", label: "总积分", show: true}
      ],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      detailTotal: 0,
      userRanking: 1,
      // 积分明细表格数据
      IntegrationRankingList: [],
      IntegrationDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        ownerUserName: null,
        ownerOrgId: null,
        pageNo: 1,
        pageSize: 10,
      },
      detailParams:  {
        userId: null,
        pageNo: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  computed: {
  },
  created() {
    this.getList();
    this.getUserRanking()
  },
  methods: {
    /** 查询积分明细列表 */
    getList() {
      this.loading = true;
      listIntegrationDetailRanking(this.queryParams).then(({data: response}) => {
        this.IntegrationRankingList = response.rows;
        this.total = response.totalRows;
        this.loading = false;
      });
    },
    getUserRanking() {
      this.loading = true;
      getUserRanking().then(response => {
        this.userRanking = response.data;
        this.loading = false;
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    handleReset() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleDetail(row) {
      if (row){
        this.detailParams.userId = row.ownerUserId;
      }
      listIntegrationDetail(this.detailParams).then(response => {
        this.IntegrationDetailList = response.rows;
        this.detailTotal = response.totalRows;
        this.open = true;
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('integrationDetail/IntegrationDetail/export', {
        ...this.queryParams
      }, `integrationDetail_IntegrationDetail.xlsx`)
    }
  }
};
</script>

<style lang="scss" scoped>
.ranking-index {
  display: block;
  margin: 0 auto;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  line-height: 30px;
}

.index1 {
  background: #d9001b !important;
}

.index2 {
  background: #02a7f0;
}

.index3 {
  background: #75e07f;
}

.index4 {
  background: #7f7f7f;
}
</style>
