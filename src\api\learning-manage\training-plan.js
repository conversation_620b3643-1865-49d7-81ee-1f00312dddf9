import request from '@/framework/utils/request'

export function getTrainingPlanList(payload) {
  return request({
    url: '/project/trainingBaseInfo/page',
    method: 'POST',
    data: payload
  })
}

export function addTrainingPlan(payload) {
  return request({
    url: '/project/trainingBaseInfo/add',
    method: 'POST',
    data: payload
  })
}

export function editTrainingPlan(payload) {
  return request({
    url: '/project/trainingBaseInfo/edit',
    method: 'POST',
    data: payload
  })
}

export function deleteTrainingPlan(payload) {
  return request({
    url: '/project/trainingBaseInfo/delete',
    method: 'POST',
    data: payload
  })
}

export function getTrainingPlanById(id) {
  return request({
    url: '/project/trainingBaseInfo/detail',
    method: 'GET',
    params: { id }
  })
}

export function downloadPersonnelTemplate() {
  return request({
    url: '/project/trainingBaseInfo/downloadTemplateFile',
    method: 'GET',
    responseType: 'blob'
  })
}

export function importPersonnel(data) {
  return request({
    url: '/project/trainingBaseInfo/importTemplateFile',
    method: 'POST',
    data
  })
}

// {id: '', status: 2||1}
export function updateTrainingPlanStatus(payload) {
  return request({
    url: '/project/trainingBaseInfo/updateStatus',
    method: 'POST',
    data: payload
  })
}

export function getPaperList(params) {
  return request({
    url: '/project/trainingExamMainPaper/page',
    method: 'GET',
    params
  })
}

// 根据培训id查询每个学员的进度
export function getTraineeProgressById(id) {
  return request({
    url: '/project/trainingBaseInfo/selectRememberLearnProcess',
    method: 'GET',
    params: { trainingId: id }
  })
}

// 根据培训id查询具体培训课程及进度
export function getTrainingPlanCourseById(id) {
  return request({
    url: '/project/trainingBaseInfo/selectCourseProcess',
    method: 'GET',
    params: { trainingId: id }
  })
}

// 根据培训id查询培训评价
export function getTrainingPlanEvaluation(id) {
  return request({
    url: '/project/trainingRemember/evaluationInfo',
    method: 'GET',
    params: { trainingId: id }
  })
}

// 根据ids查询人员信息
export function getPersonnelInfoByIds(ids) {
  return request({
    url: '/project/trainingExamPlan/getUsersPreview',
    method: 'GET',
    params: { userIds: ids }
  })
}

export function updateTrainingRecord(payload) {
  return request({
    url: '/project/trainingBaseInfo/updateTrainingBaseInfo',
    method: 'POST',
    data: payload
  })
}

export function getTrainingRecordById(id) {
  return request({
    url: '/project/trainingBaseInfo/getTrainingBaseInfo',
    method: 'GET',
    params: {
      id
    }
  })
}

