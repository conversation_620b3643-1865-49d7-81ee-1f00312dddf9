<template>
  <div class="training-plan-form">
    <div class="title-box">
      <div class="title">
        {{ title }}
      </div>

      <div class="title-actions">
        <el-button type="" @click="handleBack">
          返回
        </el-button>

        <template v-if="mode !== 'view'">
          <el-button
            :loading="isActionButtonLoading"
            type="primary"
            @click="handleSave(false)"
          >
            保存
          </el-button>

          <el-button
            v-if="formData.repetition === CONSTANTS.REPETITION_NONE"
            type="primary"
            :loading="isActionButtonLoading"
            @click="handleSave(true)"
          >
            保存并启用
          </el-button>
        </template>
      </div>
    </div>

    <el-form
      ref="formRef"
      v-loading="isLoading"
      :model="formData"
      :rules="formRules"
      :label-width="labelWidth"
      :style="{ '--label-wdith': labelWidth }"
      :disabled="mode === 'view'"
    >
      <el-row>
        <el-col :span="8">
          <el-form-item label="创建人" required>
            <span v-if="mode === 'view'">
              {{ createUserName }}
            </span>

            <el-input
              v-else
              :value="createUserName"
              placeholder="系统自动生成"
              disabled
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="创建部门" required>
            <span v-if="mode === 'view'">
              {{ createOrgName }}
            </span>

            <el-input
              v-else
              :value="createOrgName"
              placeholder="系统自动生成"
              disabled
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="创建时间" required>
            <span v-if="mode === 'view'">
              {{ createTime }}
            </span>

            <el-input v-else :value="createTime" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="培训名称" prop="trainName">
            <span v-if="mode === 'view'">
              {{ formData.trainName }}
            </span>

            <el-input
              v-else
              v-model="formData.trainName"
              placeholder="请输入培训名称"
              maxlength="30"
              show-word-limit
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="培训类型" prop="trainType">
            <span v-if="mode === 'view'">
              {{ getOptionName("trainingType", formData.trainType, true) }}
            </span>

            <el-select
              v-else
              v-model="formData.trainType"
              placeholder="请选择"
              :disabled="mode === 'edit'"
            >
              <el-option
                v-for="item in trainingTypeOptions"
                :key="item.id"
                :value="item.dictCode"
                :label="item.dictName"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="培训方式" prop="online">
            <span v-if="mode === 'view'">
              {{ getOptionName("trainingMethod", formData.online) }}
            </span>

            <el-select
              v-else
              v-model="formData.online"
              placeholder="请选择"
              :disabled="mode === 'edit'"
              @change="handleOnlineChange"
            >
              <el-option
                v-for="item in trainingMethodOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="培训时间" prop="trainTime">
            <span v-if="mode === 'view'">
              {{ formatTrainingTime }}
            </span>

            <el-date-picker
              v-else
              v-model="trainingTime"
              type="datetimerange"
              start-placeholder="选择开始时间"
              end-placeholder="选择结束时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              :picker-options="trainingTimePickerOptions"
              @change="handleTrainingTimeChange"
            />
          </el-form-item>
        </el-col>

        <template v-if="mode === 'add'">
          <el-col :span="8">
            <el-form-item label="重复" prop="repetition">
              <span v-if="mode === 'view'">
                {{
                  getRepetitionLabel(
                    repetitionOptions.find(
                      (item) => item.value === formData.repetition
                    )
                  )
                }}
              </span>

              <el-select
                v-else
                v-model="formData.repetition"
                placeholder="请选择"
                @change="handleRepetitionChange"
              >
                <el-option
                  v-for="item in repetitionOptions"
                  :key="item.value"
                  :label="getRepetitionLabel(item)"
                  :value="item.value"
                  :disabled="isOptionDisabled(item)"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col
            v-if="formData.repetition !== CONSTANTS.REPETITION_NONE"
            :span="8"
          >
            <el-form-item
              class="repetition-end"
              label="结束于"
              prop="repetitionEnd"
            >
              <template v-if="mode === 'view'">
                <span v-if="formData.repetitionType === 0">
                  {{ formData.repetitions }}次后结束
                </span>

                <span v-else>
                  {{ formData.repetitionEndDate }}
                </span>
              </template>

              <template v-else>
                <el-select
                  v-model="formData.repetitionType"
                  placeholder="请选择"
                  @change="handleRepetitionTypeChange"
                >
                  <el-option
                    v-for="item in repetitionTypeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>

                <el-input-number
                  v-if="formData.repetitionType === 0"
                  v-model="formData.repetitions"
                  :min="1"
                  :controls="false"
                  placeholder="重复次数"
                />

                <el-date-picker
                  v-else
                  v-model="formData.repetitionEndDate"
                  type="date"
                  placeholder="选择结束日期"
                  value-format="yyyy-MM-dd"
                  :picker-options="repetitionDatePickerOptions"
                />
              </template>
            </el-form-item>
          </el-col>
        </template>
      </el-row>

      <el-row>
        <el-col
          v-if="formData.online === CONSTANTS.TRAINING_METHOD_ONLINE"
          :span="8"
        >
          <el-form-item
            key="onlineCourseList"
            label="培训课程"
            prop="onlineCourseList"
          >
            <template v-if="mode === 'view'">
              <div class="course-list view">
                <div
                  v-for="(course, index) in formData.onlineCourseList"
                  :key="index"
                  class="course-item"
                >
                  <span>{{ course.courseName }}</span>
                </div>
              </div>
            </template>

            <div v-else class="course-list">
              <div
                v-for="(course, index) in formData.onlineCourseList"
                :key="index"
                class="course-item"
              >
                <span>{{ course.courseName }}</span>

                <el-button type="text" @click="handleRemoveCourse(index)">
                  删除
                </el-button>
              </div>

              <el-button
                type="primary"
                icon="el-icon-plus"
                @click="handleAddCourse"
              >
                添加课程
              </el-button>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item key="hours" label="培训课时" prop="hours">
            <span v-if="mode === 'view'">
              {{ formData.hours }}
            </span>

            <el-input-number
              v-else
              v-model="formData.hours"
              :controls="false"
              :disabled="formData.online === '1'"
              placeholder="请输入培训课时"
              :min="0"
              :precision="1"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col v-if="formData.online === '2'" :span="8">
          <el-form-item label="培训地点" prop="position">
            <span v-if="mode === 'view'">
              {{ formData.position || "--" }}
            </span>

            <el-input
              v-else
              v-model="formData.position"
              placeholder="请输入培训地点"
              maxlength="30"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="培训形式" prop="trainForm" required>
            <span v-if="mode === 'view'">
              {{ getOptionName("trainingForm", formData.trainForm) }}
            </span>

            <el-select
              v-else
              v-model="formData.trainForm"
              placeholder="请选择"
              @change="handleTrainingFormChange"
            >
              <el-option
                v-for="item in trainingFormOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item
            v-if="formData.trainForm === CONSTANTS.TRAINING_FORM_INTERNAL"
            key="teacher"
            label="培训讲师"
            prop="teacher"
          >
            <PersonSelect
              ref="teacherSelectRef"
              v-model="formData.teacher"
              :api="personSelectorApi"
              reappear
              tag-visible
              multiple
              @hook:mounted="handleTeacherSelectMounted"
            />
          </el-form-item>

          <el-form-item
            v-else
            key="outerTeacher"
            label="培训讲师"
            prop="outTeacher"
          >
            <span v-if="mode === 'view'">
              {{ formData.outTeacher || "--" }}
            </span>

            <el-input
              v-else
              v-model="formData.outTeacher"
              placeholder="请输入培训讲师"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="人员范围" prop="personnelScope">
            <span v-show="mode === 'view'">
              {{ getOptionName("personnelScope", formData.personnelScope) }}
            </span>

            <el-select
              v-show="mode !== 'view'"
              v-model="formData.personnelScope"
              placeholder="请选择"
            >
              <el-option
                v-for="item in personnelScopeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item
            v-if="mode !== 'view'"
            label="培训人员"
            prop="userIdList"
          >
            <PersonSelect
              ref="traineeSelectRef"
              v-model="userIdList"
              :api="personSelectorApi"
              :reappear="true"
              :tag-visible="true"
              multiple
              @hook:mounted="handleTraineeSelectMounted"
              @handleSelectData="handleTraineeSelected"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="培训人员预览">
            <div v-if="mode !== 'view'" class="table-actions">
              <el-button type="primary" @click="handleDownloadTemplate">
                下载模板
              </el-button>

              <el-button
                type="primary"
                :loading="isImportLoading"
                @click="handleImport"
              >
                导入
              </el-button>

              <input
                ref="importInputRef"
                style="display: none"
                type="file"
                accept=".xls,.xlsx"
                @change="handleFilePicked"
              >
            </div>

            <el-table
              v-loading="isPreviewLoading"
              border
              style="width: 100%"
              :data="formData.remembers"
            >
              <el-table-column prop="userName" label="姓名" />

              <el-table-column prop="tel" label="手机号" />

              <el-table-column prop="orgName" label="部门" />
            </el-table>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <span v-if="mode === 'view'">
              {{ formData.remark || "--" }}
            </span>

            <el-input
              v-else
              v-model="formData.remark"
              type="textarea"
              placeholder="请输入备注信息"
              maxlength="200"
              show-word-limit
              :rows="3"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="formData.repetition === CONSTANTS.REPETITION_NONE">
        <el-col :span="8">
          <el-form-item label="是否考试" prop="examType">
            <span v-if="mode === 'view'">
              {{ getOptionName("hasExam", formData.examType) }}
            </span>

            <el-radio-group
              v-else
              v-model="formData.examType"
              @change="handleExamTypeChange"
            >
              <el-radio
                v-for="item in hasExamOptions"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <template
        v-if="
          formData.examType === CONSTANTS.HAS_EXAM &&
            formData.repetition === CONSTANTS.REPETITION_NONE
        "
      >
        <el-row>
          <el-col :span="8">
            <el-form-item
              label="考试名称"
              prop="trainingExamPlanRequest.examName"
            >
              <span v-if="mode === 'view'">
                {{ formData.trainingExamPlanRequest.examName || "--" }}
              </span>

              <el-input
                v-else
                v-model="formData.trainingExamPlanRequest.examName"
                maxlength="30"
                show-word-limit
                placeholder="请输入考试名称"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="考试类型" prop="trainingExamPlanRequest.examType">
              <span v-if="mode === 'view'">
                {{ getOptionName("examType", formData.trainingExamPlanRequest.examType, true) }}
              </span>

              <el-select
                v-else
                v-model="formData.trainingExamPlanRequest.examType"
                style="width: 100%;"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in examTypeOptions"
                  :key="item.dictCode"
                  :label="item.dictName"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item
              label="考试方式"
              prop="trainingExamPlanRequest.examMode"
            >
              <span v-if="mode === 'view'">
                {{
                  getOptionName(
                    "examMode",
                    formData.trainingExamPlanRequest.examMode
                  )
                }}
              </span>

              <el-select
                v-else
                v-model="formData.trainingExamPlanRequest.examMode"
                placeholder="请选择"
                @change="handleExamModeChange"
              >
                <el-option
                  v-for="item in examModeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item
              label="选择试卷"
              prop="trainingExamPlanRequest.paperId"
              :required="isExamPaperRequired"
            >
              <div style="display: flex; gap: 6px">
                <el-input
                  v-model="formData.trainingExamPlanRequest.paperName"
                  maxlength="30"
                  show-word-limit
                  placeholder="请选择试卷"
                  disabled
                />

                <el-button
                  v-if="mode === 'add' || mode === 'edit'"
                  type="primary"
                  @click="showSelectPaper"
                >
                  选择试卷
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row
          v-if="
            formData.trainingExamPlanRequest.examMode ===
              CONSTANTS.EXAM_MODE_OFFLINE
          "
        >
          <el-col :span="8">
            <el-form-item
              label="考试地点"
              prop="trainingExamPlanRequest.examLocation"
            >
              <span v-if="mode === 'view'">
                {{ formData.trainingExamPlanRequest.examLocation || "--" }}
              </span>

              <el-input
                v-else
                v-model="formData.trainingExamPlanRequest.examLocation"
                placeholder="请输入考试地点"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="考试时间" prop="examTime">
              <span v-if="mode === 'view'">
                {{ formatExamTime }}
              </span>

              <el-date-picker
                v-else
                v-model="examTime"
                type="datetimerange"
                start-placeholder="考试开始时间"
                end-placeholder="考试结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="examTimePickerOptions"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item
              label="限时 (分钟)"
              prop="trainingExamPlanRequest.examTime"
            >
              <span v-if="mode === 'view'">
                {{ formData.trainingExamPlanRequest.examTime || "--" }}分钟
              </span>

              <el-input-number
                v-else
                v-model="formData.trainingExamPlanRequest.examTime"
                placeholder="考试时长"
                :min="0"
                :max="1000"
                :controls="false"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item
              label="及格线"
              prop="trainingExamPlanRequest.passScore"
            >
              <span v-if="mode === 'view'">
                {{ formData.trainingExamPlanRequest.passScore || "--" }}分
              </span>

              <el-input-number
                v-else
                v-model="formData.trainingExamPlanRequest.passScore"
                placeholder="及格分数"
                :min="0"
                :controls="false"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item
              label="限制次数"
              prop="trainingExamPlanRequest.limitTimes"
            >
              <span v-if="mode === 'view'">
                {{ formData.trainingExamPlanRequest.limitTimes || "--" }}次
              </span>

              <el-input-number
                v-else
                v-model="formData.trainingExamPlanRequest.limitTimes"
                placeholder="考试次数"
                :min="1"
                :max="1000"
                :controls="false"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item
              label="考试备注"
              prop="trainingExamPlanRequest.remark"
            >
              <span v-if="mode === 'view'">
                {{ formData.trainingExamPlanRequest.remark || "--" }}
              </span>

              <el-input
                v-else
                v-model="formData.trainingExamPlanRequest.remark"
                type="textarea"
                placeholder="请输入考试备注"
                maxlength="200"
                show-word-limit
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>

    <CoursePickerModal
      append-to-body
      :disabled-question-list="disabledQuestionList"
      :visible.sync="isCourseModalVisible"
      @picked="handleCoursePicked"
      @closed="handleCoursePickerModalClosed"
    />

    <PaperSelectDialog
      ref="paperSelectDialogRef"
      @selectPaper="handleSelectPaper"
    />
  </div>
</template>

<script>
// TODO: 考试时间时间禁用状态
import omit from 'lodash/omit'
import uniq from 'lodash/uniq'
import dayjs from 'dayjs'
import weekOfYear from 'dayjs/plugin/weekOfYear'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
// 扩展dayjs功能
dayjs.extend(weekOfYear)
dayjs.extend(quarterOfYear)
dayjs.extend(isSameOrBefore)

import {
  // 选项数组
  TRAINING_METHOD_OPTIONS,
  TRAINING_FORM_OPTIONS,
  REPETITION_OPTIONS,
  REPETITION_TYPE_OPTIONS,
  IS_ENABLED_OPTIONS,
  HAS_EXAM_OPTIONS,
  EXAM_MODE_OPTIONS,
  PERSONNEL_SCOPE_OPTIONS,
  // 常量值对象
  CONSTANTS
} from '@/constants/training-plan'
import orgApi from '@/framework/api/userCenter/hrOrganization'
import CoursePickerModal from './course-picker-modal.vue'
import PaperSelectDialog from '@/components/paper-select'

import {
  getManagingOrgUserList,
  searchUserName,
  getUserListByUserIds,
  searchOrgName
} from '@/framework/api/affairManage/flowDetail/Config'

import {
  downloadPersonnelTemplate,
  importPersonnel,
  editTrainingPlan,
  addTrainingPlan,
  getTrainingPlanById,
  getPersonnelInfoByIds
} from '@/api/learning-manage/training-plan'

import { getTotalScore } from '@/utils/paper'
import {
  getTrainingExamMainPaperDetail
} from '@/api/exam-manage/paper-manage'

export default {
  name: 'TrainingPlanForm',
  components: { CoursePickerModal, PaperSelectDialog },
  props: {
    mode: {
      type: String,
      default: 'add',
      validator: (value) => ['add', 'view', 'edit'].includes(value)
    },

    trainingPlanId: {
      type: String,
      default: ''
    }
  },

  data() {
    const trainingTimeValidator = (rule, value, callback) => {
      if (this.formData.trainBegin && this.formData.trainEnd) {
        if (this.formData.online === CONSTANTS.TRAINING_METHOD_ONLINE) {
          const now = dayjs()
          const beginTime = dayjs(this.formData.trainBegin)

          if (beginTime.isBefore(now)) {
            callback(new Error('培训开始时间必须晚于当前时间'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      } else {
        callback(new Error('请选择培训时间'))
      }
    }

    const examTimeValidator = (rule, value, callback) => {
      if (
        this.formData.trainingExamPlanRequest.examBeginTime &&
        this.formData.trainingExamPlanRequest.examEndTime
      ) {
        if (this.formData.online === CONSTANTS.TRAINING_METHOD_ONLINE) {
          const now = dayjs()
          const beginTime = dayjs(this.formData.trainingExamPlanRequest.examBeginTime)

          if (beginTime.isBefore(now)) {
            callback(new Error('考试开始时间必须晚于当前时间'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      } else {
        callback('请选择考试时间')
      }
    }

    const trainingEndValidator = (rule, value, callback) => {
      if (this.formData.repetitionType === CONSTANTS.REPETITION_TYPE_COUNT) {
        // 如果是限定次数，检查次数是否有效
        if (!this.formData.repetitions || this.formData.repetitions < 1) {
          callback(new Error('请输入有效的重复次数'))
        } else {
          callback()
        }
        return
      }

      // 如果是某天结束，检查结束日期是否晚于培训结束时间
      if (!this.formData.repetitionEndDate) {
        callback(new Error('请选择重复结束日期'))
      } else if (
        this.formData.trainEnd &&
        dayjs(this.formData.repetitionEndDate).isSameOrBefore(
          dayjs(this.formData.trainEnd)
        )
      ) {
        callback(new Error('重复结束日期必须晚于培训结束时间'))
      } else {
        callback()
      }
    }

    return {
      CONSTANTS,
      labelWidth: '110px',
      isPaperModalVisible: false,
      isCourseModalVisible: false,
      isImportLoading: false,
      isPreviewLoading: false,
      paperTotalScore: 0,

      trainingTypeOptions: [],
      trainingMethodOptions: TRAINING_METHOD_OPTIONS,
      trainingFormOptions: TRAINING_FORM_OPTIONS,
      repetitionOptions: REPETITION_OPTIONS,
      repetitionTypeOptions: REPETITION_TYPE_OPTIONS,
      isEnabledOptions: IS_ENABLED_OPTIONS,
      hasExamOptions: HAS_EXAM_OPTIONS,
      examModeOptions: EXAM_MODE_OPTIONS,
      personnelScopeOptions: PERSONNEL_SCOPE_OPTIONS,
      examTypeOptions: [],
      orgName: '',
      isSaving: false,
      isLoading: false,

      formData: {
        id: '',
        createTime: '',
        createUser: '',
        updateTime: '',
        updateUser: '',
        createName: '',
        orgName: '',
        ownerOrgId: null,
        trainName: '',
        trainType: '',
        online: CONSTANTS.TRAINING_METHOD_ONLINE, // 默认线上培训，对应 TRAINING_METHOD_OPTIONS
        trainBegin: '',
        trainEnd: '',
        // 重复类型
        repetition: CONSTANTS.REPETITION_NONE, // 默认不重复，对应 REPETITION_OPTIONS
        repetitionType: CONSTANTS.REPETITION_TYPE_DATE, // 默认某天结束，对应 REPETITION_TYPE_OPTIONS
        // 重复次数
        repetitions: '',
        // 重复结束日期
        repetitionEndDate: '',
        // 培训课程
        onlineCourseList: [],
        // 培训课时
        hours: 0,
        // 培训地点
        position: '',
        // 培训形式
        trainForm: CONSTANTS.TRAINING_FORM_INTERNAL,
        // 培训老师（内培时使用）
        teacher: '',
        // 外培老师
        outTeacher: '',
        // 人员范围
        personnelScope: '',
        // 培训人员
        userIdList: [],
        // 培训人员预览
        remembers: [],
        // 备注
        remark: '',
        // 是否考试
        examType: CONSTANTS.HAS_EXAM, // 默认考试
        status: CONSTANTS.STATUS_DISABLED, // 默认停用
        trainingExamPlanRequest: {
          // 选择试卷
          paperId: '',
          // 试卷名称
          paperName: '',
          // 考试名称
          examName: '',
          // 考试类型
          examType: '',
          // 考试方式
          examMode: CONSTANTS.EXAM_MODE_ONLINE, // 默认线上考试
          // 考试开始时间
          examBeginTime: '',
          // 考试结束时间
          examEndTime: '',
          // 限时
          examTime: '', // 考试时长，单位分钟
          examLocation: '',
          // 及格线
          passScore: '',
          // 限制次数
          limitTimes: 1,
          // 备注
          remark: ''
        }
      },

      formRules: {
        trainName: [
          { required: true, message: '请输入培训名称', trigger: 'blur' }
        ],

        trainType: [
          {
            required: true,
            message: '请选择培训类型',
            trigger: ['change', 'blur']
          }
        ],

        online: [
          {
            required: true,
            message: '请选择培训方式',
            trigger: ['change', 'blur']
          }
        ],

        trainTime: [
          { required: true, validator: trainingTimeValidator, trigger: 'blur' }
        ],

        repetition: [
          {
            required: true,
            message: '请选择重复类型',
            trigger: ['change', 'blur']
          }
        ],

        repetitionEnd: [
          {
            required: true,
            message: '请选择重复结束于何时',
            trigger: ['change', 'blur'],
            validator: trainingEndValidator
          }
        ],

        onlineCourseList: [
          {
            required: true,
            type: 'array',
            message: '请选择培训课程',
            trigger: 'change',
            min: 1
          }
        ],

        hours: [{ required: true, message: '请输入培训课时', trigger: 'blur' }],

        personnelScope: [
          {
            required: true,
            message: '请选择人员范围',
            trigger: ['change', 'blur']
          }
        ],

        trainForm: [
          {
            required: true,
            message: '请选择培训形式',
            trigger: ['change', 'blur']
          }
        ],

        teacher: [
          {
            required: true,
            message: '请选择培训讲师',
            trigger: ['change', 'blur']
          }
        ],

        outTeacher: [
          {
            required: true,
            message: '请输入培训讲师',
            trigger: ['change', 'blur']
          }
        ],

        userIdList: [
          {
            required: true,
            message: '请选择培训人员',
            trigger: ['change', 'blur']
          }
        ],

        examType: [
          { required: true, message: '请选择是否考试', trigger: ['change'] }
        ],

        'trainingExamPlanRequest.examType': [
          {
            required: true, message: '请选择考试类型', trigger: ['change']
          }
        ],

        'trainingExamPlanRequest.paperId': [
          {
            validator: (rule, value, callback) => {
              if (this.formData.examType === CONSTANTS.HAS_EXAM) {
                if (!value) {
                  callback(new Error('请选择试卷'))
                } else {
                  callback()
                }
              } else {
                callback()
              }
            },

            trigger: ['change', 'blur']
          }
        ],

        'trainingExamPlanRequest.examName': [
          { required: true, message: '请输入考试名称', trigger: 'blur' }
        ],

        'trainingExamPlanRequest.examMode': [
          {
            required: true,
            message: '请选择考核方式',
            trigger: ['change', 'blur']
          }
        ],

        'trainingExamPlanRequest.examLocation': [
          { required: true, message: '请输入考试地点', trigger: 'blur' }
        ],

        examTime: [
          {
            required: true,
            validator: examTimeValidator,

            trigger: 'blur'
          }
        ],

        'trainingExamPlanRequest.examTime': [
          { required: true, message: '请输入限时', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                callback('请输入有效限时')
              } else {
                callback()
              }
            },

            trigger: 'blur'
          }
        ],

        'trainingExamPlanRequest.passScore': [
          { required: true, message: '请输入及格线', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                callback('请输入有效及格线')
              } else if (value > this.paperTotalScore) {
                callback(`及格线不能大于试卷总分数：${this.paperTotalScore}`)
              } else {
                callback()
              }
            },

            trigger: 'blur'
          }
        ],

        'trainingExamPlanRequest.limitTimes': [
          { required: true, message: '请输入限制次数', trigger: 'blur' },
          {
            validator: (rlue, value, callback) => {
              if (value <= 0) {
                callback('请输入有效限制次数')
              } else {
                callback()
              }
            }
          }
        ]
      },

      personSelectorApi: {
        getManagingOrgUserList,
        searchUserName,
        getUserListByUserIds,
        searchOrgName
      }
    }
  },

  computed: {
    isActionButtonLoading() {
      return this.isSaving || this.isImportLoading || this.isLoading
    },

    title() {
      const map = {
        add: '新增',
        edit: '编辑',
        view: '详情'
      }
      return map[this.mode]
    },

    createTime() {
      return this.formData.createTime || '由系统自动生成'
    },

    createUserName() {
      return this.formData.createName || this.$store.getters.name
    },

    createOrgName() {
      return this.formData.orgName || this.orgName
    },

    trainingTime: {
      get() {
        if (this.formData.trainBegin && this.formData.trainEnd) {
          return [this.formData.trainBegin, this.formData.trainEnd]
        }
        return []
      },

      set(value) {
        if (value && value.length === 2) {
          this.formData.trainBegin = value[0]
          this.formData.trainEnd = value[1]
        } else {
          this.formData.trainBegin = ''
          this.formData.trainEnd = ''
        }
      }
    },

    examTime: {
      get() {
        if (
          this.formData.trainingExamPlanRequest.examBeginTime &&
          this.formData.trainingExamPlanRequest.examEndTime
        ) {
          return [
            this.formData.trainingExamPlanRequest.examBeginTime,
            this.formData.trainingExamPlanRequest.examEndTime
          ]
        }
        return []
      },

      set(value) {
        if (value && value.length === 2) {
          this.formData.trainingExamPlanRequest.examBeginTime = value[0]
          this.formData.trainingExamPlanRequest.examEndTime = value[1]
        } else {
          this.formData.trainingExamPlanRequest.examBeginTime = ''
          this.formData.trainingExamPlanRequest.examEndTime = ''
        }
      }
    },

    repetitionDatePickerOptions() {
      return {
        disabledDate: (time) => {
          // 只要求不能早于培训结束时间
          if (!this.formData.trainEnd) {
            return false
          }

          const trainEndDate = dayjs(this.formData.trainEnd).startOf('day')
          const currentDate = dayjs(time).startOf('day')

          // 不能早于或等于培训结束时间
          return currentDate.isSameOrBefore(trainEndDate)
        }
      }
    },

    // 培训时间选择器限制选项
    trainingTimePickerOptions() {
      return {
        disabledDate: (time) => {
          // 线上培训：只能选择当前时间之后的时间
          if (this.formData.online === CONSTANTS.TRAINING_METHOD_ONLINE) {
            const now = dayjs()
            const currentDate = dayjs(time)
            return currentDate.isBefore(now, 'day')
          }
          // 线下培训：不做时间限制
          return false
        }
      }
    },

    // 考试时间选择器限制选项
    examTimePickerOptions() {
      return {
        disabledDate: (time) => {
          // 线上考试：只能选择当前时间之后的时间
          if (this.formData.trainingExamPlanRequest.examMode === CONSTANTS.EXAM_MODE_ONLINE) {
            const now = dayjs()
            const currentDate = dayjs(time)
            return currentDate.isBefore(now, 'day')
          }
          // 线下考试：不做时间限制
          return false
        }
      }
    },

    isExamPaperRequired() {
      return (
        this.formData.trainingExamPlanRequest.examMode ===
        CONSTANTS.EXAM_MODE_ONLINE
      )
    },

    disabledQuestionList() {
      return (this.formData.onlineCourseList || []).map(
        (item) => item.courseId
      )
    },

    userIdList: {
      get() {
        return this.formData.userIdList.filter((item) => !!item).join(',')
      },

      set(val) {
        this.formData.userIdList = val.split(',').filter((item) => !!item)
      }
    },

    formatExamTime() {
      const { examBeginTime, examEndTime } = this.formData.trainingExamPlanRequest
      if (!examBeginTime || !examEndTime) return '--'

      const start = dayjs(examBeginTime).format('YYYY-MM-DD HH:mm:ss')
      const end = dayjs(examEndTime).format('YYYY-MM-DD HH:mm:ss')

      return `${start} ~ ${end}`
    },

    formatTrainingTime() {
      const { trainBegin, trainEnd } = this.formData
      if (!trainBegin || !trainEnd) return '--'

      const start = dayjs(trainBegin).format('YYYY-MM-DD HH:mm:ss')
      const end = dayjs(trainEnd).format('YYYY-MM-DD HH:mm:ss')
      return `${start} ~ ${end}`
    }
  },

  watch: {
    ['formData.repetition'](newVal, oldVal) {
      if (newVal !== oldVal && oldVal === CONSTANTS.REPETITION_NONE) {
        this.resetExamFields()
      }
    }
  },

  created() {
    this.getOrgName()
    this.getDicts()
    this.initFormData()
  },

  methods: {
    resetExamFields() {
      this.formData.examType = CONSTANTS.NO_EXAM
      this.formData.trainingExamPlanRequest = {
        paperId: '',
        paperName: '',
        examName: '',
        examMode: CONSTANTS.EXAM_MODE_ONLINE,
        examBeginTime: '',
        examEndTime: '',
        examTime: '',
        examLocation: '',
        passScore: '',
        limitTimes: 1,
        remark: ''
      }
    },

    handleExamTypeChange() {
      this.formData.trainingExamPlanRequest = {
        paperId: '',
        paperName: '',
        examName: '',
        examMode: CONSTANTS.EXAM_MODE_ONLINE,
        examBeginTime: '',
        examEndTime: '',
        examTime: '',
        examLocation: '',
        passScore: '',
        limitTimes: 1,
        remark: ''
      }
    },

    handleExamModeChange() {
      this.formData.trainingExamPlanRequest.examLocation = ''
    },

    handleRepetitionTypeChange() {
      this.formData.repetitions = 1
      this.formData.repetitionEndDate = ''
    },

    handleRepetitionChange() {
      this.formData.repetitions = 1
      this.formData.repetitionEndDate = ''
      this.formData.repetitionType = CONSTANTS.REPETITION_TYPE_DATE
    },

    handleTrainingFormChange() {
      this.formData.outTeacher = ''
      this.formData.teacher = ''
      this.formData.onlineCourseList = []
    },

    handleOnlineChange() {
      this.formData.hours = 0
      this.formData.position = ''
      this.formData.onlineCourseList = []

      // 培训方式改编后，清空培训时间和考试时间
      this.formData.trainBegin = ''
      this.formData.trainEnd = ''
      this.handleTrainingTimeChange()
      this.formData.trainingExamPlanRequest.examBeginTime = ''
      this.formData.trainingExamPlanRequest.examBeginEnd = ''
    },

    handleTrainingTimeChange() {
      this.formData.repetition = CONSTANTS.REPETITION_NONE
      this.formData.repetitionType = CONSTANTS.REPETITION_TYPE_DATE
      this.formData.repetitions = ''
      this.formData.repetitionEndDate = ''
    },

    getRepetitionLabel(item) {
      let label
      const weekmap = ['日', '一', '二', '三', '四', '五', '六'].map(
        (label) => `周${label}`
      )

      if (!this.formData.trainBegin) {
        label = item.label
      } else if (item.value === CONSTANTS.REPETITION_NONE) {
        label = '不重复'
      } else if (item.value === CONSTANTS.REPETITION_WEEKLY) {
        label = `${item.label}（${
          weekmap[dayjs(this.formData.trainBegin).day()]
        }）`
      } else {
        label = `${item.label}（${dayjs(this.formData.trainBegin).date()}日）`
      }
      return label
    },

    getDicts() {
      this.businessDictList({ dictTypeCode: 'trainType' }).then((res) => {
        this.trainingTypeOptions = res.data.rows
      })
      this.businessDictList({ dictTypeCode: 'examType' }).then((res) => {
        this.examTypeOptions = res.data.rows
      })
    },

    initFormData() {
      if (this.mode === 'add') {
        // 新增模式，设置默认值
      } else if (this.mode === 'edit' || this.mode === 'view') {
        // 编辑或查看模式，根据id获取详情
        if (this.trainingPlanId) {
          this.getDetail(this.trainingPlanId)
        } else {
          this.$message.error('获取数据失败')
        }
      }
    },

    adaptResponseData(data) {
      const result = {
        ...data,
        userIdList: data.userIdList || [],
        remembers: data.remembers || [],
        trainingExamPlanRequest: data.trainingExamPlanRequest || {
          paperId: '',
          paperName: '',
          examName: '',
          examMode: '',
          examBeginTime: '',
          examEndTime: '',
          examTime: '',
          examLocation: '',
          passScore: '',
          limitTimes: 1,
          remark: ''
        }
      }
      return result
    },

    getDetail(id) {
      this.isLoading = true
      getTrainingPlanById(id)
        .then((res) => {
          this.formData = this.adaptResponseData(res.data)

          if (res.data.trainingExamPlanRequest) {
            getTrainingExamMainPaperDetail({ id: res.data.trainingExamPlanRequest.paperId }).then((res) => {
              this.paperTotalScore = getTotalScore(res.data)
            })
          }

          this.$nextTick().then(() => {
            this.$refs.formRef.clearValidate()
          })
        })
        .catch((err) => {
          this.$message.error('获取详情失败')
          console.error(err)
        })
        .finally(() => {
          this.isLoading = false
        })
    },

    handleBack() {
      this.$emit('back', false)
    },

    handleSave(enable) {
      let payload = JSON.parse(JSON.stringify(this.formData))
      const omitFields = [
        'createTime',
        'createName',
        'remembers',
        'updateTime',
        'createUser',
        'updateUser'
      ]
      if (enable) {
        payload.status = CONSTANTS.STATUS_ENABLED
      }
      if (this.mode === 'add') {
        payload = omit(payload, ['id', ...omitFields])
      } else if (this.mode === 'edit') {
        payload = omit(payload, omitFields)
      }

      const api = this.mode === 'add' ? addTrainingPlan : editTrainingPlan

      this.isSaving = true
      this.$refs.formRef
        .validate()
        .then(() => {
          return api(payload)
        })
        .then((res) => {
          this.$emit('back', true)
          this.$message.success('保存成功')
        })
        .catch((err) => {
          if (err instanceof Error) {
            this.$message.error('保存失败')
            console.log(err)
          }
        })
        .finally(() => {
          this.isSaving = false
        })
    },

    isOptionDisabled(item) {
      // 如果没有选择培训时间，所有选项都可用
      if (!this.formData.trainBegin || !this.formData.trainEnd) {
        return false
      }

      // "不重复"选项永远可用
      if (item.value === CONSTANTS.REPETITION_NONE) {
        return false
      }

      const startTime = dayjs(this.formData.trainBegin)
      const endTime = dayjs(this.formData.trainEnd)

      switch (item.value) {
        case CONSTANTS.REPETITION_WEEKLY: // 每周
          return !(
            startTime.week() === endTime.week() &&
            startTime.year() === endTime.year()
          )
        case CONSTANTS.REPETITION_MONTHLY: // 每月
          return !(
            startTime.month() === endTime.month() &&
            startTime.year() === endTime.year()
          )
        case CONSTANTS.REPETITION_QUARTERLY: // 每季度
          return !(
            startTime.quarter() === endTime.quarter() &&
            startTime.year() === endTime.year()
          )
        case CONSTANTS.REPETITION_YEARLY: // 每年
          return !(startTime.year() === endTime.year())
        default:
          return false
      }
    },

    getOrgName() {
      orgApi.detail(this.$store.getters.orgId).then((res) => {
        this.orgName = res.data.orgName
      })
    },

    handleAddCourse() {
      this.isCourseModalVisible = true
    },

    handleRemoveCourse(index) {
      this.formData.onlineCourseList.splice(index, 1)
      console.log('this.formData.onlineCourseList,', this.formData.onlineCourseList)
      console.log('length', this.formData.onlineCourseList.length)
      this.$nextTick().then(() => {
        this.formData.hours = this.formData.onlineCourseList.reduce(
          (hours, current) => {
            return hours + current.classHour
          },
          0
        )
        this.$refs.formRef.validateField('onlineCourseList')
      })
    },

    handleCoursePicked(list) {
      console.log(list)
      this.formData.onlineCourseList.push(
        ...list.map((course) => ({
          courseId: course.id,
          courseName: course.videoName,
          classHour: course.classHour
        }))
      )

      this.formData.hours =
        this.formData.hours +
        list.reduce((hours, current) => {
          return hours + current.classHour
        }, 0)
    },

    handleCoursePickerModalClosed() {
      this.$nextTick().then(() => {
        this.$refs.formRef.validateField('onlineCourseList')
      })
    },

    getFilenameFromHeaders(res, defaultName) {
      const disposition = res.headers['content-disposition']
      if (disposition) {
        const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
        const matches = filenameRegex.exec(disposition)
        if (matches != null && matches[1]) {
          return decodeURIComponent(matches[1].replace(/['"]/g, ''))
        }
      }
    },

    handleDownloadTemplate() {
      downloadPersonnelTemplate().then((res) => {
        const fileName =
          this.getFilenameFromHeaders(res) || '培训人员导入模板.xlsx'
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(res.data)
        link.download = fileName
        link.click()
        window.URL.revokeObjectURL(link.href)
      })
    },

    handleImport() {
      this.$refs.importInputRef.click()
    },

    handleFilePicked(event) {
      const file = event.target.files[0]
      if (!file) return
      const formData = new FormData()
      formData.append('file', file)
      this.isImportLoading = true

      importPersonnel(formData)
        .then((res) => {
          // 获取新导入的用户ID列表
          const newUserIds = res.data.map((item) => item.id)

          // 合并并去重userIdList
          const mergedUserIds = uniq([...this.formData.userIdList, ...newUserIds])
          this.formData.userIdList = mergedUserIds

          // 调用获取人员信息接口更新remembers
          return this.getPersonnelPreview(mergedUserIds)
        })
        .then((res) => {
          // 更新remembers字段，转换API返回的数据格式
          this.formData.remembers = res
          this.$message.success('导入成功')
        })
        .catch((err) => {
          this.$message.error('导入失败')
          console.log(err)
        })
        .finally(() => {
          this.isImportLoading = false
          this.$refs.importInputRef.value = ''
        })
    },

    handlePickPaper() {
      this.isPaperModalVisible = true
    },

    getOptionName(name, value, isDict = false) {
      let options = []
      switch (name) {
        case 'trainingType':
          options = this.trainingTypeOptions
          break
        case 'trainingMethod':
          options = this.trainingMethodOptions
          break
        case 'trainingForm':
          options = this.trainingFormOptions
          break
        case 'personnelScope':
          options = this.personnelScopeOptions
          break
        case 'hasExam':
          options = this.hasExamOptions
          break
        case 'examMode':
          options = this.examModeOptions
          break
        case 'examType':
          options = this.examTypeOptions
          break
        default:
          break
      }

      if (isDict) {
        return (
          options.find((item) => item.dictCode === value)?.dictName || '--'
        )
      }
      return options.find((item) => item.value === value)?.label || '--'
    },

    handleTeacherSelectMounted() {
      this.$refs.teacherSelectRef.maxNum = 999999
    },

    handleTraineeSelectMounted() {
      this.$refs.traineeSelectRef.maxNum = 999999
    },

    handleTraineeSelected(users) {
      if (!users || users.length === 0) {
        this.formData.remembers = []
        return
      }

      // 获取所有选中用户的ID
      const userIds = users.map((user) => user.userId)

      // 调用接口获取人员详细信息
      this.getPersonnelPreview(userIds)
        .then((res) => {
          this.formData.remembers = res
        })
        .catch((err) => {
          console.error('获取人员信息失败:', err)
          this.$message.error('获取人员信息失败')
        })
    },

    getPersonnelPreview(ids) {
      this.isPreviewLoading = true
      return getPersonnelInfoByIds(`${ids.join(',')}`)
        .then((res) => {
          return res.data.map((item) => ({
            id: item.userId,
            userName: item.userName,
            tel: item.userPhone,
            orgName: item.userDpt
          }))
        })
        .catch((err) => {
          console.error('获取人员信息失败:', err)
          this.$message.error('获取人员信息失败')
        })
        .finally(() => {
          this.isPreviewLoading = false
        })
    },

    handleSelectPaper(paper) {
      this.formData.trainingExamPlanRequest.paperId = paper.id
      this.formData.trainingExamPlanRequest.paperName = paper.paperName
      this.formData.trainingExamPlanRequest.passScore = 0
      this.paperTotalScore = getTotalScore(paper)
    },

    showSelectPaper() {
      this.$refs.paperSelectDialogRef.init()
    }
  }
}
</script>

<style lang="scss" scoped>
.training-plan-form {
  padding: 20px;
  background-color: #fff;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;

    .title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .title-actions {
      .el-button {
        margin-left: 10px;
      }
    }
  }

  .course-list {
    &.view {
      .course-item {
        padding: 8px 12px;
        margin-bottom: 8px;
        background-color: #f5f7fa;
        border-radius: 4px;
        color: #303133;
      }
    }

    .course-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      margin-bottom: 8px;
      background-color: #f5f7fa;
      border-radius: 4px;

      span {
        color: #303133;
      }
    }
  }

  .table-actions {
    margin-bottom: 10px;
    text-align: right;

    .el-button {
      margin-left: 10px;
    }
  }
}

::v-deep .el-form-item__content {
  > :is(.el-input, .el-select, .el-input-number, .el-date-editor) {
    width: 100%;

    .el-input__inner {
      text-align: left;
    }
  }
}

.repetition-end ::v-deep .el-form-item__content {
  --select-width: 120px;

  .el-select {
    width: var(--select-width);
  }

  :is(.el-input-number, .el-date-editor) {
    width: calc(100% - var(--select-width));
  }
}

::v-deep .el-radio-group {
  .el-radio {
    margin-right: 20px;
  }
}

::v-deep .el-table {
  margin-bottom: 10px;
}
</style>
