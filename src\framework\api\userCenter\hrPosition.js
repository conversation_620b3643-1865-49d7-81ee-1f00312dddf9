import { default as request, cloud } from '@/framework/utils/request'

export default ({
  fetchList(params) {
    return request({
      url: `${cloud.usercenter}/hrPosition/list`,
      method: 'get',
      params
    })
  },
  fetchPage(params) {
    return request({
      url: `${cloud.usercenter}/hrPosition/page`,
      method: 'get',
      params
    })
  },
  updateStatus(data) {
    return request({
      url: `${cloud.usercenter}/hrPosition/updateStatus`,
      method: 'POST',
      data
    })
  },
  detail(id) {
    return request({
      url: `${cloud.usercenter}/hrPosition/detail`,
      method: 'get',
      params: {
        positionId: id
      }
    })
  },
  edit(data) {
    return request({
      url: `${cloud.usercenter}/hrPosition/edit`,
      method: 'POST',
      data
    })
  },
  add(data) {
    return request({
      url: `${cloud.usercenter}/hrPosition/add`,
      method: 'POST',
      data
    })
  },
  delete(data) {
    return request({
      url: `${cloud.usercenter}/hrPosition/delete`,
      method: 'post',
      data
    })
  },
  exportPostion(params) {
    return request({
      url: `${cloud.usercenter}/hrPosition/exportPostion`,
      method: 'get',
      params,
      responseType: 'arraybuffer'
    })
  }

})
