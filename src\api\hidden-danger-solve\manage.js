/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-20 16:43:48
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-05-23 15:33:34
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud}  from '@/framework/utils/request'

// 新增业务-隐患管理数据
export function add(data) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/add',
    method: 'post',
    data: data
  })
}

// 新增业务-隐患验收记录数据
export function addCheck(data) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/addCheck',
    method: 'post',
    data: data
  })
}

// 新增业务-隐患整改记录数据
export function addRectify(data) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/addRectify',
    method: 'post',
    data: data
  })
}

// 隐患变更
export function alertDanger(data) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/alert',
    method: 'post',
    data: data
  })
}

// 删除业务-隐患管理数据
export function del(id) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 获取业务-隐患管理单条数据详情
export function getDetail(id) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/detail?id=' + id,
    method: 'get'
  })
}

// 修改业务-隐患管理数据
export function edit(data) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/edit',
    method: 'post',
    data: data
  })
}

// 获取相关所有图片信息
export function getImageInfo(query) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/imageInfo',
    method: 'get',
    params: query
  })
}

// 分页查询业务-我的隐患、 作为整改 作为验收 作为上报
export function getOwnList(query) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/own/page',
    method: 'get',
    params: query
  })
}

// 分页查询业务-隐患管理所有数据
export function getList(query) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/page',
    method: 'get',
    params: query
  })
}

// 新增业务-隐患延期记录数据
export function postpone(data) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/postpone',
    method: 'post',
    data: data
  })
}

// 新增业务-催办消息通知数据
export function urge(data) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/urge',
    method: 'post',
    data: data
  })
}

// 隐患督办
export function supervise(data) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/supervise',
    method: 'post',
    data: data
  })
}

// 隐患延期记录所有数据 不分页
export function getPostponeList(query) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDangerPostpone/list',
    method: 'get',
    params: query
  })
}

// 隐患变更责任人记录所有数据 不分页
export function getAlterList(query) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDangerAlter/listByDangerId',
    method: 'get',
    params: query
  })
}
