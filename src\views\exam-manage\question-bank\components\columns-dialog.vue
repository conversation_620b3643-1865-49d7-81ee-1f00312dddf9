<template>
  <div>
    <!-- 栏目管理弹框 -->
    <el-dialog
      title="栏目管理"
      :visible.sync="dialogVisible"
      width="50%"
      :before-close="handleClose"
      top="5vh"
    >
      <div class="opts">
        <el-button type="primary" @click="addDialogVisible = true">
          添加栏目
        </el-button>

        <div>
          <el-input
            v-model="queryParams.classificationName"
            placeholder="请输入"
            prefix-icon="el-icon-search"
            style="width: 240px;margin-right: 10px;"
            clearable
            @keyup.enter.native="handleQuery"
          />

          <div class="fr">
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="handleQuery"
            >
              搜索
            </el-button>

            <el-button icon="el-icon-refresh" @click="handleReset">
              重置
            </el-button>
          </div>
        </div>
      </div>

      <el-table
        v-loading="loading"
        style="width: 100%"
        border
        highlight-current-row
        :header-cell-style="{ backgroundColor: '#f2f2f2' }"
        :data="tableData"
      >
        <template slot="empty">
          <p>{{ $store.getters.dataText }}</p>
        </template>

        <el-table-column
          label="名称"
          show-overflow-tooltip
          align="center"
          prop="classificationName"
          min-width="300"
        />

        <el-table-column
          label="状态"
          show-overflow-tooltip
          align="center"
          prop="status"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.status == 1">
              启用
            </span>

            <span v-else-if="scope.row.status == 2">
              停用
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status == 2"
              type="text"
              @click="handleEnable(scope.row)"
            >
              启用
            </el-button>

            <el-button
              v-if="scope.row.status == 1"
              type="text"
              @click="handleDisable(scope.row)"
            >
              停用
            </el-button>

            <el-button type="text" @click="handleDelete(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <dt-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNo"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-dialog>

    <!-- 添加栏目弹框 -->
    <el-dialog
      title="添加栏目"
      :visible.sync="addDialogVisible"
      width="30%"
      :before-close="handleAddClose"
    >
      <el-form
        ref="addForm"
        :model="addForm"
        :rules="addRules"
        label-width="90px"
        @submit.native.prevent
      >
        <el-form-item label="栏目名称" prop="classificationName">
          <el-input
            v-model="addForm.classificationName"
            placeholder="请输入"
            maxlength="30"
            show-word-limit
            clearable
          />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" @click="handleColumnsSubmit">
          提交
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getTrainingQuestionClassificationPage,
  trainingQuestionClassificationAdd,
  trainingQuestionClassificationDelete,
  trainingQuestionClassificationUpdateStatus
} from '@/api/exam-manage/question-bank'

export default {
  data() {
    return {
      // 栏目管理弹框
      dialogVisible: false,

      queryParams: {
        pageNo: 1,
        pageSize: 10,
        classificationName: '' // 栏目名称
      },

      total: 0, // 总条数
      tableData: [], // 栏目列表数据
      loading: false, // 加载动画


      // 添加栏目弹框
      addDialogVisible: false,
      addForm: {
        classificationName: '' // 栏目名称
      },

      addRules: {
        classificationName: [
          { required: true, message: '请输入栏目名称', trigger: 'blur' }
        ]
      }
    }
  },

  methods: {
    init() {
      this.handleQuery()
      this.dialogVisible = true
    },

    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 重置
    handleReset() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        classificationName: '' // 栏目名称
      }
      this.getList()
    },

    // 获取列表数据
    getList() {
      this.loading = true
      getTrainingQuestionClassificationPage(this.queryParams).then((res) => {
        this.loading = false
        this.tableData = res.data.rows
        this.total = res.data.totalRows
      }).catch(() => {
        this.loading = false
      })
    },

    // 启用
    handleEnable(row) {
      this.$dtModal
        .confirm(`是否确认启用栏目名称为"${row.classificationName}"的数据项？`)
        .then(() => {
          row.status = 1
          return trainingQuestionClassificationUpdateStatus(row)
        })
        .then((res) => {
          if ((res.success = true)) {
            this.getList()
            this.$dtModal.msgSuccess('启用成功')
          }
        })
        .catch(() => {})
    },

    // 停用
    handleDisable(row) {
      this.$dtModal
        .confirm(`是否确认停用栏目名称为"${row.classificationName}"的数据项？`)
        .then(() => {
          row.status = 2
          return trainingQuestionClassificationUpdateStatus(row)
        })
        .then((res) => {
          if ((res.success = true)) {
            this.getList()
            this.$dtModal.msgSuccess('停用成功')
          }
        })
        .catch(() => {})
    },

    // 删除
    handleDelete(row) {
      this.$dtModal
        .confirm(`是否确认删除栏目名称为"${row.classificationName}"的数据项？`)
        .then(() => {
          row.ids = []
          row.ids.push(row.id)
          return trainingQuestionClassificationDelete(row)
        })
        .then((res) => {
          if (res.success == true) {
            this.getList()
            this.$dtModal.msgSuccess('删除成功')
          }
        })
        .catch(() => {})
    },

    // 关闭栏目管理弹框
    async handleClose() {
      await this.$emit('update')
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        classificationName: '' // 栏目名称
      }
      this.dialogVisible = false
    },

    // 关闭添加栏目弹框
    handleAddClose() {
      this.addDialogVisible = false
      this.$refs.addForm.resetFields()
      this.addForm = {
        classificationName: '' // 栏目名称
      }
    },

    // 提交添加栏目
    handleColumnsSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          trainingQuestionClassificationAdd(this.addForm).then((res) => {
            this.getList()
            this.$message.success('添加成功')
            this.handleAddClose()
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.opts{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
</style>
