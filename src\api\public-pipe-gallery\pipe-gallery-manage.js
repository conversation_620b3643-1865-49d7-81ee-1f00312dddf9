/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-08-07 22:48:13
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-08-11 13:15:12
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-08-07 22:48:13
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-08-08 15:35:42
 * @Description: 管廊管理
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */

import request, {cloud}  from '@/framework/utils/request'

// 查询管廊管理列表
export function listPipeGalleryManage(query) {
  return request({
    url: cloud.dqbasic + '/pipeGalleryManage/page',
    method: 'get',
    params: query
  })
}

// 查询管廊管理详细
export function getPipeGalleryManage(id) {
  return request({
    url: cloud.dqbasic + '/pipeGalleryManage/detail?id=' + id,
    method: 'get'
  })
}
// 校验设备信息是否已绑定
export function getGalleryDeviceBindingInfo(data) {
  return request({
    url: cloud.dqbasic + '/deviceManager/getGalleryDeviceBindingInfo',
    method: 'post',
    data: data
  })
}

// 新增管廊管理
export function addPipeGalleryManage(data) {
  return request({
    url: cloud.dqbasic + '/pipeGalleryManage/add',
    method: 'post',
    data: data
  })
}

// 修改管廊管理
export function updatePipeGalleryManage(data) {
  return request({
    url: cloud.dqbasic + '/pipeGalleryManage/edit',
    method: 'post',
    data: data
  })
}

// 删除管廊管理
export function delPipeGalleryManage(id) {
  return request({
    url: cloud.dqbasic + '/pipeGalleryManage/delete',
    method: 'post',
    data: { ids: id }
  })
}
