
/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-07-01 08:59:03
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-07-22 11:15:08
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud}  from '@/framework/utils/request'

// 查询入库列表
export function getStorageList(query) {
  return request({
    url: cloud.dqbasic + '/maInOutHouse/in/page',
    method: 'get',
    params: query
  })
}

// 查询出库列表
export function getOutboundList(query) {
  return request({
    url: cloud.dqbasic + '/maInOutHouse/out/page',
    method: 'get',
    params: query
  })
}

// 查询出/入库详细
export function getDetail(id) {
  return request({
    url: cloud.dqbasic + '/maInOutHouse/detail?id=' + id,
    method: 'get'
  })
}

// 出/入库新增
export function add(data) {
  return request({
    url: cloud.dqbasic + '/maInOutHouse/add',
    method: 'post',
    data: data
  })
}

// 出/入库修改
export function edit(data) {
  return request({
    url: cloud.dqbasic + '/maInOutHouse/edit',
    method: 'post',
    data: data
  })
}

// 出/入库列表删除
export function del(id) {
  return request({
    url: cloud.dqbasic + '/maInOutHouse/delete',
    method: 'post',
    data: { ids: id }
  })
}

/** 获取出入库流程水账列表 */
export function getLedger(query) {
  return request({
    url: cloud.dqbasic + '/maInOutHouse/ledger',
    method: 'get',
    params: query
  })
}

/** 库存统计列表 */
export function ventoryStatistics(query) {
  return request({
    url: cloud.dqbasic + '/maInOutHouse/ventoryStatistics',
    method: 'get',
    params: query
  })
}

/** 库存预警 */
export function inventoryWarning(query) {
  return request({
    url: cloud.dqbasic + '/maInOutHouse/inventoryWarning',
    method: 'get',
    params: query
  })
}

// 查询发放仓库
export function getWarehouse() {
  return request({
    url: cloud.dqbasic + '/materialsIssue/warehouse',
    method: 'get'
  })
}



