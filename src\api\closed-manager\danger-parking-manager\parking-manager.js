import request from '@/framework/utils/request'

// 查询停车场管理列表
export function listParking(query) {
  return request({
    url: '/project/hazardParking/page',
    method: 'get',
    params: query
  })
}

// 查询停车场管理详细
export function getParking(id) {
  return request({
    url: '/project/hazardParking/detail?id=' + id,
    method: 'get'
  })
}

// 新增停车场管理
export function addParking(data) {
  return request({
    url: '/project/hazardParking/add',
    method: 'post',
    data: data
  })
}

// 修改停车场管理
export function updateParking(data) {
  return request({
    url: '/project/hazardParking/edit',
    method: 'post',
    data: data
  })
}

// 删除停车场管理
export function delParking(id) {
  return request({
    url: '/project/hazardParking/delete',
    method: 'post',
    data: { ids: id }
  })
}
