<template>
  <div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="60%"
      :before-close="handleClose"
      top="4vh"
    >
      <el-steps
        :active="active"
        finish-status="success"
        simple
        style="margin-bottom: 20px"
      >
        <el-step title="创建试卷" />

        <el-step title="添加试题" />

        <el-step title="确认试题" />

        <el-step title="设定分值" />
      </el-steps>

      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="90px"
        @submit.native.prevent
      >
        <div v-if="active == 0">
          <el-form-item label="试卷名称" prop="paperName">
            <el-input
              v-model="form.paperName"
              maxlength="30"
              show-word-limit
              placeholder="请输入"
              clearable
            />
          </el-form-item>
        </div>

        <div v-if="active == 1">
          <div class="ques_check_container">
            <div class="left">
              <div class="search">
                <el-input
                  v-model="query.classificationName"
                  placeholder="搜索"
                  prefix-icon="el-icon-search"
                  clearable
                  @keyup.enter.native="getColumnsList"
                />
              </div>

              <div class="items">
                <div
                  v-for="item in columnsList"
                  :key="item.id"
                  class="col_item"
                  :class="{ active: queryParams.classification == item.id }"
                  @click="handleColumnClick(item.id)"
                >
                  <span v-if="item.classificationName.length < 10">
                    {{ item.classificationName }}
                  </span>

                  <el-tooltip
                    v-else
                    class="item"
                    effect="dark"
                    :content="item.classificationName"
                    placement="top"
                  >
                    <span>{{ item.classificationName.slice(0,10) + '...' }}</span>
                  </el-tooltip>
                </div>
              </div>
            </div>

            <div class="right">
              <div class="sort">
                <div class="sort_item">
                  单选题<span>{{ form.singleChoiceCount }}</span>

                  ，多选题<span>{{ form.multipleChoiceCount }}</span>

                  ,判断题<span>{{ form.judgeCount }}</span>
                </div>

                <div style="display: flex;align-items: center">
                  <el-select
                    v-model="queryParams.quesType"
                    placeholder="请选择"
                    clearable
                    style="width: 180px;"
                    @change="handleQuery"
                  >
                    <el-option label="单选题" value="0" />

                    <el-option label="多选题" value="1" />

                    <el-option label="判断题" value="2" />
                  </el-select>

                  <el-button
                    type="primary"
                    style="margin-left: 10px;"
                    @click="getRandomPaper"
                  >
                    系统选题
                  </el-button>
                </div>
              </div>

              <el-table
                :key="queryParams.classification + randomKey"
                ref="multipleTable"
                v-loading="loading"
                row-key="id"
                style="width: 100%"
                max-height="480px"
                border
                highlight-current-row
                :header-cell-style="{ backgroundColor: '#f2f2f2' }"
                :data="tableDataObj[queryParams.classification]"
                :row-class-name="rowClassName"
                @select="handleSelect"
                @select-all="handleSelectAll"
              >
                <template slot="empty">
                  <p>{{ $store.getters.dataText }}</p>
                </template>

                <el-table-column
                  type="selection"
                  width="55"
                  :reserve-selection="true"
                />

                <el-table-column
                  label="试题"
                  show-overflow-tooltip
                  align="center"
                  prop="content"
                  min-width="560"
                />

                <el-table-column
                  label="题型"
                  show-overflow-tooltip
                  align="center"
                  prop="quesType"
                >
                  <template slot-scope="scope">
                    <span v-if="scope.row.quesType == '0'">
                      单选题
                    </span>

                    <span v-else-if="scope.row.quesType == '1'">
                      多选题
                    </span>

                    <span v-else-if="scope.row.quesType == '2'">
                      判断题
                    </span>
                  </template>
                </el-table-column>
              </el-table>

              <dt-pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNo"
                :limit.sync="queryParams.pageSize"
                @pagination="getList()"
              />
            </div>
          </div>
        </div>

        <div v-if="active == 2">
          <div>
            <div class="sort">
              <div class="sort_item">
                单选题<span>{{ form.singleChoiceCount }}</span>

                ，多选题<span>{{ form.multipleChoiceCount }}</span>

                ,判断题<span>{{ form.judgeCount }}</span>
              </div>
            </div>

            <el-table
              ref="elTable"
              style="width: 100%"
              border
              highlight-current-row
              :header-cell-style="{ backgroundColor: '#f2f2f2' }"
              :data="form.questions"
              row-key="examTitleId"
              max-height="510px"
            >
              <template slot="empty">
                <p>{{ $store.getters.dataText }}</p>
              </template>

              <!-- 拖拽手柄列 -->
              <el-table-column width="60">
                <template slot-scope="{}">
                  <i class="el-icon-rank drag-handler" />
                </template>
              </el-table-column>

              <el-table-column
                type="index"
                label="序号"
                width="70"
                :index="
                  (index) => index + 1
                "
              />

              <el-table-column
                label="试题"
                show-overflow-tooltip
                align="center"
                prop="content"
                min-width="500"
              />

              <el-table-column
                label="题型"
                show-overflow-tooltip
                align="center"
                prop="quesType"
              >
                <template slot-scope="scope">
                  <span v-if="scope.row.quesType == '0'">
                    单选题
                  </span>

                  <span v-else-if="scope.row.quesType == '1'">
                    多选题
                  </span>

                  <span v-else-if="scope.row.quesType == '2'">
                    判断题
                  </span>
                </template>
              </el-table-column>

              <el-table-column
                label="栏目"
                show-overflow-tooltip
                align="center"
                prop="classificationName"
                min-width="140"
              />
            </el-table>
          </div>
        </div>

        <div v-if="active == 3">
          <el-form-item label="出题类型" prop="" required>
            <table class="question_type_table">
              <colgroup>
                <col :span="1" width="20%">

                <col :span="1" width="40%">

                <col :span="1" width="40%">
              </colgroup>

              <tr>
                <td>题型</td>

                <td>数量</td>

                <td>每题分值</td>
              </tr>

              <tr>
                <td>单选</td>

                <td>
                  <el-input-number
                    v-model="form.singleChoiceCount"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    clearable
                    disabled
                    style="width: 100%"
                    @blur="e => form.singleChoiceCount = !form.singleChoiceCount ? 0 : form.singleChoiceCount"
                  />
                </td>

                <td>
                  <el-form-item
                    label=""
                    prop="singleChoiceScore"
                    :rules="form.singleChoiceCount > 0 ? typeRules.singleChoiceScore : []"
                  >
                    <el-input-number
                      v-model="form.singleChoiceScore"
                      :min="0"
                      :precision="0"
                      :controls="false"
                      clearable
                      style="width: 100%"
                      @blur="e => (form.singleChoiceScore = !form.singleChoiceScore ? 0 : form.singleChoiceScore,$refs.form.validateField('singleChoiceScore'))"
                    />
                  </el-form-item>
                </td>
              </tr>

              <tr>
                <td>多选</td>

                <td>
                  <el-input-number
                    v-model="form.multipleChoiceCount"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    clearable
                    disabled
                    style="width: 100%"
                    @blur="e => form.multipleChoiceCount = !form.multipleChoiceCount ? 0 : form.multipleChoiceCount"
                  />
                </td>

                <td>
                  <el-form-item
                    label=""
                    prop="multipleChoiceScore"
                    :rules="form.multipleChoiceCount > 0 ? typeRules.multipleChoiceScore : []"
                  >
                    <el-input-number
                      v-model="form.multipleChoiceScore"
                      :min="0"
                      :precision="0"
                      :controls="false"
                      clearable
                      style="width: 100%"
                      @blur="e => (form.multipleChoiceScore = !form.multipleChoiceScore ? 0 : form.multipleChoiceScore,$refs.form.validateField('multipleChoiceScore'))"
                    />
                  </el-form-item>
                </td>
              </tr>

              <tr>
                <td>判断</td>

                <td>
                  <el-input-number
                    v-model="form.judgeCount"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    clearable
                    disabled
                    style="width: 100%"
                    @blur="e => form.judgeCount = !form.judgeCount ? 0 : form.judgeCount"
                  />
                </td>

                <td>
                  <el-form-item
                    label=""
                    prop="judgeScore"
                    :rules="form.judgeCount > 0 ? typeRules.judgeScore : []"
                  >
                    <el-input-number
                      v-model="form.judgeScore"
                      :min="0"
                      :precision="0"
                      :controls="false"
                      clearable
                      style="width: 100%"
                      @blur="e => (form.judgeScore = !form.judgeScore ? 0 : form.judgeScore,$refs.form.validateField('judgeScore'))"
                    />
                  </el-form-item>
                </td>
              </tr>

              <tr>
                <td>合计</td>

                <td>共 {{ totalQuestion }} 题</td>

                <td> 共 {{ totalScore }} 分</td>
              </tr>
            </table>

            <span>
              【注意】题型数量总和需大于0；题型数量大于0时，对应的每题分值需大于0
            </span>
          </el-form-item>
        </div>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button
          size="small"
          @click="handleClose"
        >
          取 消
        </el-button>

        <el-button
          v-if="active !== 0"
          size="small"
          @click="prevStep"
        >
          上 一 步
        </el-button>

        <el-button
          v-if="active !== 3"
          type="primary"
          size="small"
          @click="nextStep"
        >
          下 一 步
        </el-button>

        <el-button
          v-if="active == 3"
          type="primary"
          size="small"
          @click="handleSubmit"
        >
          提 交
        </el-button>
      </span>
    </el-dialog>

    <!-- 系统选题弹框 -->
    <el-dialog
      title="系统选题设置"
      :visible.sync="randomVisible"
      width="40%"
      :before-close="randomClose"
    >
      <el-form
        ref="randomForm"
        :model="randomForm"
        :rules="randomRules"
        label-width="90px"
        @submit.native.prevent
      >
        <el-form-item
          label="题目数量"
          prop=""
          required
        >
          <table class="question_type_table">
            <colgroup>
              <col :span="1" width="30%">

              <col :span="1" width="70%">
            </colgroup>

            <tr>
              <td>题型</td>

              <td>数量</td>
            </tr>

            <tr>
              <td>单选</td>

              <td>
                <el-form-item
                  label=""
                  prop="singleChoiceCount"
                >
                  <el-input-number
                    v-model="randomForm.singleChoiceCount"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    clearable
                    style="width: 100%"
                    @blur="e => randomForm.singleChoiceCount = !randomForm.singleChoiceCount ? 0 : randomForm.singleChoiceCount"
                  />
                </el-form-item>
              </td>
            </tr>

            <tr>
              <td>多选</td>

              <td>
                <el-form-item
                  label=""
                  prop="multipleChoiceCount"
                >
                  <el-input-number
                    v-model="randomForm.multipleChoiceCount"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    clearable
                    style="width: 100%"
                    @blur="e => randomForm.multipleChoiceCount = !randomForm.multipleChoiceCount ? 0 : randomForm.multipleChoiceCount"
                  />
                </el-form-item>
              </td>
            </tr>

            <tr>
              <td>判断</td>

              <td>
                <el-form-item
                  label=""
                  prop="judgeCount"
                >
                  <el-input-number
                    v-model="randomForm.judgeCount"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    clearable
                    style="width: 100%"
                    @blur="e => randomForm.judgeCount = !randomForm.judgeCount ? 0 : randomForm.judgeCount"
                  />
                </el-form-item>
              </td>
            </tr>

            <tr>
              <td>合计</td>

              <td>共 {{ totalRandomQuestion }} 题</td>
            </tr>
          </table>

          <span>
            【注意】题型数量总和需大于0
          </span>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button
          size="small"
          @click="randomClose"
        >
          取 消
        </el-button>

        <el-button
          type="primary"
          size="small"
          @click="randomSubmit"
        >
          提 交
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Sortable from 'sortablejs'
import { getTotalScore, getTotalQuestion } from '@/utils/paper'
import {
  getTrainingQuestionClassificationList,
  getTrainingQuestionSortList,
  getCountQuestionByType
} from '@/api/exam-manage/question-bank'
import {
  getTrainingExamMainPaperDetail,
  trainingExamMainPaperAdd,
  trainingExamMainPaperEdit,
  getRandomExamPaperQuestionList
} from '@/api/exam-manage/paper-manage'

export default {
  name: 'FixedPaperDialog',
  data() {
    // 题型设置校验-单选
    const validateSingleChoiceScore = (rule, value, callback) => {
      if (!value) {
        callback('请输入单选题每题分值')
      } else {
        callback()
      }
    }

    // 题型设置校验-多选
    const validateMultipleChoiceScore = (rule, value, callback) => {
      if (!value) {
        callback('请输入多选题每题分值')
      } else {
        callback()
      }
    }

    // 题型设置校验-判断
    const validateJudgeScore = (rule, value, callback) => {
      if (!value) {
        callback('请输入判断题每题分值')
      } else {
        callback()
      }
    }

    // 系统选题数量校验-单选
    const validateRandomSingleChoiceCount = (rule, value, callback) => {
      if (Number(value) > this.random.singleChoiceCount) {
        this.$message.error(`单选题数量超出题库可用数量${this.random.singleChoiceCount}`)
        callback('单选题数量超出题库可用数量')
      } else {
        callback()
      }
    }

    // 系统选题数量校验-多选
    const validateRandomMultipleChoiceCount = (rule, value, callback) => {
      if (Number(value) > this.random.multipleChoiceCount) {
        this.$message.error(`多选题数量超出题库可用数量${this.random.multipleChoiceCount}`)
        callback('多选题数量超出题库可用数量')
      } else {
        callback()
      }
    }

    // 系统选题数量校验-判断
    const validateRandomJudgeCount = (rule, value, callback) => {
      if (Number(value) > this.random.judgeCount) {
        this.$message.error(`判断题数量超出题库可用数量${this.random.judgeCount}`)
        callback('判断题数量超出题库可用数量')
      } else {
        callback()
      }
    }

    return {
      // 弹框
      dialogTitle: '',
      dialogVisible: false,

      // 步骤条
      active: 0,

      // 第二步 栏目
      columnsList:[],
      query:{
        classificationName: '', // 栏目名称
        status:1 // 栏目状态-启用
      },

      // 第二步 试题列表
      loading: false,
      total: 0,
      randomKey: Math.random(),
      tableDataObj: {},
      queryParams: {
        quesType: '', // 题型
        classification: '', // 栏目
        status:1, // 状态-启用
        pageNo: 1,
        pageSize: 10
      },

      // 表单
      form: {
        singleChoiceCount: 0,
        singleChoiceScore: 0,
        multipleChoiceCount: 0,
        multipleChoiceScore: 0,
        judgeCount: 0,
        judgeScore: 0,
        questions: []
      },

      rules: {
        paperName: [
          { required: true, message: '请输入试卷名称', trigger: 'blur' }
        ]
      },

      typeRules:{
        singleChoiceScore: [
          { required: true, validator: validateSingleChoiceScore, trigger: 'blur' }
        ],

        multipleChoiceScore: [
          { required: true, validator: validateMultipleChoiceScore, trigger: 'blur' }
        ],

        judgeScore: [
          { required: true, validator: validateJudgeScore, trigger: 'blur' }
        ]
      },

      // 系统选题-表单
      randomVisible: false,
      random:{},
      randomForm: {
        singleChoiceCount: 0,
        multipleChoiceCount: 0,
        judgeCount: 0
      },

      randomRules: {
        singleChoiceCount: [
          { required: false, validator: validateRandomSingleChoiceCount, trigger: 'blur' }
        ],

        multipleChoiceCount: [
          { required: false, validator: validateRandomMultipleChoiceCount, trigger: 'blur' }
        ],

        judgeCount: [
          { required: false, validator: validateRandomJudgeCount, trigger: 'blur' }
        ]
      },

      allSelectedMap: {},

      dragWarningTimeout: null
    }
  },

  computed:{
    totalQuestion() {
      return getTotalQuestion(this.form)
    },

    totalScore() {
      return getTotalScore(this.form)
    },

    totalRandomQuestion() {
      return getTotalQuestion(this.randomForm)
    }
  },

  watch: {
    active(newVal, oldVal) {
      if (newVal === 1) {
        this.toggleRowStatus()
      }

      if (oldVal == 2 && this.sortable) {
        this.sortable.destroy()
        this.sortable = null
      }

      if (newVal === 2) {
        this.$nextTick().then(() => {
          this.rowDrop()
        })
      }
    },

    'form.singleChoiceCount': function (val) {
      if (val == 0) {
        this.form.singleChoiceScore = 0
      }
    },

    'form.multipleChoiceCount': function (val) {
      if (val == 0) {
        this.form.multipleChoiceScore = 0
      }
    },

    'form.judgeCount': function (val) {
      if (val == 0) {
        this.form.judgeScore = 0
      }
    }
  },

  mounted() {
    // 行拖拽
    this.rowDrop()
  },

  methods: {
    // 初始化
    init(row) {
      this.getColumnsList()

      if (row && row.id) {
        this.form = row
        this.getPaperDetail(this.form.id)
        this.dialogTitle = '编辑固定题库试题'
      } else {
        this.dialogTitle = '新增固定题库试题'
        this.form.paperType = 2 // 固定
      }
      this.dialogVisible = true
    },

    // 行拖拽
    rowDrop() {
      this.$nextTick().then(() => {
        const table = this.$refs.elTable
        if (!table) return

        const wrapper = table.$el.querySelector('.el-table__body-wrapper tbody')
        if (!wrapper) return

        if (this.sortable) {
          this.sortable.destroy()
          this.sortable = null
        }

        this.sortable = Sortable.create(wrapper, {
          animation: 150,
          handle: '.drag-handler',
          ghostClass: 'sortable-ghost',
          chosenClass: 'sortable-chosen',
          // 关键点1：限制只能同题型拖拽
          onMove: (evt) => {
            const draggedType = evt.dragged.dataset.type
            const relatedType = evt.related.dataset.type
            if (draggedType !== relatedType) {
              // 清除之前的计时器
              if (this.dragWarningTimeout) {
                clearTimeout(this.dragWarningTimeout)
                this.dragWarningTimeout = null
              }

              // 设置新的计时器，500ms内只显示一次
              this.dragWarningTimeout = setTimeout(() => {
                this.$message.warning('只允许在同一题型内拖拽排序！')
              }, 500)
              return false
            }

            return true
          },
          // 关键点2：更新数据
          onEnd: (evt) => {
            if (evt.oldIndex === evt.newIndex) return

            const tempData = [...this.form.questions]
            const [movedItem] = tempData.splice(evt.oldIndex, 1)
            tempData.splice(evt.newIndex, 0, movedItem)
            this.form.questions = tempData

            table.doLayout()
          }
        })

        // 关键点3：为每一行设置data-type属性
        this.setRowDataType()
      })
    },

    // 为每行设置题型标识
    setRowDataType() {
      const rows = this.$refs.elTable.$el.querySelectorAll('.el-table__body-wrapper tbody tr')
      rows.forEach((row, index) => {
        const question = this.form.questions[index]
        if (question) {
          row.dataset.type = question.quesType
        }
      })
    },

    // 获取题库栏目-启用状态
    getColumnsList() {
      getTrainingQuestionClassificationList(this.query).then((res) => {
        this.columnsList = res.data || []

        if (res.data.length && res.data.length > 0) {
          res.data.forEach((item) => {
            // 初始化表格和选中数据
            this.tableDataObj[item.id] = []
          })

          this.queryParams.classification = res.data[0].id
          this.handleQuery()
        }
      })
    },

    // 栏目点击
    handleColumnClick(cid) {
      this.queryParams.classification = cid
      this.quesType = ''
      this.handleQuery()
    },

    // 获取试卷详情
    getPaperDetail(id) {
      getTrainingExamMainPaperDetail({ id }).then((res) => {
        this.form.questions = res.data.questions

        // 初始化全量选中Map
        this.allSelectedMap = {}
        this.form.questions.forEach((item) => {
          if (!this.allSelectedMap[item.classification]) {
            this.allSelectedMap[item.classification] = new Set()
          }
          this.allSelectedMap[item.classification].add(item.examTitleId)
        })

        // 触发当前分类的选中状态恢复
        if (this.$refs.multipleTable) {
          this.toggleRowStatus()
        }
      })
    },

    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 获取试题列表
    getList() {
      const query = JSON.parse(JSON.stringify(this.queryParams))
      this.loading = true
      getTrainingQuestionSortList(query).then((res) => {
        this.loading = false
        res.data.rows.forEach((item) => {
          item.examTitleId = item.id
          // eslint-disable-next-line no-nested-ternary
          item.correctAnswer = item.quesType == 0 ? item.monoRight : item.quesType == 1 ? item.multiRight : item.judgeRight
        })
        this.tableDataObj[this.queryParams.classification] = res.data.rows
        this.total = res.data.totalRows

        if (this.active == 1) {
          this.toggleRowStatus()
        }
      }).catch(() => {
        this.loading = false
      })
    },

    // 单行选中
    handleSelect(rows, row) {
      const cid = this.queryParams.classification
      const selected = this.allSelectedMap[cid] || new Set()

      if (selected.has(row.examTitleId)) {
        selected.delete(row.examTitleId) // 取消选中
        this.form.questions = this.form.questions.filter((item) => item.examTitleId !== row.examTitleId)
      } else {
        selected.add(row.examTitleId) // 选中
        this.form.questions.push(row)
      }

      this.$set(this.allSelectedMap, cid, new Set(selected))
      this.getQuestypeCount()
    },

    // 全选当前页
    handleSelectAll(selection) {
      const cid = this.queryParams.classification
      const currentPageIds = new Set(
        this.tableDataObj[cid].map((item) => item.examTitleId)
      )
      const selected = this.allSelectedMap[cid] || new Set()

      if (selection.length > selected.size) {
        // 全选当前页：添加当前页所有ID
        currentPageIds.forEach((id) => selected.add(id))
        this.form.questions = this.form.questions.concat(
          this.tableDataObj[cid].filter((item) => currentPageIds.has(item.examTitleId))
        ).filter((value, index, self) =>
          index === self.findIndex((t) => (t.examTitleId === value.examTitleId)))
      } else {
        // 取消全选：删除当前页所有ID
        currentPageIds.forEach((id) => selected.delete(id))
        currentPageIds.forEach((id) => this.form.questions = this.form.questions.filter((item) => item.examTitleId !== id))
      }

      this.$set(this.allSelectedMap, cid, new Set(selected))
      this.getQuestypeCount()
    },

    // 选题类型统计
    getQuestypeCount() {
      this.form.singleChoiceCount = 0
      this.form.multipleChoiceCount = 0
      this.form.judgeCount = 0
      const singleChoiceArr = []
      const multipleChoiceArr = []
      const judgeArr = []

      this.form.questions.forEach((item) => {
        if (item.quesType == 0) {
          this.form.singleChoiceCount++
          singleChoiceArr.push(item)
        }

        if (item.quesType == 1) {
          this.form.multipleChoiceCount++
          multipleChoiceArr.push(item)
        }

        if (item.quesType == 2) {
          this.form.judgeCount++
          judgeArr.push(item)
        }
      })

      if (this.active === 2) {
        this.$nextTick().then(() => {
          this.rowDrop()
        })
      }

      this.form.questions = [...singleChoiceArr, ...multipleChoiceArr, ...judgeArr]
    },

    // 行样式（用于视觉反馈）
    rowClassName({ row }) {
      const cid = this.queryParams.classification
      return this.allSelectedMap[cid]?.has(row.examTitleId) ? 'selected-row' : ''
    },

    // 选中试题回显
    toggleRowStatus() {
      // 选中的数据源回显
      this.$nextTick().then(() => {
        const cid = this.queryParams.classification
        const selectedIds = this.allSelectedMap[cid] || new Set()
        this.tableDataObj[cid].forEach((row) => {
          if (selectedIds.has(row.examTitleId)) {
            this.$refs.multipleTable.toggleRowSelection(row, true)
          }
        })
      })
    },

    // 上一步
    prevStep() {
      this.active = this.active - 1
    },

    // 下一步
    nextStep() {
      if (this.active == 0) {
        if (!this.form.paperName) {
          this.$message.error('请输入试卷名称')
          return
        }
        this.active = 1
        return
      }

      if (this.active == 1) {
        if (this.form.singleChoiceCount + this.form.multipleChoiceCount + this.form.judgeCount <= 0) {
          this.$message.error('请选择试题')
          return
        }
        this.active = 2
        return
      }

      if (this.active == 2) {
        this.active = 3
        return
      }
    },

    // 系统选题按钮
    getRandomPaper() {
      this.$dtModal
        .confirm('系统选题生成试卷将覆盖现有数据,是否确认继续操作？')
        .then(() => {
          getCountQuestionByType({ status: 1 }).then((res) => {
            this.random = res.data
          })
          // 展示系统选题弹框
          this.randomVisible = true
        })
        .catch(() => {})
    },

    // 系统选题关闭
    randomClose() {
      this.$refs.randomForm.resetFields()
      this.randomForm = {
        singleChoiceCount: 0,
        multipleChoiceCount: 0,
        judgeCount: 0
      }
      this.randomVisible = false
    },

    // 系统选题生成
    randomSubmit() {
      if (this.totalRandomQuestion <= 0) { // 系统选题
        this.$message.error('请设置系统选题题目数量')
        return
      }

      getRandomExamPaperQuestionList(this.randomForm).then((res) => {
        this.form.singleChoiceCount = this.randomForm.singleChoiceCount
        this.form.multipleChoiceCount = this.randomForm.multipleChoiceCount
        this.form.judgeCount = this.randomForm.judgeCount
        this.form.questions = res.data

        this.allSelectedMap = {}
        this.form.questions.forEach((item) => {
          if (!this.allSelectedMap[item.classification]) {
            this.allSelectedMap[item.classification] = new Set()
          }
          this.allSelectedMap[item.classification].add(item.examTitleId)
        })

        this.queryParams.classification = this.columnsList[0].id
        this.queryParams.quesType = ''
        this.handleQuery()
        this.randomKey = Math.random()
        this.randomClose()
      })
    },

    // 关闭弹框
    handleClose() {
      // 重置状态
      this.active = 0
      this.allSelectedMap = {}

      this.$refs.form.resetFields()
      this.form = {
        singleChoiceCount: 0,
        singleChoiceScore: 0,
        multipleChoiceCount: 0,
        multipleChoiceScore: 0,
        judgeCount: 0,
        judgeScore: 0,
        questions: []
      }
      this.dialogVisible = false
    },

    // 提交
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.form.id) {
            // 新增
            trainingExamMainPaperAdd(this.form).then((res) => {
              this.$message.success('添加成功')
              this.handleClose()
              this.$emit('update')
            })
          } else {
            // 编辑
            trainingExamMainPaperEdit(this.form).then((res) => {
              this.$message.success('编辑成功')
              this.handleClose()
              this.$emit('update')
            })
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.ques_check_container {
  border: 1px solid #e4e7ed;
  display: flex;

  .left {
    width: 20%;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
    height: 595px;
    overflow-y: auto;

    .search {
      height: 46px;
      padding: 0 10px;
      display: flex;
      align-items: center;

      .el-input ::v-deep .el-input__inner {
        background: #fcfcfc;
      }
    }

    .items {
      flex: 1;
      width: 100%;
      height: 100%;
      overflow-y: auto;
      font-size: 14px;

      .col_item {
        height: 50px;
        border: 1px solid #e8e8e8;
        border-left: none;
        border-right: none;
        padding: 8px 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &.active {
          background: #e6f7ff;
        }

        &:not(:first-of-type) {
          border-top: none;
        }
      }
    }
  }

  .right {
    flex: 1;
    width: 100%;
    overflow-y: auto;
    padding: 10px;
  }
}

.sort{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  .sort_item {
    font-size: 14px;
    color: #606266;
    margin-right: 10px;

    span {
      color: #F56C6C;
      margin-left: 5px;
    }
  }

  .el-select {
    width: 180px;
  }
}

.question_type_table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  border: 1px solid #e4e7ed;
  text-align: center;

  tr {
    &:first-of-type {
      font-weight: 700;
      color: #606266;
    }
  }

  td {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #e4e7ed;
    border-right: 1px solid #e4e7ed;
    padding: 0 !important;
  }

  .el-input ::v-deep .el-input__inner {
    text-align: center;
  }

  ::v-deep .el-form-item__error{
    display: none ! important;
  }
}

.el-dialog .el-dialog__body .el-form .question_type_table td .el-form-item{
  margin-bottom: 0 ! important;
}

/* 拖拽手柄样式 */
.drag-handler {
  cursor: move !important;
  font-size: 16px;
  color: #999;
  pointer-events: auto !important;
  /* 防止其他样式覆盖 */
  touch-action: none; /* 移动端兼容 */
}

/* 拖拽时的占位符样式 */
.sortable-ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

/* 选中项样式 */
.sortable-chosen {
  background: #f0f9eb;
}

/* 选中行高亮 */
.el-table .selected-row {
  background-color: #f0f9eb;
}
</style>
