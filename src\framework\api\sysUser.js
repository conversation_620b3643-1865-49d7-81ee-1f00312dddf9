import { default as request, cloud } from '@/framework/utils/request'

export default ({
  fetchPage(params) {
    return request({
      url: `${cloud.dqbasic}/sysUser/page`,
      method: 'get',
      params
    })
  },
  exportUser(params) {
    return request({
      url: `${cloud.dqbasic}/sysUser/exportUser`,
      method: 'post',
      // params,
      params,
      responseType: 'arraybuffer'
    })
  },
  exportAllUser(params) {
    return request({
      url: `${cloud.dqbasic}/sysUser/exportAllUser`,
      method: 'post',
      params,
      responseType: 'arraybuffer'
    })
  },
  changeStatus(data) {
    return request({
      url: `${cloud.dqbasic}/sysUser/changeStatus`,
      method: 'POST',
      data
    })
  },
  detail(id) {
    return request({
      url: `${cloud.dqbasic}/sysUser/detail`,
      method: 'get',
      params: {
        userId: id
      }
    })
  },
  edit(data) {
    return request({
      url: `${cloud.dqbasic}/sysUser/edit`,
      method: 'POST',
      data
    })
  },
  add(data) {
    return request({
      url: `${cloud.dqbasic}/sysUser/add`,
      method: 'POST',
      data
    })
  },
  delete(data) {
    return request({
      url: `${cloud.dqbasic}/sysUser/delete`,
      method: 'post',
      data
    })
  },
  resetPwd(data) {
    return request({
      url: `${cloud.dqbasic}/sysUser/resetPwd`,
      method: 'post',
      data
    })
  },
  onlineUserList(params) {
    return request({
      url: `${cloud.usercenter}/sysUser/onlineUserList`,
      method: 'get',
      params
    })
  },
  removeSession(token) {
    return request({
      url: `${cloud.auth}/auth/removeSession`,
      method: 'post',
      data: {
        token
      }
    })
  },
  getUserListByIds(ids) {
    return request({
      url: `${cloud.dqbasic}/sysUser/getUserListByIds`,
      method: 'get',
      params: {
        ids
      }
    })
  }

})
// 用户自助中心---密码策略
export function getPwdRole(params) {
  return request({
    url: `${cloud.usercenter}/pwdConfig/getPwdConfigNeedLogin`,
    method: 'get',
    params
  })
}
