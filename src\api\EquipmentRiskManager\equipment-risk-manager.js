import request from "@/framework/utils/request"

// 查询设备设施风险列表
export function queryEquipmentRiskList(params) {
  return request({
    url: "/project/bizRiskEquipment/page",
    method: "get",
    params
  })
}

// 新增设备设施风险数据
export function addEquipmentRisk(data) {
  return request({
    url: "/project/bizRiskEquipment/add",
    method: "post",
    data
  })
}

// 编辑设备设施风险数据
export function editEquipmentRisk(data) {
  return request({
    url: "/project/bizRiskEquipment/edit",
    method: "post",
    data
  })
}

// 删除设备设施风险数据
export function deleteEquipmentRisk(data) {
  return request({
    url: "/project/bizRiskEquipment/delete",
    method: "post",
    data
  })
}

// 获取设备设施风险单条数据详情
export function queryEquipmentRiskById(params) {
  return request({
    url: "/project/bizRiskMeasure/page",
    method: "get",
    params
  })
}
// 获取设备设施风险单条数据详情
export function queryEquipmentRiskById1(params) {
  return request({
    url: "/project/bizRiskEquipment/detail",
    method: "get",
    params
  })
}

// 查询设备设施列表
export function queryEquipmentList(data) {
  return request({
    url: "/project/bizEquipment/selectList",
    method: "post",
    data
  })
}

// 可能导致的事故类型列表
export function queryPossibleAccidentTypeList(data) {
  return request({
    url: "/project/bizPossibleAccidentType/selectList",
    method: "post",
    data
  })
}

// 管控主体控制器列表
export function queryControlSubjectList(data) {
  return request({
    url: "/project/bizControlSubject/selectList",
    method: "post",
    data
  })
}

// 评估方法列表
export function queryEvaluationMethodList(data) {
  return request({
    url: "/project/bizEvaluationMethods/selectList",
    method: "post",
    data
  })
}

// 查询 风险管控措施数据
export function queryAllRiskControlMeasure() {
  return request({
    url: "/project/bizRiskMeasure/page",
    method: "get"
  })
}

// 查询 风险管控措施数据 单条详情
export function queryRiskControlMeasureById(params) {
  return request({
    url: "/project/bizRiskMeasure/page",
    method: "get",
    params
  })
}

// 编辑 风险管控措施数据
export function editRiskControlMeasure(data) {
  return request({
    url: "/project/bizRiskMeasure/edit",
    method: "post",
    data
  })
}

// 新增 风险管控措施数据
export function addRiskControlMeasure(data) {
  return request({
    url: "/project/bizRiskMeasure/add",
    method: "post",
    data
  })
}

// 删除 风险管控措施数据
export function deleteRiskControlMeasure(data) {
  return request({
    url: "/project/bizRiskMeasure/delete",
    method: "post",
    data
  })
}
