/*
 * @Author: du<PERSON><PERSON> <EMAIL>
 * @Date: 2024-07-02 17:25:14
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-07-09 15:41:45
 * @FilePath: \isrmp_vue\src\api\related-management\company-evaluate.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import { default as request, cloud } from '@/framework/utils/request'

// 查询相关方-公司评估记录列表
export function listCompanyEvaluate(query) {
  return request({
    url: cloud.dqbasic + '/evaluate/page',
    method: 'get',
    params: query
  })
}

// 查询相关方-公司评估记录详细
export function getCompanyEvaluate(id) {
  return request({
    url: cloud.dqbasic + '/evaluate/detail?id=' + id,
    method: 'get'
  })
}

// 新增相关方-公司评估记录
export function addCompanyEvaluate(data) {
  return request({
    url: cloud.dqbasic + '/evaluate/add',
    method: 'post',
    data: data
  })
}

// 修改相关方-公司评估记录
export function updateCompanyEvaluate(data) {
  return request({
    url: cloud.dqbasic + '/evaluate/edit',
    method: 'post',
    data: data
  })
}

// 删除相关方-公司评估记录
export function delCompanyEvaluate(id) {
  return request({
    url: cloud.dqbasic + '/evaluate/delete',
    method: 'post',
    data: { ids: id }
  })
}
