/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-29 15:41:51
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-30 11:45:22
 * @Description: 包保负责人履职
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import { default as request, cloud } from '@/framework/utils/request'
// 查询包保责任人履职列表
export function listDeputyActivity(query) {
  return request({
    url: cloud.dqbasic +'/deputyActivity/page',
    method: 'get',
    params: query
  })
}

// 查询包保责任人履职详细
export function getDeputyActivity(id) {
  return request({
    url: cloud.dqbasic +'/deputyActivity/detail?id=' + id,
    method: 'get'
  })
}

// 新增包保责任人履职
export function addDeputyActivity(data) {
  return request({
    url: cloud.dqbasic +'/deputyActivity/add',
    method: 'post',
    data: data
  })
}

// 修改包保责任人履职
export function updateDeputyActivity(data) {
  return request({
    url: cloud.dqbasic +'/deputyActivity/edit',
    method: 'post',
    data: data
  })
}

// 删除包保责任人履职
export function delDeputyActivity(id) {
  return request({
    url: cloud.dqbasic +'/deputyActivity/delete',
    method: 'post',
    data: { ids: id }
  })
}
