import request from '@/framework/utils/request'

// 查询化工企业名单列表
export function listEnterpriseList(query) {
  return request({
    url: '/chemicalEnterpriseList/page',
    method: 'get',
    params: query
  })
}

// 查询化工企业名单详细
export function getEnterpriseList(id) {
  return request({
    url: '/chemicalEnterpriseList/detail?id=' + id,
    method: 'get'
  })
}

// 新增化工企业名单
export function addEnterpriseList(data) {
  return request({
    url: '/chemicalEnterpriseList/add',
    method: 'post',
    data: data
  })
}

// 修改化工企业名单
export function updateEnterpriseList(data) {
  return request({
    url: '/chemicalEnterpriseList/edit',
    method: 'post',
    data: data
  })
}

// 删除化工企业名单
export function delEnterpriseList(id) {
  return request({
    url: '/chemicalEnterpriseList/delete',
    method: 'post',
    data: { ids: id }
  })
}
