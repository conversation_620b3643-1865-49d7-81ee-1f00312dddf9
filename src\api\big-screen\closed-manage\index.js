import request from '@/framework/utils/request'


// 封闭化管理大屏
export function getLargeScreenData(query) {
  return request({
    url: `/project/closedManagement/largeScreen`,
    method: 'get',
    params: query
  })
}
// 封闭化管理大屏危化品运输车辆停车场统计数字点击
export function largeScreenParkingInfo(query) {
  return request({
    url: `/project/closedManagement/largeScreenParkingInfo`,
    method: 'get',
    params: query
  })
}
// 封闭化管理大屏危化品运输车辆停车场占用车位详细信息
export function usedCarportInfo(query) {
  return request({
    url: `/project/closedManagement/usedCarportInfo`,
    method: 'get',
    params: query
  })
}
