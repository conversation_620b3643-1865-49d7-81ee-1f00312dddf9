import { default as request, cloud } from '@/framework/utils/request'

export default ({

  // 获取表单数据
  fetchList(params) {
    return request({
      url: `${cloud.process}/flow/customerForm/getPage`,
      method: 'get',
      params
    })
  },

  // 添加表单
  addForm(data) {
    return request({
      url: `${cloud.process}/flow/customerForm/add`,
      method: 'post',
      data
    })
  },
  // 更新
  updateForm(data) {
    return request({
      url: `${cloud.process}/flow/customerForm/update`,
      method: 'post',
      data
    })
  },

  // 删除
  deleteForm(data) {
    return request({
      url: `${cloud.process}/flow/customerForm/delete`,
      method: 'post',
      data
    })
  },
  // 详情
  detail(params) {
    return request({
      url: `${cloud.process}/flow/customerForm/detail`,
      method: 'get',
      params
    })
  }
})
