<template>
  <div class="table-actions">
    <el-button
      v-for="action in directDisplayActions"
      :key="action.label"
      :type="action.type || 'text'"
      @click="action.onClick(scope)"
    >
      {{ action.label }}
    </el-button>

    <el-dropdown v-if="showMoreDropdown" trigger="click">
      <el-button type="text">
        更多<i class="el-icon-arrow-down el-icon--right" />
      </el-button>

      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="action in dropdownDisplayActions"
          :key="action.label"
          :disabled="action.disabled"
          @click.native="action.onClick(scope)"
        >
          {{ action.label }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
export default {
  name: 'TableActions',
  props: {
    actions: {
      type: Array,
      required: true
    },

    scope: {
      type: Object,
      required: true
    },

    maxVisibleCount: {
      type: Number,
      default: 2
    }
  },

  computed: {
    visibleActions() {
      return this.actions.filter((action) => {
        if (typeof action.visible === 'function') {
          return action.visible(this.scope)
        }
        return action.visible !== false
      })
    },

    directDisplayActions() {
      const totalVisible = this.visibleActions.length
      if (totalVisible <= this.maxVisibleCount + 1) {
        return this.visibleActions
      }
      return this.visibleActions.slice(0, this.maxVisibleCount)
    },

    dropdownDisplayActions() {
      const totalVisible = this.visibleActions.length
      if (totalVisible > this.maxVisibleCount + 1) {
        return this.visibleActions.slice(this.maxVisibleCount)
      }
      return []
    },

    showMoreDropdown() {
      return this.visibleActions.length > this.maxVisibleCount + 1
    }
  }
}
</script>

<style lang="scss" scoped>
.table-actions {
  display: inline-flex;
}
.el-button + .el-dropdown {
  margin-left: 10px;
}
</style>
