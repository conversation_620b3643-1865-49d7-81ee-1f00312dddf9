// @Author: wang<PERSON><PERSON> <EMAIL>
// @Date: 2024-08-14 09:15:52
// @LastEditors: wangzexin <EMAIL>
// @LastEditTime: 2025-04-25 11:30:51
// @Description: 我的培训列表
// Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
import request, { cloud } from '@/framework/utils/request'
// 分页查询培训课程信息
export function getTrainRecordPage(query) {
  return request({
    url: `${cloud.dqbasic}/trainingBaseInfo/getTrainRecordPage`,
    method: 'post',
    data: query
  })
}

// 获取培训课程详情
export function getTrainingUserListPage(query) {
  return request({
    url: `${cloud.dqbasic}/trainingRemember/page`,
    method: 'get',
    params: query
  })
}
// 编辑用户评价
export function editTrainingRemember(data) {
  return request({
    url: `${cloud.dqbasic}/trainingRemember/edit`,
    method: 'post',
    data: data
  })
}
// 批量编辑用户评价
export function batchEditTrainingRemember(data) {
  return request({
    url: `${cloud.dqbasic}/trainingRemember/editBatch`,
    method: 'post',
    data: data
  })
}
// 根据培训计划id获取培训课程列表
export function getOnlineCourseList(query) {
  return request({
    url: `${cloud.dqbasic}/trainingOnlineCourse/getOnlineCourseList`,
    method: 'get',
    params: query
  })
}
// 根据培训计划获取对应考试总成绩
export function getExamTotalScore(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/getExamTotalScore`,
    method: 'get',
    params: query
  })
}
// 新增或更新用户线上培训学习记录
export function addTrainingVideoRecord(data) {
  return request({
    url: `${cloud.dqbasic}/trainingVideoRecord/add`,
    method: 'post',
    data: data
  })
}
// 将 minio docx文件转换pdf
export function docxToPdf(query) {
  return request({
    url: `${cloud.dqbasic}/file/docxToPdf`,
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

export function getMyTrainingList(payload) {
  return request({
    url: `${cloud.dqbasic}/trainingBaseInfo/getMyTrainPage`,
    method: 'POST',
    data: payload
  })
}

export function getTrainingCourseListById(trainId) {
  return request({
    url: `${cloud.dqbasic}/trainingOnlineCourse/getOnlineCourseList`,
    method: 'GET',
    params: {
      trainId
    }
  })
}

export function saveCoursePosition(data) {
  return request({
    url: `${cloud.dqbasic}/trainingVideoRecord/add`,
    method: 'POST',
    data
  })
}

export function getCoursePosition(params) {
  return request({
    url: `${cloud.dqbasic}/trainingVideoRecord/detail`,
    method: 'GET',
    params
  })
}
