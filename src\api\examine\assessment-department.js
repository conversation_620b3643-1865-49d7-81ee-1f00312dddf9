/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-08-18 22:48:02
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-08-18 22:48:51
 * @Description: 部门考核
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */

import request, {cloud}  from '@/framework/utils/request'

// 查询部门考核列表
export function listAssessmentDepartment(query) {
  return request({
    url: cloud.dqbasic +'/assessmentDepartment/page',
    method: 'get',
    params: query
  })
}

// 查询部门考核详细
export function getAssessmentDepartment(id) {
  return request({
    url: cloud.dqbasic +'/assessmentDepartment/detail?id=' + id,
    method: 'get'
  })
}

// 新增部门考核
export function addAssessmentDepartment(data) {
  return request({
    url: cloud.dqbasic +'/assessmentDepartment/add',
    method: 'post',
    data: data
  })
}

// 修改部门考核
export function updateAssessmentDepartment(data) {
  return request({
    url: cloud.dqbasic +'/assessmentDepartment/edit',
    method: 'post',
    data: data
  })
}

// 删除部门考核
export function delAssessmentDepartment(id) {
  return request({
    url: cloud.dqbasic +'/assessmentDepartment/delete',
    method: 'post',
    data: { ids: id }
  })
}
