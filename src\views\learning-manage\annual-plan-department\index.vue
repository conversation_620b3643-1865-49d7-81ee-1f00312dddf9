<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          inline
          :model="queryParams"
          label-width="100px"
          @submit.native.prevent
        >
          <el-form-item label="年份">
            <el-date-picker
              v-model="queryParams.planYear"
              type="year"
              value-format="yyyy"
            />
          </el-form-item>

          <el-form-item label="培训类型">
            <el-select v-model="queryParams.trainType" clearable>
              <el-option
                v-for="item in trainingTypeOptions"
                :key="item.id"
                :value="item.dictCode"
                :label="item.dictName"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="是否完成">
            <el-select v-model="queryParams.completeStatus" clearable>
              <el-option
                v-for="item in isCompletedOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="是否延期">
            <el-select v-model="queryParams.delayStatus" clearable>
              <el-option
                v-for="item in isPostponedOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>

          <div class="fr">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">
              搜索
            </el-button>

            <el-button icon="el-icon-refresh" @click="handleReset">
              重置
            </el-button>
          </div>
        </el-form>
      </div>

      <div class="table-container table-fullscreen">
        <el-table
          ref="table"
          v-loading="loading"
          style="width: 100%;"
          border
          highlight-current-row
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            type="index"
            label="序号"
            width="70"
            fixed="left"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            key="trainMonth"
            label="计划培训时间"
            show-overflow-tooltip
            align="center"
            prop="trainMonth"
            width="130"
            :formatter="trainingMonthFormatter"
          />

          <el-table-column
            show-overflow-tooltip
            label="培训内容"
            prop="trainName"
            align="center"
            width="140"
            :formatter="trainingTypeFormatter"
          />

          <el-table-column
            show-overflow-tooltip
            label="培训类型"
            prop="trainType"
            align="center"
            width="140"
            :formatter="trainingTypeFormatter"
          />

          <el-table-column
            key="remembers"
            show-overflow-tooltip
            label="培训对象"
            align="center"
            prop="remembers"
            min-width="140"
          />

          <el-table-column
            label="计划培训人数"
            prop="planNumber"
            align="center"
            show-overflow-tooltip
            width="130"
          />

          <el-table-column
            label="责任部门"
            prop="orgName"
            align="center"
            show-overflow-tooltip
            width="180"
          />

          <el-table-column
            label="是否完成"
            prop="completeStatus"
            align="center"
            show-overflow-tooltip
            width="180"
            :formatter="isCompletedFormatter"
          />

          <el-table-column
            label="是否延期"
            prop="delayStatus"
            align="center"
            show-overflow-tooltip
            width="180"
            :formatter="isPostponedFormatter"
          />

          <el-table-column
            label="实际培训人数"
            prop="totalStuff"
            align="center"
            show-overflow-tooltip
            width="180"
          />

          <el-table-column
            label="实际培训时间"
            align="center"
            show-overflow-tooltip
            width="180"
            :formatter="realTimeFormatter"
          />

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            width="180"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                v-if="+scope.row.delayStatus === 1"
                type="text"
                @click="handleOpenPostponeModal(scope.row)"
              >
                延期说明
              </el-button>

              <el-button
                v-if="+scope.row.completeStatus !== 1"
                type="text"
                @click="handleOpenCompleteModal(scope.row, 'edit')"
              >
                完成
              </el-button>

              <el-button
                v-else
                type="text"
                @click="handleOpenCompleteModal(scope.row, 'view')"
              >
                完成结果
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <ConfirmModal
          title="延期说明"
          :visible.sync="isPostponeModalVisible"
          :submit-loading="isSubmitLoading"
          :rules="postponeModalRules"
          :default-value="currentDelayDescription"
          @submit="handlePostponeReasonSubmit"
        />

        <CompleteTrainingModal
          :visible.sync="isCompleteModalVisible"
          :record="completeRecord"
          :mode="completeModalMode"
          @refresh="handleQuery"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getAnnualPlanExecutionDetailList, submitPostponeReason } from '@/api/learning-manage/annual-plan'
import ConfirmModal from '@/components/confirm-modal'
import CompleteTrainingModal from './components/complete-training-modal'
import dayjs from 'dayjs'

export default {
  name: 'AnnualPlanCompany',
  components: { ConfirmModal, CompleteTrainingModal },
  data() {
    const ownerOrgId = this.$store.getters.orgId
    return {
      isPostponeModalVisible: false,
      isSubmitLoading: false,
      postponeModalRules: { required: true, message: '请输入延期说明', trigger: 'blur' },
      postponedRecordId: '',
      currentDelayDescription: '',

      isCompleteModalVisible: false,
      completeRecord: {},
      completeModalMode: 'edit',

      queryParams:{
        ownerOrgId,
        planYear: `${dayjs().year()}`,
        trainType: '',
        completeStatus: '',
        delayStatus: '',
        searchBeginTime: '',
        searchEndTime: '',
        pageNo: 1,
        pageSize:10
      },

      isCompletedOptions: [{ label: '是', value: 1 }, { label: '否', value: 0 }],
      isPostponedOptions: [{ label: '是', value: 1 }, { label: '否', value: 0 }],

      total:0,
      loading:false,
      tableData:[],
      modalMode: 'add',
      modalRecord: {},
      modalVisible: false,
      currentFileId: '',
      trainingTypeOptions: []
    }
  },

  computed: {
    actualTrainingTime: {
      set(value) {
        if (value && value.length === 2) {
          this.queryParams.searchBeginTime = value[0]
          this.queryParams.searchEndTime = value[1]
        } else {
          this.queryParams.searchBeginTime = ''
          this.queryParams.searchEndTime = ''
        }
      },

      get() {
        if (this.queryParams.searchBeginTime && this.queryParams.searchEndTime) {
          return [this.queryParams.searchBeginTime, this.queryParams.searchEndTime]
        } else {
          return []
        }
      }
    }
  },

  watch: {},
  created() {
    this.handleQuery()
    this.getDicts()
  },

  mounted() {},

  methods: {
    realTimeFormatter(row) {
      if(row.realBeginTime){
        return `${row.realBeginTime}~${row.realEndTime}`
      }
    },

    handleOpenCompleteModal(row, mode) {
      this.completeRecord = JSON.parse(JSON.stringify(row))
      this.completeModalMode = mode
      this.isCompleteModalVisible = true
    },

    handleOpenPostponeModal(row) {
      this.postponedRecordId = row.id
      this.currentDelayDescription = row.delayDesc
      this.isPostponeModalVisible = true
    },

    handlePostponeReasonSubmit(reason) {
      this.isSubmitLoading = true
      submitPostponeReason({
        id: this.postponedRecordId,
        delayDesc: reason
      }).then(() => {
        this.$message.success('提交成功')
        this.isPostponeModalVisible = false
        this.handleQuery()
      }).catch((err) => {
        console.log(err)
        this.$message.error('提交失败')
      }).finally(() => {
        this.isSubmitLoading = false
      })
    },

    trainingMonthFormatter(row, cell, cellValue) {
      // return dayjs().month(cellValue - 1).format('MMM')
      if ([0, 1].includes(row.timeType)) {
        return +row.timeType == 1 ? `${row.timeRange}` : `${cellValue}月`
      } else {
        return 'N/A'
      }
    },

    getDicts() {
      this.businessDictList({ dictTypeCode: 'trainType' }).then((res) => {
        this.trainingTypeOptions = res.data.rows
      })
    },

    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    handleReset() {
      this.queryParams = {
        orgId: this.$store.getters.orgId,
        planYear: `${dayjs().year()}`,
        trainType: '',
        completeStatus: '',
        delayStatus: '',
        searchBeginTime: '',
        searchEndTime: '',
        pageNo: 1,
        pageSize:10
      }
      this.getList()
    },

    transformListData(listData) {
      return listData
    },

    getList() {
      const loadData = (data) => {
        this.tableData = this.transformListData(data.data.rows)
        this.total = data.data.totalRows
      }

      this.loading = true
      getAnnualPlanExecutionDetailList(this.queryParams)
        .then(loadData)
        .finally(() => {
          this.loading = false
        })
    },

    trainingTypeFormatter(row, column, cellValue) {
      const type = this.trainingTypeOptions.find((item) => item.dictCode === cellValue)
      return type ? type.dictName : cellValue
    },

    isPostponedFormatter(row, column, cellValue) {
      const item = this.isPostponedOptions.find((item) => item.value === cellValue)
      return item ? item.label : cellValue
    },

    isCompletedFormatter(row, column, cellValue) {
      const item = this.isCompletedOptions.find((item) => item.value === cellValue)
      return item ? item.label : cellValue
    }
  }
}
</script>

<style scoped lang="scss">
.title-container {
  .el-form-item {
    margin-bottom: 16px;

    ::v-deep .el-form-item__content {
      margin-bottom: 0 !important;
    }
  }
}
</style>
