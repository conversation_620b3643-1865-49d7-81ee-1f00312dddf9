<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="60%"
    :before-close="handleClose"
    top="10vh"
  >
    <div v-loading="loading" class="show_container">
      <div class="questions_sort">
        <div class="sort_item">
          单选题 <span>{{ form.singleChoiceCount }}</span> 题
        </div>

        <div class="sort_item">
          多选题 <span>{{ form.multipleChoiceCount }}</span> 题
        </div>

        <div class="sort_item">
          判断题 <span>{{ form.judgeCount }}</span> 题
        </div>
      </div>

      <div ref="questionsRef" class="question_container">
        <div v-for="(item,index) in form.questions" :key="index" class="question_item">
          <div class="title">
            （{{ item.quesType == 0 ? '单选' : item.quesType == 1 ? '多选' : '判断' }}）{{ item.content }}
          </div>

          <el-radio-group
            v-if="item.quesType == 0"
            v-model="item.correctAnswer"
            class="radio_group"
            disabled
          >
            <el-radio
              v-if="item.optionA"
              label="0"
            >
              <span>{{ 'A、' +item.optionA }}</span>
            </el-radio>

            <el-radio
              v-if="item.optionB"
              label="1"
            >
              <span>{{ 'B、' +item.optionB }}</span>
            </el-radio>

            <el-radio
              v-if="item.optionC"
              label="2"
            >
              <span>{{ 'C、' +item.optionC }}</span>
            </el-radio>

            <el-radio
              v-if="item.optionD"
              label="3"
            >
              <span>{{ 'D、' +item.optionD }}</span>
            </el-radio>

            <el-radio
              v-if="item.optionE"
              label="4"
            >
              <span>{{ 'E、' +item.optionE }}</span>
            </el-radio>

            <el-radio
              v-if="item.optionF"
              label="5"
            >
              <span>{{ 'F、' +item.optionF }}</span>
            </el-radio>
          </el-radio-group>


          <el-checkbox-group
            v-if="item.quesType == 1"
            v-model="item.correctAnswer"
            class="checkbox_group"
            disabled
          >
            <el-checkbox
              v-if="item.optionA"
              label="0"
            >
              <span>{{ 'A、' +item.optionA }}</span>
            </el-checkbox>

            <el-checkbox
              v-if="item.optionB"
              label="1"
            >
              <span>{{ 'B、' +item.optionB }}</span>
            </el-checkbox>

            <el-checkbox
              v-if="item.optionC"
              label="2"
            >
              <span>{{ 'C、' +item.optionC }}</span>
            </el-checkbox>

            <el-checkbox
              v-if="item.optionD"
              label="3"
            >
              <span>{{ 'D、' +item.optionD }}</span>
            </el-checkbox>

            <el-checkbox
              v-if="item.optionE"
              label="4"
            >
              <span>{{ 'E、' +item.optionE }}</span>
            </el-checkbox>

            <el-checkbox
              v-if="item.optionF"
              label="5"
            >
              <span>{{ 'F、' +item.optionF }}</span>
            </el-checkbox>
          </el-checkbox-group>


          <el-radio-group
            v-if="item.quesType == 2"
            v-model="item.correctAnswer"
            class="radio_group"
            disabled
          >
            <el-radio label="0">
              <span>正确</span>
            </el-radio>

            <el-radio label="1">
              <span>错误</span>
            </el-radio>
          </el-radio-group>
        </div>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <div style="width: 100%;text-align: center;margin-top: 15px;">
        <el-button type="primary" size="small" @click="handleClose">
          关 闭
        </el-button>
      </div>
    </span>
  </el-dialog>
</template>

<script>
import {
  getTrainingExamMainPaperDetail
} from '@/api/exam-manage/paper-manage'

export default {
  name: 'FixedPaperDetailDialog',
  data() {
    return {
      // 弹框
      dialogTitle: '',
      dialogVisible: false,

      // 试卷数据
      form:{},

      loading: false
    }
  },

  methods: {
    // 初始化
    init(row) {
      this.dialogTitle = row.paperName
      this.getPaperDetail(row.id)
      this.dialogVisible = true
    },

    // 获取试卷详情
    getPaperDetail(id) {
      this.loading = true
      getTrainingExamMainPaperDetail({ id }).then((res) => {
        this.form = res.data
        this.form.questions.forEach((item) => {
          item.correctAnswer = item.quesType == 1 ? [] : ''
        })
        this.$nextTick().then(() => {
          this.$refs.questionsRef.scrollTop = 0
        })

        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    // 关闭弹框
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.show_container{
  .questions_sort {
    display: flex;
    margin-bottom: 20px;
    font-size: 16px;

    .sort_item {
      margin-right: 20px;

      span {
        color: #f59a23;
        font-weight: 700;
      }
    }
  }

  .question_container{
    height: 500px;
    overflow-y: auto;

    .question_item{
      padding: 10px 20px;
      margin: 10px 0;
      border-bottom: 1px solid #ebeef5;

      .title{
        font-size: 16px;
        color: #333;
        font-weight: 700;
        line-height: 24px;
      }

      .radio_group{
        display: flex;
        flex-direction: column;

        .el-radio{
          margin: 10px 15px;
          white-space: normal;
          line-height: 24px;
          text-align: justify;
          cursor: default;
        }
      }

      .checkbox_group{
        display: flex;
        flex-direction: column;

        .el-checkbox{
          margin: 10px 15px;
          white-space: normal;
          line-height: 24px;
          text-align: justify;
          cursor: default;
        }
      }
    }
  }
}

.radio_group ::v-deep .el-radio__input{
  cursor: default;
}
.radio_group  ::v-deep .el-radio__input.is-disabled + span.el-radio__label{
  color: #606266;
  cursor: default;
}
.radio_group ::v-deep .el-radio__input.is-disabled .el-radio__inner{
  border-color: #dcdfe6;
  background-color: #fff;
  cursor: default;
}

.checkbox_group ::v-deep .el-checkbox__input{
  cursor: default;
}
.checkbox_group  ::v-deep .el-checkbox__input.is-disabled + span.el-checkbox__label{
  color: #606266;
  cursor: default;
}
.checkbox_group ::v-deep .el-checkbox__input.is-disabled .el-checkbox__inner{
  border-color: #dcdfe6;
  background-color: #fff;
  cursor: default;
}
</style>

