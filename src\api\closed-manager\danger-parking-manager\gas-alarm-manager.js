import request from '@/framework/utils/request'

// 查询停车场气体报警管理列表
export function listCarGas(query) {
  return request({
    url: '/project/hazardCarGas/page',
    method: 'get',
    params: query
  })
}

// 查询停车场气体报警管理详细
export function getCarGas(id) {
  return request({
    url: '/project/hazardCarGas/detail?id=' + id,
    method: 'get'
  })
}

// 新增停车场气体报警管理
export function addCarGas(data) {
  return request({
    url: '/project/hazardCarGas/add',
    method: 'post',
    data: data
  })
}

// 修改停车场气体报警管理
export function updateCarGas(data) {
  return request({
    url: '/project/hazardCarGas/edit',
    method: 'post',
    data: data
  })
}

// 删除停车场气体报警管理
export function delCarGas(id) {
  return request({
    url: '/project/hazardCarGas/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 消警停车场气体报警管理
export function cancelCarGas(id) {
  return request({
    url: '/project/hazardCarGas/cancel',
    method: 'post',
    data: { ids: id }
  })
}
