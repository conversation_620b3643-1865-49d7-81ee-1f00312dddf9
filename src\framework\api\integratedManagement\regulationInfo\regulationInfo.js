import request from '@/framework/utils/request'

// 查询执法法规列表
export function listregulationInfo(query) {
  return request({
    url: '/regulationInfo/page',
    method: 'get',
    params: query
  })
}

// 查询执法法规详细
export function getregulationInfo(id) {
  return request({
    url: '/regulationInfo/detail?id=' + id,
    method: 'get'
  })
}

// 新增执法法规
export function addregulationInfo(data) {
  return request({
    url: '/regulationInfo/add',
    method: 'post',
    data: data
  })
}

// 修改执法法规
export function updateregulationInfo(data) {
  return request({
    url: '/regulationInfo/edit',
    method: 'post',
    data: data
  })
}

// 删除执法法规
export function delregulationInfo(id) {
  return request({
    url: '/regulationInfo/delete',
    method: 'post',
    data: { ids: id }
  })
}
