import { default as request, cloud } from '@/framework/utils/request'

export default ({
  fileInfoListPage(params) {
    return request({
      url: `${cloud.file}/sysFileInfo/fileInfoListPage`,
      method: 'get',
      params
    })
  },
  detail(id) {
    return request({
      url: `${cloud.file}/sysFileInfo/detail`,
      method: 'get',
      params: {
        fileId: id
      }
    })
  },
  deleteReally(data) {
    return request({
      url: `${cloud.file}/sysFileInfo/deleteReally`,
      method: 'post',
      data
    })
  }

})
