<!--
  * @Author: jiadongjin <EMAIL>
  * @Date: 2024-05-17 13:43:32
  * @LastEditors: jiadongjin <EMAIL>
  * @LastEditTime: 2024-05-20 16:54:04
  * @Description: 
  * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
-->
<template>
  <el-popover
    v-model="popoverVisible"
    placement="top-start"
    trigger="click"
  >
    <el-tree
      class="tree"
      width="100%"
      :data="treePopover"
      :load="loadPopover"
      lazy
      :props="defaultProps"
      :default-expand-all="false"
      :expand-on-click-node="false"
      @node-click="selectOrg"
    />

    <el-input
      slot="reference"
      v-model="orgName"
      :readonly="false"
      :placeholder="placeholder"
      :disabled="disabled"
      @focus="orgFocus"
      @input="orgInputchange"
    />
  </el-popover>
</template>

<script>
import hrOrganizationApi from "@/framework/api/userCenter/hrOrganization"
// import router from "@/router"

export default {
  props: {
    /**
     * 部门id
     */
    value: {
      type: String,
      default: ''
    },

    /**
     * placeholder
     */
    placeholder: {
      type: String,
      default: ''
    },

    /**
     * 默认部门名称
     */
    defalutOrgName: {
      type: String,
      default: ''
    },

    /**
     * 是否禁用
     */
    disabled: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      popoverVisible: false,
      treePopover: [],
      defaultProps: {
        children: "children",
        label: "name"
      },

      orgName: this.defalutOrgName || ''
    }
  },

  watch: {
    value(val) {
      if(!val) {
        this.orgName = ''
      }
    },

    defalutOrgName(val) {
      this.orgName = val
    }
  },

  methods: {
    /** 获取组织树 */
    loadPopover(tree, resolve) {
      let orgid = ""
      if (!tree.data || tree.data.length == 0) {
        orgid = 0
      } else {
        orgid = tree.data.id
      }
      hrOrganizationApi.getOrgListTreeNode({ orgId: orgid }).then((res) => {
        const { data } = res
        // if (router.history.current.name != "UserManage" && orgid == 0) {
        //   // this.queryParams.accidentDept = data[0].id;
        //   // this.orgName = data[0].name;
        //   // this.selectOrgId = data[0].id;
        //   // this.selectOrgName = data[0].name;
        // }
        resolve(data)
      })
    },

    // 选择机构
    selectOrg(data) {
      this.popoverVisible = false
      this.orgName = data.name

      this.$emit('input', data.id)
    },

    orgFocus() {
      hrOrganizationApi
        .getOrgListTreeNode({ orgId: 0, orgName: this.orgName })
        .then((res) => {
          const data = res.data.map((item, index) => {
            item.accidentDept = item.id
            item.orgName = item.name
            return item
          })
          this.treePopover = data
        })
    },

    orgInputchange() {
      hrOrganizationApi
        .getOrgListTreeNode({ orgId: 0, orgName: this.orgName })
        .then((res) => {
          const data = res.data.map((item, index) => {
            item.accidentDept = item.id
            item.orgName = item.name
            return item
          })
          this.treePopover = data
        })

      if (this.orgName == "") {
        this.orgName = ""
        this.$emit('input', "")
      }
    }
  }
}
</script>