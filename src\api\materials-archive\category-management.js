import request, {cloud} from '@/framework/utils/request'

// 查询类别管理列表
export function listCategoryManagement(query) {
  return request({
    url: cloud.dqbasic + '/maCategoryManagement/page',
    method: 'get',
    params: query
  })
}

// 查询类别管理详细
export function getCategoryManagement(id) {
  return request({
    url: cloud.dqbasic + '/maCategoryManagement/detail?id=' + id,
    method: 'get'
  })
}

// 新增类别管理
export function addCategoryManagement(data) {
  return request({
    url: cloud.dqbasic + '/maCategoryManagement/add',
    method: 'post',
    data: data
  })
}

// 修改类别管理
export function updateCategoryManagement(data) {
  return request({
    url: cloud.dqbasic + '/maCategoryManagement/edit',
    method: 'post',
    data: data
  })
}

// 删除类别管理
export function delCategoryManagement(id) {
  return request({
    url: cloud.dqbasic + '/maCategoryManagement/delete',
    method: 'post',
    data: { ids: id }
  })
}
