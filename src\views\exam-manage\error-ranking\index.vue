<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="70px"
          @submit.native.prevent
        >
          <el-form-item label="试题内容" prop="content">
            <el-input
              v-model.trim="queryParams.content"
              class="filter-item limit"
              maxlength="30"
              clearable
              placeholder="请输入"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item label="试题类型" prop="quesType">
            <el-select v-model="queryParams.quesType" placeholder="请选择">
              <el-option label="单选题" value="0" />

              <el-option label="多选题" value="1" />

              <el-option label="判断题" value="2" />
            </el-select>
          </el-form-item>

          <el-form-item label="所属栏目" prop="columns">
            <el-select
              v-model="queryParams.classification"
              placeholder="请选择"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="item in columnsList"
                :key="item.id"
                :label="item.classificationName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-form>

        <div class="flex-1" />

        <div class="fr">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            搜索
          </el-button>

          <el-button icon="el-icon-refresh" @click="handleReset">
            重置
          </el-button>
        </div>
      </div>

      <div class="table-container table-fullscreen">
        <el-table
          ref="table"
          v-loading="loading"
          style="width: 100%;"
          border
          highlight-current-row
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            type="index"
            label="序号"
            width="70"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            label="错误次数"
            show-overflow-tooltip
            align="center"
            prop="errorNum"
          />

          <el-table-column
            label="试题"
            show-overflow-tooltip
            align="center"
            prop="content"
            min-width="320"
          />

          <el-table-column
            key="quesType"
            label="题型"
            show-overflow-tooltip
            align="center"
            prop="quesType"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.quesType == '0'">
                单选题
              </span>

              <span v-if="scope.row.quesType == '1'">
                多选题
              </span>

              <span v-if="scope.row.quesType == '2'">
                判断题
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="所属栏目"
            show-overflow-tooltip
            align="center"
            prop="classificationName"
            min-width="100"
          />

          <el-table-column
            label="正确答案"
            show-overflow-tooltip
            align="left"
            prop=""
            min-width="140"
          >
            <template slot-scope="scope">
              <div class="option-content">
                <div v-if="scope.row.monoRight == '0' || scope.row.multiRight && scope.row.multiRight.split(',').includes('0') || scope.row.judgeRight == '0'">
                  A、{{ scope.row.optionA || '正确' }}
                </div>

                <div v-if="scope.row.monoRight == '1' || scope.row.multiRight && scope.row.multiRight.split(',').includes('1') || scope.row.judgeRight == '1'">
                  B、{{ scope.row.optionB || '错误' }}
                </div>

                <div v-if="scope.row.monoRight == '2' || scope.row.multiRight && scope.row.multiRight.split(',').includes('2')">
                  C、{{ scope.row.optionC }}
                </div>

                <div v-if="scope.row.monoRight == '3' || scope.row.multiRight && scope.row.multiRight.split(',').includes('3')">
                  D、{{ scope.row.optionD }}
                </div>

                <div v-if="scope.row.monoRight == '4' || scope.row.multiRight && scope.row.multiRight.split(',').includes('4')">
                  E、{{ scope.row.optionE }}
                </div>

                <div v-if="scope.row.monoRight == '5' || scope.row.multiRight && scope.row.multiRight.split(',').includes('5')">
                  F、{{ scope.row.optionF }}
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            label="解析"
            align="center"
            width="120"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="showAnalysis(scope.row)">
                查看解析
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 查看解析弹框 -->
    <QuestionAnalysisDialog ref="questionAnalysisRef" />
  </div>
</template>

<script>
import {
  getTrainingQuestionClassificationList
} from '@/api/exam-manage/question-bank'
import {
  getErrorNumRecordsPage
} from '@/api/exam-manage/error-ranking'
import QuestionAnalysisDialog from '@/components/question-analysis/index.vue'

export default {
  name:'ErrorRanking',
  components:{
    QuestionAnalysisDialog
  },

  data() {
    return {
      // 表格
      loading:false,
      tableData:[],
      total:0,
      queryParams:{
        content:'', // 试题内容
        quesType:'', // 试题类型
        classification:'', // 所属栏目
        pageNo:1,
        pageSize:10
      },

      // 栏目列表
      columnsList:[]
    }
  },

  created() {
    this.handleQuery()
    this.getColumnsList()
  },

  methods: {
    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 重置
    handleReset() {
      this.queryParams = {
        content:'', // 试题内容
        quesType:'', // 试题类型
        classification:'', // 所属栏目
        pageNo:1,
        pageSize:10
      }
      this.getList()
    },

    // 获取列表数据
    getList() {
      this.loading = true
      getErrorNumRecordsPage(this.queryParams).then((res) => {
        this.tableData = res.data.rows
        this.total = res.data.totalRows
        this.loading = false
      }).catch((err) => {
        this.loading = false
      })
    },

    // 获取栏目列表
    getColumnsList() {
      getTrainingQuestionClassificationList({ status:1 }).then((res) => {
        this.columnsList = res.data
      })
    },

    // 查看解析
    showAnalysis(row) {
      this.$refs.questionAnalysisRef.init(row.id)
    }
  }
}
</script>

<style lang="scss" scoped>
  .option-content{
    display: flex;
    flex-direction: column;
    white-space: normal;
  }
</style>
