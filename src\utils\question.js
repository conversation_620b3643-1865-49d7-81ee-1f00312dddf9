import { QUESTION_TYPE_OPTIONS } from '@/constants/question'

export function questionTimeFormatter(question) {
  let seconds
  if (typeof question === 'object' && question.second !== undefined) {
    seconds = Number(question.second)
  } else if (typeof question === 'string' || typeof question === 'number') {
    seconds = Number(question)
  }

  if (isNaN(seconds)) return question

  const hrs = Math.floor(seconds / 3600)
  const mins = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const hh = hrs > 0 ? `${hrs}`.padStart(2, '0') : ''
  const mm = `${mins}`.padStart(2, '0')
  const ss = `${secs}`.padStart(2, '0')

  return hrs ? `${hh}:${mm}:${ss}` : `${mm}:${ss}`
}

export function questionTypeFormatter(question) {
  const typeCode = typeof question === 'object' ? question.quesType || question.type : question

  const type = QUESTION_TYPE_OPTIONS.find((item) => +item.value === +typeCode)
  return type ? type.label : typeCode
}
