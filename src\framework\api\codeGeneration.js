import { default as request, cloud } from '@/framework/utils/request'

// 获取数据源列表接口
export function getDataSourceList(query) {
  return request({
    url: `${cloud.dqbasic}/databaseInfo/list`,
    method: 'get',
    params: query
  })
}
// 根据选择的数据源dbid进行表的信息查询
export function getTablesInfo(query) {
  return request({
    url: `${cloud.dqbasic}/databaseInfo/getTablesInfo`,
    method: 'get',
    params: query
  })
}
// 获取字段列表-- （新增时获取字段列表）
export function getColumnsList(query) {
  return request({
    url: `${cloud.dqbasic}/sysgentabe/getColumns`,
    method: 'get',
    params: query
  })
}
// /sysgentabe/addGen  新增接口
export function addGen(data) {
  return request({
    url: `${cloud.dqbasic}/sysgentabe/addGen`,
    method: 'post',
    data
  })
}
// 点击编辑获取代码生成表及字段详情：/sysgentabe/detail
export function detailGen(query) {
  return request({
    url: `${cloud.dqbasic}/sysgentabe/detail`,
    method: 'get',
    params: query
  })
}
// 修改代码生成信息保存：/sysgentabe/updateGen
export function updateGen(data) {
  return request({
    url: `${cloud.dqbasic}/sysgentabe/updateGen`,
    method: 'post',
    data
  })
}

export default ({
  // 条件查询
  page(params) {
    return request({
      url: `${cloud.dqbasic}/sysgentabe/page`,
      method: 'get',
      params
    })
  },
  // 增加代码生成信息
  addGen(data) {
    return request({
      url: `${cloud.dqbasic}/sysgentabe/addGen`,
      method: 'post',
      data
    })
  },
  // 获取代码生成表及字段详情
  detail(params) {
    return request({
      url: `${cloud.dqbasic}/sysgentabe/detail`,
      method: 'get',
      params
    })
  },
  // 获取字段列表
  getColumns(params) {
    return request({
      url: `${cloud.dqbasic}/sysgentabe/getColumns`,
      method: 'get',
      params
    })
  },
  // 修改代码生成信息
  updateGen(data) {
    return request({
      url: `${cloud.dqbasic}/sysgentabe/updateGen`,
      method: 'post',
      data
    })
  },
  // 删除代码信息
  removeGen(data) {
    return request({
      url: `${cloud.dqbasic}/sysgentabe/removeGen`,
      method: 'post',
      data
    })
  },
  // 批量新增
  addMore(data) {
    return request({
      url: `${cloud.dqbasic}/sysgentabe/addGenBatch`,
      method: 'post',
      data
    })
  },
  // 获取数据源
  list(params) {
    return request({
      url: `${cloud.dqbasic}/databaseInfo/list`,
      method: 'get',
      params
    })
  },
  // 获取表名
  getTablesInfo(params) {
    return request({
      url: `${cloud.dqbasic}/databaseInfo/getTablesInfo`,
      method: 'get',
      params
    })
  },
  // 代码生成
  genCode(params) {
    return request({
      url: `${cloud.dqbasic}/sysgentabe/genCode`,
      method: 'get',
      params,
      responseType: 'blob'
    })
  },
  // 批量生成
  genCodeBatch(params) {
    return request({
      url: `${cloud.dqbasic}/sysgentabe/genCodeBatch`,
      method: 'get',
      params,
      responseType: 'blob'
    })
  }
})
