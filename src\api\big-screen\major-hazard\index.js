import { default as request, cloud } from '@/framework/utils/request'
// 重大危险源类型统计：/hazard/hazardCategoryCount
export function getHazardCategoryCount(params) {
  return request({
    url: `${cloud.dqbasic}/hazard/hazardCategoryCount`,
    method: 'get',
    params
  })
}
//   重大危险源隐患排查统计：/hazard/hiddenDangerCount
export function getHiddenDangerCount(params) {
  return request({
    url: `${cloud.dqbasic}/hazard/hiddenDangerCount`,
    method: 'get',
    params
  })
}
// /重大危险源报警统计：/hazard/hazardWarningCount
export function getHazardWarningCount(params) {
  return request({
    url: `${cloud.dqbasic}/hazard/hazardWarningCount`,
    method: 'get',
    params
  })
}
// 24小时报警统计 /hazard/callPolice
export function getCallPolice(params) {
  return request({
    url: `${cloud.dqbasic}/hazard/callPolice`,
    method: 'get',
    params
  })
}


/** *统计包保责任人包保记录：/hazard/deputyRecord* */
export function getDeputyRecord(params) {
  return request({
    url: `${cloud.dqbasic}/hazard/deputyRecord`,
    method: 'get',
    params
  })
}
// /hazard/safetyAssessment 大屏右下角安全评估评价的接口 companyName 企业名称，hazardCount 危险源数量 ，fileCount安全文件数量
export function getSafetyAssessment(params) {
  return request({
    url: `${cloud.dqbasic}/hazard/safetyAssessment`,
    method: 'get',
    params
  })
}
// 预警处理列表+详情接口：/hazard/warningList
export function getWarningList(params) {
  return request({
    url: `${cloud.dqbasic}/hazard/warningList`,
    method: 'get',
    params
  })
}
// 预警处理接口：/hazard/warningDeal post请求
export function warningDeal(data) {
  return request({
    url: `${cloud.dqbasic}/hazard/warningDeal`,
    method: 'post',
    data
  })
}

// 统计分析视图页面-饼图 /hazard/classifySupervise
export function getClassifySupervise(params) {
  return request({
    url: `${cloud.dqbasic}/hazard/classifySupervise`,
    method: 'get',
    params
  })
}
// 统计分析视图页面-列表 /hazard/classifySuperviseList
export function getClassifySuperviseList(params) {
  return request({
    url: `${cloud.dqbasic}/hazard/classifySuperviseList`,
    method: 'get',
    params
  })
}

// 保障责任主体清单接口
export function getResponsiblePersonStatistics(params) {
  return request({
    url: `${cloud.dqbasic}/hazard/responsiblePersonStatistics`,
    method: 'get',
    params
  })
}

// 危险源类型综合分析接口
export function getHazardCount(params) {
  return request({
    url: `${cloud.dqbasic}/hazard/hazardCount`,
    method: 'get',
    params
  })
}

// 重大危险源查询列表统计接口（支持危险源等级条件查询，危险源名称模糊查询）不分页
export function getHazardListBySelect(data) {
  return request({
    url: `${cloud.dqbasic}/hazard/hazardListBySelect`,
    method: 'post',
    data
  })
}

// 预警事件综合统计接口
export function getHazardEventsCount(params) {
  return request({
    url: `${cloud.dqbasic}/hazard/hazardEventsCount`,
    method: 'get',
    params
  })
}

// 预警事件报警信息列表接口  请求传参status（已处理/未处理）
export function getHazardEventsInfo(data) {
  return request({
    url: `${cloud.dqbasic}/hazard/hazardEventsInfo`,
    method: 'post',
    data
  })
}

// 监管设备/应用实时状态接口
export function getStatusCount(params) {
  return request({
    url: `${cloud.dqbasic}/hazard/statusCount`,
    method: 'get',
    params
  })
}

// 履职统计及达成率接口
export function getPerformanceStatistics(params) {
  return request({
    url: `${cloud.dqbasic}/hazard/performanceStatistics`,
    method: 'get',
    params
  })
}

// 不分页查询设备管理信息及点位数据实时监测信息
export function getDeviceAll(params) {
  return request({
    url: `${cloud.dqbasic}/deviceManager/getAllListInfo`,
    method: 'get',
    params
  })
}

// 不分页查询AI智能应用所有数据
export function getAiAppAll(params) {
  return request({
    url: `${cloud.dqbasic}/aiApp/getAllListInfo`,
    method: 'get',
    params
  })
}

// 不分页查询AI智能应用所有数据
export function getHazardDetail(params) {
  return request({
    url: `${cloud.dqbasic}/hazard/detail`,
    method: 'get',
    params
  })
}

// 履职统计查询详情列表
export function getAllDeputyActivityList(data) {
  return request({
    url: `${cloud.dqbasic}/hazard/getAllDeputyActivityList`,
    method: 'post',
    data
  })
}

// 数据预警点击处理详情
export function getHazardDataWarningDetail(params) {
  return request({
    url: `${cloud.dqbasic}/hazardDataWarning/detail`,
    method: 'get',
    params
  })
}

// 视频预警点击处理详情
export function getHazardVideoWarningDetail(params) {
  return request({
    url: `${cloud.dqbasic}/hazardVideoWarning/detail`,
    method: 'get',
    params
  })
}

// 数据预警报警处理
export function dealHazardDataWarning(data) {
  return request({
    url: `${cloud.dqbasic}/hazardDataWarning/deal`,
    method: 'post',
    data
  })
}

// 视频预警报警处理
export function dealHazardVideoWarning(data) {
  return request({
    url: `${cloud.dqbasic}/hazardVideoWarning/deal`,
    method: 'post',
    data
  })
}
// 实时预警
export function getHazardWarningInfoList(data) {
  return request({
    url: `${cloud.dqbasic}/hazard/getHazardWarningInfoList`,
    method: 'post',
    data
  })
}