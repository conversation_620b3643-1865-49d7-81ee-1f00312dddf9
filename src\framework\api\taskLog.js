import { default as request, cloud } from '@/framework/utils/request'

export default ({
  getTaskLogList(params) {
    return request({
      url: `${cloud.dqbasic}/timerLog/page`,
      method: 'get',
      params
    })
  },
  deleteLog(data) {
    return request({
      url: `${cloud.dqbasic}/timerLog/deleteLog`,
      method: 'post',
      data
    })
  },
  getDetail(params) {
    return request({
      url: `${cloud.dqbasic}/timerLog/detail`,
      method: 'get',
      params
    })
  },
  getTaskName(params) {
    return request({
      url: `${cloud.dqbasic}/sysTimers/page`,
      method: 'get',
      params
    })
  },
  // 获取字典表
  getDictList(params) {
    return request({
      url: `${cloud.dqbasic}/dict/list`,
      method: 'get',
      params
    })
  }
})
