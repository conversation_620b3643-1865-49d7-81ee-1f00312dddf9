<template>
  <div class="app-container">
    <div class="mainbox">
      <div class=" record-container">
        <div class="tree-container">
          <div>
            <div class="tree-title">
              <!-- <i class="el-icon-s-home" /> -->
              <span>课程栏目</span>
            </div>

            <el-tree
              class="tree"
              width="100%"
              :data="treePopover"
              :props="defaultProps"
              :default-expand-all="false"
              :expand-on-click-node="false"
              @node-click="selectDuration"
            />
          </div>
        </div>

        <div class="list-container">
          <div class="top-container">
            <el-form
              ref="queryForm"
              :model="queryParams"
              :inline="true"
              label-width="100px"
              @submit.native.prevent
            >
              <el-form-item label="姓名">
                <el-input v-model="queryParams.userName" />
              </el-form-item>

              <el-form-item label="部门">
                <DeptSelect v-model="queryParams.orgId" placeholder="请选择创建部门" />
              </el-form-item>
            </el-form>
            <div class="flex-1" />
            <div>
              <div class="flex-1" />
              <div style="display: flex;">
                <el-button type="primary" icon="el-icon-search" @click="handleQuery">
                  搜索
                </el-button>
                <el-button icon="el-icon-refresh" @click="handleReset">
                  重置
                </el-button>
              </div>
            </div>
          </div>

          <div class="table-wrap">
            <el-table
              ref="table"
              v-loading="isLoading"
              border
              :header-cell-style="{ backgroundColor: '#f2f2f2' }"
              :data="tableData"
            >
              <el-table-column
                show-overflow-tooltip
                align="center"
                prop="userName"
                label="姓名"
                width="120"
              />

              <el-table-column
                show-overflow-tooltip
                align="center"
                prop="orgName"
                label="部门"
                width="0"
              />

              <el-table-column
                show-overflow-tooltip
                align="center"
                prop="post"
                label="岗位"
                width="120"
              >
              </el-table-column>

              <el-table-column
                show-overflow-tooltip
                align="center"
                prop="learnTime"
                label="视频学习（总时长/分钟）"
                width="150"
              />

              <el-table-column
                show-overflow-tooltip
                align="center"
                prop="todayTime"
                label="视频学习（今日时长/分钟）"
                width="150"
              />

              <el-table-column
                show-overflow-tooltip
                align="center"
                prop="learnNumber"
                label="图文学习（总次数/次）"
                width="150"
              />

              <el-table-column
                show-overflow-tooltip
                align="center"
                prop="todayNumber"
                label="图文学习（今日次数/次）"
                width="150"
              />
            </el-table>
          </div>

          <dt-pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNo"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </div>


      
  </div>
</template>

<script>
import { getCourseDurationPage } from '@/api/learning-manage/training-report.js'
import DeptSelect from '@/components/dept-select/dept-select.vue'

export default {
  name: 'StudyDuration',
  components: { DeptSelect },
  data() {
    return {
      queryParams: {
        userName: '',
        courseName: '',
        courseGrouping: '',
        orgId: '',
        pageNo: 1,
        pageSize: 10
      },
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      currentDirId: '',
      treePopover: [],
      total: 0,
      isLoading: false,
      tableData: [],
      groupOptions: []
    }
  },

  created() {
    this.handleQuery()
    this.getTreeDicts()
  },

  mounted() {

  },

  methods: {
    getTreeDicts(){
      this.businessDictList({ dictTypeCode: 'courseGrouping' }).then((res) => {
        const treeData  = res.data.rows.map(item => ({
          id: item.dictId,         // 节点唯一标识
          label: item.dictName,    // 节点显示文本
          ...item                  // 保留原始数据所有字段
        }))
        // 在首位添加"全部"节点
        this.treePopover = [
          {
            id: '',                     // 使用特殊ID，确保唯一性
            label: '全部',                // 显示文本
            dictId: '',                // 与id保持一致
            dictName: '全部',            // 完整名称
            dictCode: 'all',             // 编码设为'all'
            dictTypeCode: 'courseGrouping', // 保持相同字典类型
            isAllNode: true              // 添加自定义标记，方便后续识别
          },
          ...treeData                    // 展开原始数据
        ]
      })
    },
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    handleReset() {
      this.queryParams = {
        userName: '',
        courseName: '',
        orgId: '',
        pageNo: 1,
        pageSize: 10
      }
      this.getList()
    },

    getList() {
      this.isLoading = true
      this.queryParams.courseGrouping = this.currentDirId
      getCourseDurationPage(this.queryParams).then((data) => {
        this.isLoading = false
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      })
    },

    selectDuration(val){
      this.currentDirId = val.id
      this.getList()
    },
  }
}
</script>

<style lang="scss" scoped>
.record-container {
  display: flex;
  flex: 1;
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 4px 8px 2px rgb(0 0 0 / 4%), 0 2px 6px 0 rgb(0 0 0 / 6%), 0 0 4px 0 rgb(0 0 0 / 8%);

  .tree-container {
    width: 20%;
  }

  .list-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    width: 80%;
    border-left: 1px solid #ccc;

    .top-container {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      margin-bottom: 16px;
    }
  }

  .table-wrap {
    padding-left: 20px;
  }
}

.tree-title {
  margin-bottom: 10px;

  i {
    margin-right: 10px;
    font-size: 14px;
  }

  span {
    display: inline-block;
    font-size: 14px;
    cursor: pointer;
  }
}
</style>
