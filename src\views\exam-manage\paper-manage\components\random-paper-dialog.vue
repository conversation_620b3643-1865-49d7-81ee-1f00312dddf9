<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="40%"
    :before-close="handleClose"
  >
    <el-form
      ref="form"
      v-loading="loading"
      :model="form"
      :rules="rules"
      label-width="90px"
      @submit.native.prevent
    >
      <el-form-item label="名称" prop="paperName">
        <el-input
          v-model="form.paperName"
          maxlength="30"
          show-word-limit
          :disabled="disabled"
          clearable
          placeholder="请输入"
        />
      </el-form-item>

      <el-form-item label="出题范围" prop="classification">
        <el-select
          v-model="form.classification"
          placeholder="请选择"
          style="width: 100%"
          :disabled="disabled"
          clearable
        >
          <el-option
            v-for="item in columnsList"
            :key="item.id"
            :label="item.classificationName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="出题类型" prop="" required>
        <table class="question_type_table">
          <colgroup>
            <col :span="1" width="20%">

            <col :span="1" width="40%">

            <col :span="1" width="40%">
          </colgroup>

          <tr>
            <td>题型</td>

            <td>数量</td>

            <td>每题分值</td>
          </tr>

          <tr>
            <td>单选</td>

            <td>
              <el-input-number
                v-model="form.singleChoiceCount"
                :min="0"
                :precision="0"
                :controls="false"
                :disabled="disabled"
                clearable
                style="width: 100%"
                @blur="e => form.singleChoiceCount = !form.singleChoiceCount ? 0 : form.singleChoiceCount"
              />
            </td>

            <td>
              <el-form-item
                label=""
                prop="singleChoiceScore"
                :rules="form.singleChoiceCount > 0 ? typeRules.singleChoiceScore : []"
              >
                <el-input-number
                  v-model="form.singleChoiceScore"
                  :min="0"
                  :precision="0"
                  :controls="false"
                  :disabled="disabled"
                  clearable
                  style="width: 100%"
                  @blur="e => (form.singleChoiceScore = !form.singleChoiceScore ? 0 : form.singleChoiceScore,$refs.form.validateField('singleChoiceScore'))"
                />
              </el-form-item>
            </td>
          </tr>

          <tr>
            <td>多选</td>

            <td>
              <el-input-number
                v-model="form.multipleChoiceCount"
                :min="0"
                :precision="0"
                :controls="false"
                :disabled="disabled"
                clearable
                style="width: 100%"
                @blur="e => form.multipleChoiceCount = !form.multipleChoiceCount ? 0 : form.multipleChoiceCount"
              />
            </td>

            <td>
              <el-form-item
                label=""
                prop="multipleChoiceScore"
                :rules="form.multipleChoiceCount > 0 ? typeRules.multipleChoiceScore : []"
              >
                <el-input-number
                  v-model="form.multipleChoiceScore"
                  :min="0"
                  :precision="0"
                  :controls="false"
                  :disabled="disabled"
                  clearable
                  style="width: 100%"
                  @blur="e => (form.multipleChoiceScore = !form.multipleChoiceScore ? 0 : form.multipleChoiceScore,$refs.form.validateField('multipleChoiceScore'))"
                />
              </el-form-item>
            </td>
          </tr>

          <tr>
            <td>判断</td>

            <td>
              <el-input-number
                v-model="form.judgeCount"
                :min="0"
                :precision="0"
                :controls="false"
                :disabled="disabled"
                clearable
                style="width: 100%"
                @blur="e => form.judgeCount = !form.judgeCount ? 0 : form.judgeCount"
              />
            </td>

            <td>
              <el-form-item
                label=""
                prop="judgeScore"
                :rules="form.judgeCount > 0 ? typeRules.judgeScore : []"
              >
                <el-input-number
                  v-model="form.judgeScore"
                  :min="0"
                  :precision="0"
                  :controls="false"
                  :disabled="disabled"
                  clearable
                  style="width: 100%"
                  @blur="e => (form.judgeScore = !form.judgeScore ? 0 : form.judgeScore,$refs.form.validateField('judgeScore'))"
                />
              </el-form-item>
            </td>
          </tr>

          <tr>
            <td>合计</td>

            <td>共 {{ totalQuestion }} 题</td>

            <td> 共 {{ totalScore }} 分</td>
          </tr>
        </table>

        <span v-if="!disabled">
          【注意】题型数量总和需大于0；题型数量大于0时，对应的每题分值需大于0
        </span>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="handleClose">
        {{ disabled ? '关 闭' : '取 消' }}
      </el-button>

      <el-button
        v-if="!disabled" type="primary" size="small"
        @click="handleSubmit"
      >
        提 交
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getTotalScore, getTotalQuestion } from '@/utils/paper'
import {
  getTrainingQuestionClassificationList
} from '@/api/exam-manage/question-bank'
import {
  trainingExamMainPaperAdd,
  trainingExamMainPaperEdit
} from '@/api/exam-manage/paper-manage'

export default {
  name: 'FixedPaperDialog',
  data() {
    // 题型设置校验-单选
    const validateSingleChoiceScore = (rule, value, callback) => {
      if (!value) {
        callback('请输入单选题每题分值')
      } else {
        callback()
      }
    }

    // 题型设置校验-多选
    const validateMultipleChoiceScore = (rule, value, callback) => {
      if (!value) {
        callback('请输入多选题每题分值')
      } else {
        callback()
      }
    }

    // 题型设置校验-判断
    const validateJudgeScore = (rule, value, callback) => {
      if (!value) {
        callback('请输入判断题每题分值')
      } else {
        callback()
      }
    }

    return {
      // 是否可编辑
      disabled: false,
      loading: false,

      // 弹框
      dialogTitle: '',
      dialogVisible: false,

      // 表单
      form: {
        singleChoiceCount: 0,
        singleChoiceScore: 0,
        multipleChoiceCount: 0,
        multipleChoiceScore: 0,
        judgeCount: 0,
        judgeScore: 0
      },

      rules: {
        paperName: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],

        classification: [
          { required: true, message: '请选择出题范围', trigger: 'change' }
        ]
      },

      typeRules:{
        singleChoiceScore: [
          { required: true, validator: validateSingleChoiceScore, trigger: 'blur' }
        ],

        multipleChoiceScore: [
          { required: true, validator: validateMultipleChoiceScore, trigger: 'blur' }
        ],

        judgeScore: [
          { required: true, validator: validateJudgeScore, trigger: 'blur' }
        ]
      },

      // 题库栏目
      columnsList:[]
    }
  },

  computed:{
    totalQuestion() {
      return getTotalQuestion(this.form)
    },

    totalScore() {
      return getTotalScore(this.form)
    }
  },

  watch: {
    'form.singleChoiceCount': function (val) {
      if (val == 0) {
        this.form.singleChoiceScore = 0
      }
    },

    'form.multipleChoiceCount': function (val) {
      if (val == 0) {
        this.form.multipleChoiceScore = 0
      }
    },

    'form.judgeCount': function (val) {
      if (val == 0) {
        this.form.judgeScore = 0
      }
    }
  },

  methods: {
    // 初始化
    init(row) {
      if (row && row.id) {
        this.loading = true
        this.form = row
        this.dialogTitle = this.disabled ? '随机题库试题详情' : '编辑随机题库试题'
      } else {
        this.dialogTitle = '新增随机题库试题'
        this.form.paperType = 1 // 随机
      }
      this.getColumnsList()
      this.dialogVisible = true
    },

    // 获取题库栏目-启用状态
    getColumnsList() {
      getTrainingQuestionClassificationList({ status:1 }).then((res) => {
        this.columnsList = res.data
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    // 关闭弹框
    handleClose() {
      // 重置状态
      this.disabled = false

      this.$refs.form.resetFields()
      this.form = {
        singleChoiceCount: 0,
        singleChoiceScore: 0,
        multipleChoiceCount: 0,
        multipleChoiceScore: 0,
        judgeCount: 0,
        judgeScore: 0
      }
      this.dialogVisible = false
    },

    // 提交
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.totalQuestion <= 0) {
            this.$message.error('请设置出题类型')
            return
          }

          if (!this.form.id) {
            // 新增
            trainingExamMainPaperAdd(this.form).then((res) => {
              this.$message.success('添加成功')
              this.handleClose()
              this.$emit('update')
            })
          } else {
            // 编辑
            trainingExamMainPaperEdit(this.form).then((res) => {
              this.$message.success('编辑成功')
              this.handleClose()
              this.$emit('update')
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.question_type_table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  border: 1px solid #e4e7ed;
  text-align: center;

  tr {
    &:first-of-type {
      font-weight: 700;
      color: #606266;
    }
  }

  td {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #e4e7ed;
    border-right: 1px solid #e4e7ed;
    padding: 0 !important;
  }

  .el-input ::v-deep .el-input__inner {
    text-align: center;
  }

  ::v-deep .el-form-item__error{
    display: none ! important;
  }
}

.el-dialog .el-dialog__body .el-form .question_type_table td .el-form-item{
  margin-bottom: 0 ! important;
}
</style>

