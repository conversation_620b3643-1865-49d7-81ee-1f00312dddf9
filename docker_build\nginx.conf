#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;

	
	# 上传最大大小
	client_max_body_size 100M;
        # 开启gzip打包
	gzip on;
        gzip_vary on;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_buffers 16 8k;
        gzip_http_version 1.1;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
	#自定义变量 $connection_upgrade
    map $http_upgrade $connection_upgrade { 
        default          keep-alive;  #默认为keep-alive 可以支持 一般http请求
        'websocket'      upgrade;     #如果为websocket 则为 upgrade 可升级的。
    }

#微服务版本 基础平台
      server {
                listen       9377;#默认端口是80，如果端口没被占用可以不用修改
                server_name  localhost;
                #charset koi8-r;
                #access_log  logs/host.access.log  main;

                location / {
                        root   /usr/share/nginx/html/dist/;#vue项目的打包后的dist
                        try_files $uri $uri/ @router;#需要指向下面的@router否则会出现vue的路由在nginx中刷新出现404
                        index  index.html index.htm;
                }
                #对应上面的@router，主要原因是路由的路径资源并不是一个真实的路径，所以无法找到具体的文件
                #因此需要rewrite到index.html中，然后交给路由在处理请求资源
                location @router {
                        rewrite ^.*$ /index.html last;
                }

                location /stage-api/ {
                   proxy_pass http://isrmp-gateway.isrmp:7010/; #后台接口地址
                   proxy_http_version 1.1;
                   proxy_set_header Upgrade $http_upgrade; #此处配置 上面定义的变量
           		   proxy_set_header Connection $connection_upgrade;
                   proxy_set_header   X-Real-Scheme    $scheme;
           		   proxy_set_header   Host $host;
           		   proxy_set_header   X-Real-IP        $remote_addr;
           		   proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
                }
        }  

         server {
                listen       9477;#默认端口是80，如果端口没被占用可以不用修改
                server_name  localhost;
                #charset koi8-r;
                #access_log  logs/host.access.log  main;

                location / {
                        root   /usr/share/nginx/html/dist-process-designer/;#vue项目的打包后的dist
                        try_files $uri $uri/ @router;#需要指向下面的@router否则会出现vue的路由在nginx中刷新出现404
                        index  index.html index.htm;
                }
                #对应上面的@router，主要原因是路由的路径资源并不是一个真实的路径，所以无法找到具体的文件
                #因此需要rewrite到index.html中，然后交给路由在处理请求资源
                location @router {
                        rewrite ^.*$ /index.html last;
                }

                location /stage-api/ {
                   proxy_pass http://isrmp-gateway.isrmp:7010:7010/; #后台接口地址
                   proxy_http_version 1.1;
                   proxy_set_header Upgrade $http_upgrade; #此处配置 上面定义的变量
           		   proxy_set_header Connection $connection_upgrade;
                   proxy_set_header   X-Real-Scheme    $scheme;
           		   proxy_set_header   Host $host;
           		   proxy_set_header   X-Real-IP        $remote_addr;
           		   proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
                }
        }  
}

