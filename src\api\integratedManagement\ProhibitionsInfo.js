import request, {cloud} from '@/framework/utils/request'

// 查询禁限控目录列表
export function listProhibitionsInfo(query) {
  return request({
    url: cloud.dqbasic + '/prohibitionsInfo/page',
    method: 'get',
    params: query
  })
}

// 查询禁限控目录详细
export function getProhibitionsInfo(id) {
  return request({
    url: cloud.dqbasic + '/prohibitionsInfo/detail?id=' + id,
    method: 'get'
  })
}

// 新增禁限控目录
export function addProhibitionsInfo(data) {
  return request({
    url: cloud.dqbasic + '/prohibitionsInfo/add',
    method: 'post',
    data: data
  })
}

// 修改禁限控目录
export function updateProhibitionsInfo(data) {
  return request({
    url: cloud.dqbasic + '/prohibitionsInfo/edit',
    method: 'post',
    data: data
  })
}

// 删除禁限控目录
export function delProhibitionsInfo(id) {
  return request({
    url: cloud.dqbasic + '/prohibitionsInfo/delete',
    method: 'post',
    data: { ids: id }
  })
}
