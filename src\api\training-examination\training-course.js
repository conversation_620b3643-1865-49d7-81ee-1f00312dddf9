/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-02 15:19:01
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-04 14:45:48
 * @Description: 培训课程
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'
// 分页查询培训课程信息
export function getTrainingVideoList(query) {
  return request({
    url: '/project/trainingVideo/page',
    method: 'get',
    params: query
  })
}
  
// 新增培训课程信息
export function addTrainingVideo(data) {
  return request({
    url: '/project/trainingVideo/add',
    method: 'post',
    data: data
  })
}
// 修改培训课程信息
export function editTrainingVideo(data) {
  return request({
    url: '/project/trainingVideo/edit',
    method: 'post',
    data: data
  })
}
// 修改培训课程信息
export function updateStatus(data) {
  return request({
    url: '/project/trainingVideo/updateStatus',
    method: 'post',
    data: data
  })
}
// 获取单个培训课程信息
export function getTrainingVideoDetail(query) {
  return request({
    url: '/project/trainingVideo/detail',
    method: 'get',
    params: query

  })
}

// 删除培训课程信息
export function deleteTrainingVideoDetail(data) {
  return request({
    url: '/project/trainingVideo/delete',
    method: 'post',
    data: data

  })
}

// 查询课程学习统计概况
export function getCourseLearningStatistics(query) {
  return request({
    url: '/project/trainingVideoRecord/getCourseLearningStatistics',
    method: 'get',
    params: query

  })
}
// 查询课程学习完课率趋势统计
export function selectCompleteTrend(query) {
  return request({
    url: '/project/trainingVideoRecord/selectCompleteTrend',
    method: 'get',
    params: query

  })
}
// 查询课程学习趋势统计
export function selectStudyTrend(query) {
  return request({
    url: '/project/trainingVideoRecord/selectStudyTrend',
    method: 'get',
    params: query

  })
}
// 分页查询学员学习数据概况
export function selectStudyPeopleDetailSituation(query) {
  return request({
    url: '/project/trainingVideoRecord/selectStudyPeopleDetailSituation',
    method: 'get',
    params: query

  })
}