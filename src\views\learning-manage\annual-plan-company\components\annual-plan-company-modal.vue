<template>
  <el-dialog
    :title="title"
    width="70%"
    v-bind="$attrs"
    :close-on-click-modal="false"
    @closed="handleClosed"
    v-on="$listeners"
  >
    <div v-loading="isLoading" class="dialog-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-position="right"
        label-width="110px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="年份" prop="planYear" required>
              <span v-if="mode === 'view'">
                {{ formData.planYear }}
              </span>

              <el-date-picker
                v-else
                v-model="formData.planYear"
                type="year"
                value-format="yyyy"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="创建部门" prop="planOrgId" required>
              <span v-if="mode === 'view'">
                {{ formData.orgName }}
              </span>

              <DeptSelect
                v-else
                v-model="formData.planOrgId"
                class="dept-select"
                :defalut-org-name="defaultOrgName"
                disabled
                style="margin-left: 10px;"
                placeholder="请选择部门"
                @input="handleDeptChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="计划内容" class="required-form-item">
              <el-button
                v-if="mode !== 'view'"
                type="primary"
                class="plan-add-button"
                @click="handleAddPlanContent"
              >
                添加
              </el-button>

              <el-table
                :class="mode === 'view' ? 'view-mode' : ''"
                :data="formData.trainingPlanInfoList"
                border
                :header-cell-style="{ backgroundColor: '#f2f2f2' }"
              >
                <el-table-column
                  required
                  label-class-name="required-column"
                  :show-overflow-tooltip="mode === 'view'"
                  label="培训内容"
                  prop="trainName"
                  align="center"
                  width="200"
                >
                  <template #default="scope">
                    <el-form-item
                      :prop="`trainingPlanInfoList.${scope.$index}.trainName`"
                      :rules="formRules.trainingName"
                    >
                      <span v-if="mode==='view'">
                        {{ scope.row.trainName }}
                      </span>

                      <el-input v-else v-model="scope.row.trainName" maxlength="200" />
                    </el-form-item>
                  </template>
                </el-table-column>

                <el-table-column
                  label="培训类型"
                  :show-overflow-tooltip="mode === 'view'"
                  prop="trainType"
                  align="center"
                  width="240"
                >
                  <template #default="scope">
                    <el-form-item>
                      <span v-if="mode === 'view'">
                        {{ getTrainingTypeLabel(scope.row.trainType) }}
                      </span>

                      <el-select v-else v-model="scope.row.trainType">
                        <el-option
                          v-for="item in trainingTypeOptions"
                          :key="item.id"
                          :value="item.dictCode"
                          :label="item.dictName"
                        />
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>

                <el-table-column
                  label="培训对象"
                  prop="remembers"
                  align="center"
                  :show-overflow-tooltip="mode === 'view'"
                  width="200"
                >
                  <template #default="scope">
                    <el-form-item>
                      <span v-if="mode === 'view'">
                        {{ scope.row.remembers }}
                      </span>

                      <el-input
                        v-else
                        v-model.trim="scope.row.remembers"
                        maxlength="200"
                      />
                    </el-form-item>
                  </template>
                </el-table-column>

                <el-table-column
                  label="计划培训人数"
                  prop="planNumber"
                  align="center"
                  :show-overflow-tooltip="mode === 'view'"
                  width="110"
                >
                  <template #default="scope">
                    <el-form-item>
                      <span v-if="mode === 'view'">
                        {{ scope.row.planNumber }}
                      </span>

                      <el-input-number
                        v-else
                        v-model.number="scope.row.planNumber"
                        :controls="false"
                      />
                    </el-form-item>
                  </template>
                </el-table-column>

                <el-table-column
                  label="计划培训时间"
                  align="center"
                  width="340"
                  :show-overflow-tooltip="mode === 'view'"
                  label-class-name="required-column"
                >
                  <template #default="scope">
                    <el-form-item
                      :prop="`trainingPlanInfoList.${scope.$index}.timeRange`"
                      :rules="formRules.trainingTime"
                    >
                      <span v-if="mode === 'view'">
                        {{ getTrainingTimeLabel(scope.row) }}
                      </span>

                      <TrainingTimeSelector
                        v-else
                        :time-range.sync="scope.row.timeRange"
                        :time-type.sync="scope.row.timeType"
                      />
                    </el-form-item>
                  </template>
                </el-table-column>

                <el-table-column
                  label="责任部门"
                  prop="ownerOrgId"
                  :show-overflow-tooltip="mode === 'view'"
                  align="center"
                  label-class-name="required-column"
                  width="240"
                >
                  <template #default="scope">
                    <el-form-item
                      :prop="`trainingPlanInfoList.${scope.$index}.ownerOrgId`"
                      :rules="formRules.trainingOrg"
                    >
                      <span v-if="mode === 'view'">
                        {{ scope.row.orgName }}
                      </span>

                      <DeptSelect
                        v-else
                        v-model="scope.row.ownerOrgId"
                        :defalut-org-name="scope.row.owerOrgName"
                      />
                    </el-form-item>
                  </template>
                </el-table-column>

                <el-table-column
                  v-if="mode !== 'view'"
                  label="操作"
                  width="60"
                  align="center"
                  fixed="right"
                >
                  <template #default="scope">
                    <el-button type="text" @click="handleDeleteTrainingContent(scope.$index)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>

            <el-form-item prop="trainingPlanInfoList" />
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="培训计划文件" prop="planFile" required>
              <FileUploadEcho
                style="width: 100%;"
                :disabled="mode === 'view'"
                :show-preview="true"
                :file-id.sync="formData.planFile"
                :file-limit="1"
                :file-size="10"
                :is-show-tip="true"
                :file-type="[
                  'png',
                  'jpg',
                  'jpeg',
                  'gif',
                  'docx',
                  'pdf'
                ]"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <span v-if="mode === 'view'">
                {{ formData.remark }}
              </span>

              <el-input
                v-else
                v-model.trim="formData.remark"
                :disabled="mode === 'view'"
                type="textarea"
                :autosize="{ minRows: 4, maxRows: 5 }"
                maxlength="250"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div slot="footer">
      <el-button
        v-if="mode === 'add'"
        type="primary"
        :loading="isSaving"
        @click="handleSave"
      >
        保存
      </el-button>

      <el-button @click="handleClose">
        取消
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import DeptSelect from '@/components/dept-select/dept-select.vue'
import FileUploadEcho from '@/components/file-upload-echo/file-upload-echo.vue'
import orgApi from '@/framework/api/userCenter/hrOrganization'
import dayjs from 'dayjs'
import TrainingTimeSelector from './training-time-selector.vue'
import { addAnnualPlan, getAnnualPlanDetailById } from '@/api/learning-manage/annual-plan'

export default {
  name: 'AnnualPlanCompanyModal',
  components: { TrainingTimeSelector, FileUploadEcho, DeptSelect },
  inheritAttrs: false,

  props: {
    mode: {
      type: String,
      required: true,
      validator: (val) => ['add', 'edit', 'view'].includes(val)
    },

    record: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    const initialData = this.createInitialData()
    return {
      isLoading: false,
      isSaving: false,
      orgName: '',
      formData: initialData,
      formRules: {
        planYear: { required: true, message: '请选择年份', trigger: ['change', 'blur'] },
        planOrgId: { required: true, message: '请选择年份', trigger: ['change', 'blur'] },
        planFile: { required: true, message: '请选择培训计划文件', trigger: ['change', 'blur'] },
        trainingPlanInfoList: {
          type: 'array',
          required: true,
          min: 1,
          message: '请选添加训内容',
          trigger: ['change', 'blur']
        },

        trainingTime: { required: true, message: '请输入培训时间', trigger: ['change', 'blur'] },
        trainingName: { required: true, message: '请输入培训内容', trigger: ['change', 'blur'] },
        trainingOrg: { required: true, message: '请选择责任部门', trigger: ['change', 'blur'] }
      },

      trainingTypeOptions: []
    }
  },

  computed: {
    title() {
      const map = {
        add: '新增',
        edit: '编辑',
        view: '查看'
      }
      return map[this.mode]
    },

    userDepartmentId() {
      return this.$store.getters.orgId
    },

    defaultOrgName() {
      return this.formData.orgName || this.orgName
    }
  },

  watch: {
    mode() {
      this.handlePropsChange()
    },

    record() {
      this.handlePropsChange()
    }
  },

  created() {
    this.getDicts()
  },

  methods: {
    resetFormData() {
      this.formData = this.createInitialData()
      this.$refs.formRef.resetFields()
    },

    handleClosed() {
      this.resetFormData()
    },

    handleClose() {
      this.$emit('update:visible')
    },

    async handleSave() {
      this.isSaving = true
      this.$refs.formRef.validate()
        .then(() => addAnnualPlan(this.formData))
        .then(() => {
          this.$message.success('创建成功')
          this.$emit('refresh')
          this.$emit('update:visible', false)
        })
        .finally(() => {
          this.isSaving = false
        })
    },

    getDicts() {
      this.businessDictList({ dictTypeCode: 'trainType' }).then((res) => {
        this.trainingTypeOptions = res.data.rows
      })
    },

    getTrainingTimeLabel(row) {
      const { timeType, timeRange } = row
      if (timeType === 0) {
        return `按月: ${timeRange}月`
      } else {
        return `其他：${timeRange}`
      }
    },

    getTrainingTypeLabel(code) {
      const type = this.trainingTypeOptions.find((item) => item.dictCode === code)
      return type ? type.dictName : code
    },

    getOrgName() {
      orgApi.detail(this.formData.planOrgId)
        .then((res) => {
          this.orgName = res.data.orgName
        })
    },

    handlePropsChange() {
      if (this.mode === 'add') {
        this.formData = this.createInitialData()
        this.getOrgName()
      } else {
        this.isLoading = true
        this.getDetail(this.record)
          .then((formData) => {
            this.formData = formData
          }).finally(() => {
            this.isLoading = false
          })
      }
    },

    createInitialData() {
      return {
        planYear: dayjs().format('YYYY'),
        planOrgId: this.userDepartmentId,
        orgName: '',
        planFile: '',
        trainingPlanInfoList: [],
        remark: ''
      }
    },

    transformData(data) {
      return data
    },

    getDetail() {
      return getAnnualPlanDetailById(this.record.id)
        .then((res) => {
          return this.transformData(res.data)
        })
    },

    handleDeptChange() {

    },

    handleAddPlanContent() {
      this.formData.trainingPlanInfoList.push({
        trainName: '',
        trainType: '',
        remembers: '',
        planNumber: '',
        ownerOrgId: '',
        timeType: 0,
        timeRange: ''
      })
      this.$nextTick().then(() => {
        this.$refs.formRef.validateField('trainingPlanInfoList')
      })
    },

    handleDeleteTrainingContent(index) {
      this.formData.trainingPlanInfoList.splice(index, 1)

      this.$nextTick().then(() => {
        this.$refs.formRef.validateField('trainingPlanInfoList')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
  ::v-deep .el-form-item__content {
    > :is(.el-input, .el-select, .el-input-number) {
      width: 100%;
      .el-input__inner {
        text-align: left;
      }
    }
  }
}

.view-mode ::v-deep .el-form-item:not(#editing) {
  margin-bottom: 0px !important;
}

.plan-add-button {
  margin-bottom: 10px;
}

.dept-select[style*='margin-left'] {
  margin-left: 0 !important;
}

::v-deep .required-column.cell {
  &::before {
    content: '* ';
    color: red;
  }
}


::v-deep .required-form-item .el-form-item__label{
  &::before {
    content: '* ';
    color: red;
  }
}
</style>
