/*
 * @Author: 高宇 <EMAIL>
 * @Date: 2024-05-10 16:41:30
 * @LastEditors: 高宇 <EMAIL>
 * @LastEditTime: 2024-05-10 17:17:36
 * @FilePath: \isrmp_vue\src\api\safety-signs\safety-signs.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/framework/utils/request'

// 查询安全标志管理列表
export function bizSafetySignsPage(query) {
  return request({
    url: '/project/bizSafetySigns/page',
    method: 'get',
    params: query
  })
}

// 查询安全标志管理详细
export function bizSafetySignsDetail(params) {
  return request({
    url: '/project/bizSafetySigns/detail',
    method: 'get',
    params
  })
}

// 新增安全标志管理
export function bizSafetySignsAdd(data) {
  return request({
    url: '/project/bizSafetySigns/add',
    method: 'post',
    data: data
  })
}

// 修改安全标志管理
export function bizSafetySignsEdit(data) {
  return request({
    url: '/project/bizSafetySigns/edit',
    method: 'post',
    data: data
  })
}

// 删除安全标志管理
export function bizSafetySignsDel(signsId) {
  return request({
    url: '/project/bizSafetySigns/delete',
    method: 'post',
    data: { signsIds: signsId }
  })
}
