import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 获取密码规则配置
  getPwdRules: () => {
    return request({
      url: `${cloud.usercenter}/pwdConfig/getPwdConfigNeedLogin`,
      method: 'get'
    })
  },
  // 修改密码规则配置
  updatePwdRules: (data) => {
    return request({
      url: `${cloud.usercenter}/pwdConfig/updatePwdConfig`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    })
  }

})
