import { default as request, cloud } from '@/framework/utils/request'

// 查询业务-事故调查改进措施列表
export function listAccidentSurveyMeasure(query) {
  return request({
    url:  cloud.dqbasic + '/bizAccidentSurveyMeasure/list',
    method: 'get',
    params: query
  })
}

// 查询业务-事故调查改进措施详细
export function getAccidentSurveyMeasure(id) {
  return request({
    url:  cloud.dqbasic + '/bizAccidentSurveyMeasure/detail?id=' + id,
    method: 'get'
  })
}

// 新增业务-事故调查改进措施
export function addAccidentSurveyMeasure(data) {
  return request({
    url:  cloud.dqbasic + '/bizAccidentSurveyMeasure/add',
    method: 'post',
    data: data
  })
}

// 修改业务-事故调查改进措施
export function updateAccidentSurveyMeasure(data) {
  return request({
    url:  cloud.dqbasic + '/bizAccidentSurveyMeasure/edit',
    method: 'post',
    data: data
  })
}

// 删除业务-事故调查改进措施
export function delAccidentSurveyMeasure(id) {
  return request({
    url:  cloud.dqbasic + '/bizAccidentSurveyMeasure/delete',
    method: 'post',
    data: { ids: id }
  })
}
