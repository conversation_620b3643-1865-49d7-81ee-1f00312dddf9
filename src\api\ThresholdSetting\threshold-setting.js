/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-14 14:51:15
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-05-25 16:45:25
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'

// 查询阈值设置列表
export function listThresholdSetting(query) {
  return request({
    url: '/project/thresholdSetting/page',
    method: 'get',
    params: query
  })
}

// 查询阈值设置详细
export function getThresholdSetting(id) {
  return request({
    url: '/project/thresholdSetting/detail?id=' + id,
    method: 'get'
  })
}

// 新增阈值设置
export function addThresholdSetting(data) {
  return request({
    url: '/project/thresholdSetting/add',
    method: 'post',
    data: data
  })
}

// 修改阈值设置
export function updateThresholdSetting(data) {
  return request({
    url: '/project/thresholdSetting/edit',
    method: 'post',
    data: data
  })
}

// 删除阈值设置
export function delThresholdSetting(id) {
  return request({
    url: '/project/thresholdSetting/delete',
    method: 'post',
    data: { ids: id }
  })
}
