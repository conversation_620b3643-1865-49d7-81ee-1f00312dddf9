/*
 * @Author: miteng <EMAIL>
 * @Date: 2024-05-15 16:13:32
 * @LastEditors: 高宇 <EMAIL>
 * @LastEditTime: 2024-05-21 13:46:47
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'

// 分页查询可能导致的事故类型所有数据
export function bizPossibleAccidentTypePage(params) {
  return request({
    url: '/project/bizPossibleAccidentType/page',
    method: 'get',
    params
  })
}

// 获取可能导致的事故类型单条数据详情
export function bizPossibleAccidentTypeDetail(params) {
  return request({
    url: '/project/bizPossibleAccidentType/detail',
    method: 'get',
    params
  })
}
// 新增可能导致的事故类型数据
export function bizPossibleAccidentTypeAdd(data) {
  return request({
    url: '/project/bizPossibleAccidentType/add',
    method: 'post',
    data: data
  })
}

// 修改可能导致的事故类型数据
export function bizPossibleAccidentTypeEdit(data) {
  return request({
    url: '/project/bizPossibleAccidentType/edit',
    method: 'post',
    data: data
  })
}

// 删除可能导致的事故类型数据
export function bizPossibleAccidentTypeDel(id) {
  return request({
    url: '/project/bizPossibleAccidentType/delete',
    method: 'post',
    data: { typeIds: id }
  })
}