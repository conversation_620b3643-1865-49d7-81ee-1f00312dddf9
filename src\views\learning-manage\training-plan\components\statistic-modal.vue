<template>
  <el-dialog
    title="培训统计"
    v-bind="$attrs"
    top="10vh"
    v-on="$listeners"
    @open="handleOpen"
    @closed="handleClosed"
  >
    <el-tabs v-model="activeName" v-loading="isLoading">
      <el-tab-pane label="课程详情" name="course">
        <el-table border :data="courseList" height="500px">
          <el-table-column
            v-for="column in courseTableColumnList"
            :key="column.prop"
            :label="column.label"
            :prop="column.prop"
            :formatter="column.formatter"
          />
        </el-table>
      </el-tab-pane>

      <el-tab-pane label="学员详情" name="trainee">
        <el-table border :data="traineeList" height="500px">
          <el-table-column
            v-for="column in traineeTableColumnList"
            :key="column.prop"
            :label="column.label"
            :prop="column.prop"
            :formatter="column.formatter"
          />
        </el-table>
      </el-tab-pane>
    </el-tabs>

    <div slot="footer">
      <el-button @click="handleCancel">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getTraineeProgressById, getTrainingPlanCourseById } from '@/api/learning-manage/training-plan'

export default {
  name: 'StatisticModal',
  props: {
    trainingId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      activeName: 'course',
      isLoading: false,

      courseTableColumnList: [
        { label: '课程名称', prop: 'courseName' },
        {
          label: '课程类型',
          prop: 'resourceType',
          formatter: (row, column, cellValue) => +cellValue === 1 ? '图文' : '视频'
        },
        { label: '完成人数', prop: 'completeNum' },
        { label: '完成率', prop: 'completePer' }
      ],

      traineeTableColumnList: [
        { label: '姓名', prop: 'userName' },
        { label: '部门', prop: 'orgName' },
        { label: '课程总数', prop: 'courseNum' },
        { label: '完成数量', prop: 'completeNum' },
        { label: '完成率', prop: 'completePer', formatter: (row, col, val) => typeof val === 'number' ? `${val}%` : val }
      ],

      courseList: [],
      traineeList: []
    }
  },

  methods: {
    handleOpen() {
      this.getData()
    },

    handleClosed() {
      this.reset()
    },

    reset() {
      this.activeName = 'course'
      this.courseList = []
      this.traineeList = []
      this.isLoading = false
    },

    handleCancel() {
      this.$emit('update:visible', false)
    },

    async getData() {
      this.isLoading = true
      const currentTraining = this.trainingId
      try {
        const [
          courseResponse,
          traineeResponse
        ] = await Promise.all([
          getTrainingPlanCourseById(this.trainingId),
          getTraineeProgressById(this.trainingId)
        ])

        if (currentTraining !== this.trainingId) return

        console.log('courseResponse, traineeResponse', courseResponse, traineeResponse)
        this.courseList = courseResponse.data || []
        this.traineeList = traineeResponse.data || []
      } catch (err) {
        this.$message.error('获取统计数据失败')
        console.error('获取培训统计数据失败:', err)
      } finally {
        if (currentTraining === this.trainingId) {
          this.isLoading = false
        }
      }
    }
  }
}
</script>
