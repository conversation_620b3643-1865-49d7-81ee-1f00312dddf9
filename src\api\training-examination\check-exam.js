/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-15 17:13:13
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-16 16:19:31
 * @Description: 查看考试
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'
// 分页查询查看考试所有数据
export function selectExamRecord(query) {
  return request({
    url: '/project/trainingExamPlan/selectExamRecord',
    method: 'get',
    params: query
  })
}

// 根据考试计划查询用户的答卷
export function getUserExam(query) {
  return request({
    url: '/project/trainingExamUser/getUserExam',
    method: 'get',
    params: query
  })
}