import { default as request, cloud } from '@/framework/utils/request'

// 左侧树
export function getAppServiceList(query) {
  return request({
    url: `${cloud.permission}/app/getAppServiceList`,
    method: 'get',
    params: query
  })
}

// table表格数据
export function getApiByAppServicePage(query) {
  return request({
    url: `${cloud.permission}/resource/getApiByAppServicePage`,
    method: 'get',
    params: query
  })
}

// 修改接口是否登录、是否鉴权
export function setApiAccess(data) {
  return request({
    url: `${cloud.permission}/allocate/setApiAccess`,
    method: 'post',
    data
  })
}
