import request, { cloud } from '@/framework/utils/request'

// 考试统计查询
export function getExamStatisticsPage(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/getExamStatistics`,
    method: 'get',
    params: query
  })
}

// 导出
export function exportExamStatistics(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExamPlan/exportExamStatistics`,
    method: 'get',
    params: query,
    responseType: 'arraybuffer'
  })
}
