/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-15 09:54:56
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-07-02 09:45:07
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import { default as request, cloud } from '@/framework/utils/request'

// 获取文件信息单条数据详情
export function getFileDetail(id) {
  return request({
    url: cloud.dqbasic + `/bizFileInfo/detail?id=${id}`,
    method: 'get'
  })
}

// 通用提交审批 { bizTableName表名, formDataId数据主键id }
export function submitForApproval(data) {
  return request({
    url: cloud.process + '/myflow/flowable/startProcess/biz',
    method: 'post',
    data
  })
}

// 文件预览
export function showDecument(params) {
  return request({
    url: cloud.dqbasic + '/filePriview',
    method: 'get',
    params
  })
}