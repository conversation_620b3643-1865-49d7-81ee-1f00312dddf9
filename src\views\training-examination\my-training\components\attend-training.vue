<template>
  <div v-loading="isTrainingLoading" class="attend-training">
    <div class="title-box">
      <div class="title-left" />

      <div class="title">
        {{ trainingName }}
      </div>

      <div class="title-actions">
        <el-button type="" @click="handleBack">
          返回
        </el-button>

        <el-button v-show="trainingInfo.examType == 1" type="primary" @click="handleBack">
          参加考试
        </el-button>
      </div>
    </div>

    <div class="training-content">
      <div class="training-content-left">
        <div class="course-list">
          <div class="course-list-title">
            课程列表
          </div>

          <ol class="course-list">
            <li
              v-for="(course, index) in courseList"
              :key="course.courseId"
              :class="[
                'course-list-item',
                course.courseId === currentCourseId ? 'course-list-item--current' : ''
              ]"
              @click="handleCourseSelect(course)"
            >
              {{ index + 1 }}. {{ course.courseName }}
            </li>
          </ol>
        </div>
      </div>

      <TrainingCourse
        v-loading="isContentLoading"
        class="training-content-right"
        :training-id="trainingPlanId"
        :course-id="currentCourseId"
      />
    </div>
  </div>
</template>

<script>
import { getTrainingPlanById } from '@/api/learning-manage/training-plan'
import TrainingCourse from '@/components/training-course'

export default {
  name: 'AttendTraining',
  components: { TrainingCourse },
  props: {
    trainingPlanId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      isTrainingLoading: false,
      currentCourseId: '',
      trainingInfo: {
        trainName: '',
        examType: ''
      }
    }
  },

  computed: {
    courseList() {
      return this.trainingInfo.onlineCourseList || []
    },

    hasCourse() {
      return this.courseList.length > 0
    },

    trainingName() {
      return this.trainingInfo.trainName || ''
    },

    isContentLoading() {
      return this.isCourseLoading || this.isFileUrlLoading || this.isPlaybackUrlLoading
    }
  },

  mounted() {
    this.getTrainingInfo()
  },

  methods: {
    handleCourseSelect(course) {
      this.currentCourseId = course.courseId
    },

    getTrainingInfo() {
      this.isTrainingLoading = true
      getTrainingPlanById(this.trainingPlanId)
        .then((res) => {
          this.trainingInfo = res.data
          if (this.hasCourse) {
            this.handleCourseSelect(this.courseList[0])
          }
        })
        .finally(() => {
          this.isTrainingLoading = false
        })
    },


    handleMenuSelect(index) {
      this.activeMenu = index
    },

    submitAnswer() {
      // 处理答题提交逻辑
    },

    handleBack() {
      this.$emit('back', false)
    }

  }
}
</script>

<style lang="scss" scoped>
.attend-training {
  background-color: #fff;
  padding: 20px;
  min-height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
  font-size: 14px;


  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;

    .title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .title-actions {
      .el-button {
        margin-left: 10px;
      }
    }
  }

  .training-content {
    flex: 1;
    display: flex;
    gap: 20px;
    min-height: 0;
    --border-color: #ebeef5;

    .course-list {
      margin: 10px 0 0;
      padding: 0;
      height: calc(100% - 10px - 36px);
      overflow: auto;
      border-radius: 4px;
      list-style: none;
    }

    .course-list-item {
      padding: 10px;
      cursor: pointer;
      border-left: 1px solid var(--border-color);
      border-bottom: 1px solid var(--border-color);

      &:first-child {
        border-top: 1px solid var(--border-color);
      }

      &:hover {
        background-color: #F5F7FA;
      }

      &--current {
        background-color: var(--primary-background-hover, rgba(11, 204, 39, 0.1));
      }
    }
    .training-content-left {
      width: 240px;
      border-right: 1px solid #ebeef5;
    }

    .training-content-right {
      flex: 1;
      position: relative;
    }
  }
}
</style>
