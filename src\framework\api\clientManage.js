import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 查询租户列表
  getTenantList(query) {
    return request({
      url: `${cloud.tenant}/sysTenant/getTenantList`,
      method: 'get',
      params: query
    })
  },

  // 新增租户
  addTenant(data) {
    return request({
      url: `${cloud.tenant}/sysTenant/addTenant`,
      method: 'post',
      data
    })
  },
  // 修改租户
  updateTenant(data) {
    return request({
      url: `${cloud.tenant}/sysTenant/updateTenant`,
      method: 'post',
      data
    })
  },
  // 删除租户
  deleteTenant(data) {
    return request({
      url: `${cloud.tenant}/sysTenant/deleteTenant`,
      method: 'post',
      data
    })
  },
  // 租户详情
  detailTenant(id) {
    return request({
      url: `${cloud.tenant}/sysTenant/detailTenant?id=${id}`,
      method: 'get'
    })
  },
  // 修改租户状态
  updateTenantStatus(data) {
    return request({
      url: `${cloud.tenant}/sysTenant/updateTenantStatus`,
      method: 'post',
      data
    })
  },
  // 查询租户管理员
  getTenantManagerList(data) {
    return request({
      url: `${cloud.tenant}/sysTenant/getTenantManagerList`,
      method: 'post',
      data
    })
  },
  // 新增租户管理员
  addTenantManager(data) {
    return request({
      url: `${cloud.tenant}/sysTenant/addTenantManager`,
      method: 'post',
      data
    })
  },
  // 修改租户管理员
  updateTenantManager(data) {
    return request({
      url: `${cloud.tenant}/sysTenant/updateTenantManager`,
      method: 'post',
      data
    })
  },
  // 查看租户管理员详情
  detailTenantManager(data) {
    return request({
      url: `${cloud.tenant}/sysTenant/detailTenantManager`,
      method: 'post',
      data
    })
  },
  // 删除租户管理员
  deleteTenantManager(data) {
    return request({
      url: `${cloud.tenant}/sysTenant/deleteTenantManager`,
      method: 'post',
      data
    })
  },
  // 重置租户管理员密码
  resetTenantManagerPassword(data) {
    return request({
      url: `${cloud.tenant}/sysTenant/resetTenantManagerPassword`,
      method: 'post',
      data
    })
  },
  // 获取租户应用列表
  tenantAppList(query) {
    return request({
      url: `${cloud.manage}/sysApp/tenantAppList`,
      method: 'get',
      params: query
    })
  },
  // 分页查询应用列表
  sysApp(query) {
    return request({
      url: `${cloud.manage}/sysApp/pageExcludeTenant`,
      method: 'get',
      params: query
    })
  },
  // 获取全部应用类型
  sysAppType(query) {
    return request({
      url: `${cloud.manage}/sysAppType/list`,
      method: 'get',
      params: query
    })
  },
  // 通过appCode来获取应用
  getPlatAppListByAppCodes(appCodes) {
    return request({
      url: `${cloud.manage}/sysApp/getPlatAppListByAppCodes`,
      method: 'post',
      data: { appCodes }
    })
  },

  // 设置租户应用
  setTenantApp(tenantId, appIds) {
    return request({
      url: `${cloud.tenant}/sysTenant/setTenantApp`,
      method: 'post',
      data: { tenantId, appIds }
    })
  },

  // 删除租户下的应用
  deleteTenantApp(data) {
    return request({
      url: `${cloud.tenant}/sysTenant/deleteTenantApp`,
      method: 'post',
      params: data
    })
  }
})
