/*
 * @Author: wang<PERSON>xin <EMAIL>
 * @Date: 2024-09-06 14:04:49
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-09-23 15:23:24
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from "@/framework/utils/request"

// 查询作业活动风险列表
export function queryTaskRiskList(params) {
  return request({
    url: "/project/bizRiskWorkActivity/page",
    method: "get",
    params
  })
}

// 新增作业活动风险列表
export function addTaskRisk(data) {
  return request({
    url: "/project/bizRiskWorkActivity/add",
    method: "post",
    data
  })
}

// 编辑作业活动风险
export function editTaskRisk(data) {
  return request({
    url: "/project/bizRiskWorkActivity/edit",
    method: "post",
    data
  })
}

// 删除作业活动风险
export function deleteTaskRisk(data) {
  return request({
    url: "/project/bizRiskWorkActivity/delete",
    method: "post",
    data
  })
}

// 查询作业活动风险详情
export function queryTaskRiskDetailById(params) {
  return request({
    url: "/project/bizRiskWorkActivity/detail",
    method: "get",
    params
  })
}

// 查询作业活动列表
export function queryWorkActivityList(data) {
  return request({
    url: "/project/bizWorkActivity/selectList",
    method: "post",
    data
  })
}
