import { default as request, cloud } from '@/framework/utils/request'

/** *应急专家统计-后台* */
export function getEmergencyExpert(params) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergencyExpertStatistics',
    method: 'get',
    params
  })
}

/** *企业应急物资预警统计-后台* */
export function getEmergSuppliesAffiliated(params) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergSuppliesAffiliatedStatistics',
    method: 'get',
    params
  })
}
/** **应急物资类型统计-后台 */
export function getEmergSuppliesType(params) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getEmergSuppliesTypeStatistics',
    method: 'get',
    params
  })
}

/** *应急物资预警* */
export function getRescuSuppliesWarning(params) {
  return request({
    url: cloud.dqbasic + '/emergencyResources/getRescuSuppliesWarningStatistics',
    method: 'get',
    params
  })
}
