/*
 * @Author: zzp
 * @Date: 2024-04-22 15:53:40
 * @LastEditors:
 * @LastEditTime: 2024-04-22 16:42:26
 * @FilePath: \isrmp_vue\src\api\HealthExaminePlan\health-examine-plan.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud} from '@/framework/utils/request'

// 查询实战应急演练列表
export function bizInspectionTaskPage(query) {
  return request({
    url: cloud.dqbasic + '/bizInspectionTask/page',
    method: 'get',
    params: query
  })
}


