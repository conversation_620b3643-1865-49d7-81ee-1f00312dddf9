import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 业务用户授权-左侧组织树查询
  getOrgTree(params) {
    return request({
      url: `${cloud.permission}/businessUser/orgTree`,
      method: 'get',
      params
    })
  },
  //   业务用户授权-右侧用户列表分页查询（带查询条件）
  getUserPage(params) {
    return request({
      url: `${cloud.permission}/businessUser/userPage`,
      method: 'get',
      params
    })
  },
  //   业务用户授权-用户明细查询
  getUserDetail(params) {
    return request({
      url: `${cloud.permission}/businessUser/userDetail`,
      method: 'get',
      params
    })
  },
  //   业务用户授权-基于角色授权-查询用户角色列表
  getUserRoleList(params) {
    return request({
      url: `${cloud.permission}/businessUser/grantUserRoleList`,
      method: 'get',
      params
    })
  },
  // 业务用户授权-基于角色授权-添加角色-查询当前登录人所在管理员组角色列表
  // getManagerGroupRoleList(params) {
  //   return request({
  //     url: cloud.permission + '/businessUser/userRoleList',
  //     method: 'get',
  //     params
  //   })
  // },
  // 业务用户授权-基于角色授权-添加角色-保存
  addRole(data) {
    return request({
      url: `${cloud.permission}/businessUser/batchAddRole`,
      method: 'POST',
      data
    })
  },
  // 业务用户授权-基于角色授权-删除（支持批量删除）
  deleteRole(data) {
    return request({
      url: `${cloud.permission}/businessUser/batchDeleteRole`,
      method: 'POST',
      data
    })
  }
})
