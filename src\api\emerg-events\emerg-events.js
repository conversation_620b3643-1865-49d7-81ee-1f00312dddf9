/*
 * @Author: mxt
 * @Date: 2024-04-22 15:53:40
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 17:31:39
 * @FilePath: \isrmp_vue\src\api\HealthExaminePlan\health-examine-plan.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud} from '@/framework/utils/request'
import {getRequest, postRequest} from '@/framework/utils/request'

// 查询应急事件数据列表
export function emPraDrillsGetPage(query) {
  return getRequest(cloud.dqbasic + '/emergEventsArchiving/getListPage', query)
}

// 查询应急事件数据详细
export function getEmPraDrill(id) {
  return getRequest(cloud.dqbasic + '/emergEventsArchiving/detail?id=' + id)
}

// 新增应急事件数据
export function addEmPraDrill(data) {
  return postRequest(cloud.dqbasic + '/emergEventsArchiving/add', data)
}

// 修改应急事件数据
export function updateEmPraDrill(data) {
  return postRequest(cloud.dqbasic + '/emergEventsArchiving/edit', data)
}

// 删除应急事件数据
export function delEmPraDrill(id) {
  return postRequest(cloud.dqbasic + '/emergEventsArchiving/delete', { ids: id })
}
