import request, {cloud}  from '@/framework/utils/request'

// 查询重大危险源在线监测列表
export function listGalleryDataMonitor(query) {
  return request({
    url: cloud.dqbasic +'/galleryDataMonitor/page',
    method: 'get',
    params: query
  })
}

// 查询重大危险源在线监测详细
export function getGalleryDataMonitor(id) {
  return request({
    url: cloud.dqbasic +'/galleryDataMonitor/detail?id=' + id,
    method: 'get'
  })
}

// 新增重大危险源在线监测
export function addGalleryDataMonitor(data) {
  return request({
    url: cloud.dqbasic +'/galleryDataMonitor/add',
    method: 'post',
    data: data
  })
}

// 修改重大危险源在线监测
export function updateGalleryDataMonitor(data) {
  return request({
    url: cloud.dqbasic +'/galleryDataMonitor/edit',
    method: 'post',
    data: data
  })
}

// 删除重大危险源在线监测
export function delGalleryDataMonitor(id) {
  return request({
    url: cloud.dqbasic +'/galleryDataMonitor/delete',
    method: 'post',
    data: { ids: id }
  })
}
