<template>
  <div class="">
    <el-dialog
      title="课程详情"
      :visible="analysisVisible"
      width="70%"
      :before-close="handleClose"
    >
      <div style="padding: -10px 30px 20px;">
        <div style="font-size: 18px;font-weight: bold;">
          {{ courseDetail.videoName }} {{ +courseDetail.status === 1 ? '已启用' : '已禁用' }}
        </div>


        <div>
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="学习效果" name="first">
              <div style="margin-top: 15px;">
                <span style="font-weight: bold;font-size: 16px;">
                  学习总人数：{{ learnersNum }}
                </span>

                <span style="margin-left: 70px;font-weight: 700;font-size: 16px;">
                  总完成人数：{{ completedNum }}
                </span>

                <span style="margin-left: 70px;font-weight: 700;font-size: 16px;">
                  累计完成率：{{ completionRate*100+'%' }}
                </span>
              </div>

              <div ref="TabContent" class="TabContent" style="margin-top: 20px;">
                <div id="chart-container" class="chartMain" style="width: 800px;height: 200px;" />
              </div>

              <div ref="completion" class="completion" style="margin-top: 20px;">
                <div id="completion-rate" class="completionRate" style="width: 800px;height: 200px;" />
              </div>
            </el-tab-pane>

            <el-tab-pane label="学员详情" name="second">
              <div style="display: flex;align-items: center;justify-content: space-between;height: 50px;">
                <span>学习数据概览</span>

                <span style="display: flex;align-items: center;">
                  选择部门
                  <DeptSelect
                    v-model="accidentDept"
                    style="margin-left: 10px;"
                    placeholder="请选择部门"
                    @input="handleDeptChange"
                  />
                </span>
              </div>

              <el-table
                ref="table"
                v-loading="loading"
                style="width: 100%;"
                border
                highlight-current-row
                :header-cell-style="{ backgroundColor: '#f2f2f2' }"
                :data="tableData"
              >
                <template slot="empty">
                  <p>{{ $store.getters.dataText }}</p>
                </template>

                <el-table-column
                  type="index"
                  label="序号"
                  width="70"
                  :index="
                    (index) =>
                      (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
                  "
                />

                <el-table-column
                  key="userName"
                  label="姓名"
                  show-overflow-tooltip
                  align="center"
                  prop="userName"
                  min-width="200"
                />

                <el-table-column
                  key="orgName"
                  label="部门"
                  show-overflow-tooltip
                  align="center"
                  prop="orgName"
                  min-width="120"
                />

                <el-table-column
                  key="trainName"
                  label="培训名称"
                  show-overflow-tooltip
                  align="center"
                  prop="trainName"
                  min-width="120"
                />

                <el-table-column
                  key="studyTime"
                  label="学习时长"
                  show-overflow-tooltip
                  align="center"
                  prop="studyTime"
                  min-width="120"
                />

                <el-table-column
                  key="studyPer"
                  label="学习进度"
                  show-overflow-tooltip
                  align="center"
                  prop="studyPer"
                  min-width="120"
                />

                <el-table-column
                  key="firstStudyDate"
                  label="学习时间"
                  show-overflow-tooltip
                  align="center"
                  prop="firstStudyDate"
                  :formatter="dateFormat"
                />

                <!--
                  <el-table-column
                  key="status"
                  label="状态"
                  show-overflow-tooltip
                  align="center"
                  prop="status"
                  min-width="120"
                  >
                  <template slot-scope="scope">
                  <span v-if="scope.row.status == 1">
                  启用
                  </span>

                  <span v-else-if="scope.row.status == 2">
                  禁用
                  </span>
                  </template>
                  </el-table-column>
                -->
              </el-table>

              <dt-pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNo"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { color } from 'echarts/lib/export'
import DeptSelect from '@/components/dept-select/dept-select.vue'
import { selectStudyTrend, getCourseLearningStatistics, selectCompleteTrend, selectStudyPeopleDetailSituation } from '@/api/training-examination/training-course'
import { getFileDetail } from '@/api/common/index'
import moment from 'moment'
export default {
  components: { DeptSelect },
  props:{
    /** 控制弹框显隐 */
    analysisVisible:{
      type:Boolean,
      required: true,
      default:false
    },

    /** 课程详情 */
    courseForm:{
      type:Object,
      required: true,
      default:null
    }
  },

  data() {
    return {
      accidentDept:'',
      url: '',
      activeName: 'second',
      total:0,
      loading:false,
      tableData:[],
      queryParams:{
        orgId:'',
        videoId:'',
        pageNo:1,
        pageSize:10
      },

      learnersNum:0,
      completedNum:0,
      completionRate:0
    }
  },

  computed: {
    showDialog:{
      get() {
        return this.analysisVisible
      },

      set(val) {
        this.$emit('updateDialogVisible', val)
      }
    },

    courseDetail() {
      if (this.courseForm == null) {
        return {
          bannerPath:'',
          videoName:'',
          status:0
        }
      } else {
        return this.courseForm
      }
    }
  },

  watch: {
    courseForm(newVal) {
      console.log(newVal)
      this.queryParams.videoId = newVal.id
      this.getList()
    }
  },

  created() {

  },

  mounted() {

  },

  activated() {


  },

  beforeDestroy() {},

  methods: {
    pick() {
      return new Promise((resolve, reject) => {
        this.pickerResolve = resolve
        this.pickerReject = resolve
      })
    },

    /** 部门选择框改变时事件 */
    handleDeptChange(val) {
      this.queryParams.orgId = val
      this.getList()
    },

    getList() {
      this.loading = true
      selectStudyPeopleDetailSituation(this.queryParams).then((res) => {
        if (res.success = true) {
          this.loading = false
          this.tableData = res.data.rows
          this.total = res.data.totalRows
        }
      })
    },

    /** 弹框关闭按钮操作 */
    handleClose() {
      this.activeName = 'second'
      this.tableData = []
      this.$emit('updateAnalysisVisible', false)
    },

    // 标签页切换事件
    handleClick(tab, event) {
      if (tab.name == 'first') {
        this.initLearningTrendChart()
        this.initCompletionRate()
        this.getCourseLearningStatistics()
      }
    },

    getCourseLearningStatistics() {
      getCourseLearningStatistics({ videoId:this.courseForm.id }).then((res) => {
        this.learnersNum = res.data.peopleNum
        this.completedNum = res.data.completeNum
        this.completionRate = res.data.completePer
      })
    },

    /** 初始化学习趋势柱状图 */
    initLearningTrendChart() {
      const dom = document.getElementById('chart-container')
      dom.style.width = `${window.innerWidth * 0.65}px`
      const myChart = echarts.init(dom, null, {
        renderer: 'canvas',
        useDirtyRect: false
      })


      const option = {
        color:['#5470c6'],
        title: {
          text: '学员学习趋势图'
        },

        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },

        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },

        xAxis: [
          {
            type: 'category',
            data: [],
            axisTick: {
              alignWithLabel: true
            }
          }
        ],

        yAxis: [
          {
            type: 'value'
          }
        ],

        series: [
          {
            name: '人数',
            type: 'bar',
            barWidth: '60%',
            data: []
          }
        ]
      }
      selectStudyTrend({ videoId:this.courseForm.id }).then((res) => {
        res.data.map((val) => {
          option.xAxis[0].data.push(val.mon)
          option.series[0].data.push(val.peopleNum)
        })
        myChart.setOption(option)
      })
    },

    /** 初始化学习趋势柱状图 */
    initCompletionRate() {
      const dom = document.getElementById('completion-rate')
      // dom.style.width = this.$refs.completion.offsetWidth + "px"

      dom.style.width = `${window.innerWidth * 0.65}px`
      const myChart = echarts.init(dom, null, {
        renderer: 'canvas',
        useDirtyRect: false
      })


      const option = {
        color:['#5470c6'],
        title: {
          text: '完课率趋势图'
        },

        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },

        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },

        xAxis: [
          {
            type: 'category',
            data: [],
            axisTick: {
              alignWithLabel: true
            }
          }
        ],

        yAxis: [
          {
            type: 'value'
          }
        ],

        series: [
          {
            name: '完课率',
            type: 'bar',
            barWidth: '60%',
            data: []
          }
        ]
      }
      myChart.setOption(option)
      selectCompleteTrend({ videoId:this.courseForm.id }).then((res) => {
        res.data.map((val) => {
          option.xAxis[0].data.push(val.mon)
          option.series[0].data.push(val.completePer)
        })
        myChart.setOption(option)
      })
    },

    /** 格式化表格列时间 */
    dateFormat(row, column, cellValue) {
    // 假设 cellValue 是一个毫秒数或标准的 JavaScript Date 对象
      if (cellValue != null) {
        return moment(cellValue).format('YYYY-MM-DD HH:mm:ss') // 使用 Moment.js
      } else {
        return null
      }
    }
  }
}
</script>

