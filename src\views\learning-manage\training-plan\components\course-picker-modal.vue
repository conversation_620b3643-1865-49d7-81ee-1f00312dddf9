<template>
  <el-dialog
    class="couse-picker"
    title="选择课程"
    v-bind="$attrs"
    :close-on-click-modal="false"
    top="10vh"
    v-on="$listeners"
    @closed="handleClosed"
    @open="handleOpen"
  >
    <div v-loading="isLoading" class="course-picker-content">
      <el-form label-position="right" inline label-width="0">
        <el-form-item>
          <el-input v-model="queryParams.videoName" placeholder="请输入课程名称" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            查询
          </el-button>
        </el-form-item>
      </el-form>

      <el-table
        ref="tableRef"
        :data="courseList"
        row-key="id"
        height="450px"
        border
        :header-cell-style="{ backgroundColor: '#f2f2f2' }"
        @selection-change="handleSelectChange"
        @row-click="handleRowClick"
      >
        <el-table-column
          type="selection"
          reserve-selection
          align="center"
          :selectable="isSelectable"
        />

        <el-table-column
          type="index"
          label="序号"
          width="70"
          fixed="left"
          :index="
            (index) =>
              (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
          "
        />

        <el-table-column
          label="课程名称"
          prop="videoName"
          show-overflow-tooltip
        />

        <el-table-column
          label="创建时间"
          prop="createTime"
          show-overflow-tooltip
        />
      </el-table>

      <dt-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNo"
        :limit.sync="queryParams.pageSize"
        @pagination="handlePagination"
      />
    </div>

    <div slot="footer">
      <el-button
        type="primary"
        @click="handleConfirm"
      >
        确定
      </el-button>

      <el-button @click="handleCancel">
        取消
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getTrainingVideoList } from '@/api/training-examination/training-course'

export default {
  name: 'CoursePickerModal',
  inheritAttrs: false,
  props: {
    disabledQuestionList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      isLoading: false,
      queryParams: {
        videName: '',
        pageNo: 1,
        pageSize: 10,
        status: 1
      },

      total: 0,
      courseList: [],
      checkedCourseList: []
    }
  },

  methods: {
    isSelectable(row) {
      return this.disabledQuestionList.findIndex((item) => item === row.id) === -1
    },

    handleOpen() {
      this.getList()
    },

    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    handleConfirm() {
      this.$emit('picked', this.checkedCourseList)
      this.$emit('update:visible', false)
    },

    handleCancel() {
      this.$emit('update:visible', false)
    },

    handleClosed() {
      this.reset()
    },

    reset() {
      this.queryParams = {
        videName: '',
        pageNo: 1,
        pageSize: 10,
        status: 1
      }
      this.total = 0
      this.courseList = []
      this.checkedCourseList = []
      if (this.$refs.tableRef) {
        this.$refs.tableRef.clearSelection()
      }
    },

    getList() {
      this.isLoading = true
      getTrainingVideoList(this.queryParams).then((res) => {
        this.courseList = res.data.rows
        this.total = res.data.totalRows
      }).finally(() => {
        this.isLoading = false
      })
    },

    handleSelectChange(selection) {
      this.checkedCourseList = selection
    },

    handlePagination() {
      this.getList()
    },

    handleRowClick(row) {
      if (this.disabledQuestionList.includes(row.id)) return
      this.$refs.tableRef.toggleRowSelection(row)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table__body {
  .el-table__row {
    cursor: pointer
  }
}
</style>
