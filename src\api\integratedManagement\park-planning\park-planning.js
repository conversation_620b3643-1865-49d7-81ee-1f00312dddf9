/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-07-26 09:46:48
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 17:12:15
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud} from '@/framework/utils/request'
import {getRequest, postRequest} from '@/framework/utils/request'

// 查询园区规划列表
export function getList(query) {
  return getRequest(cloud.dqbasic + '/industrialParkDraft/page', query)
}

// 查询园区规划详细
export function getDetail(id) {
  return getRequest(cloud.dqbasic + '/industrialParkDraft/detail?id=' + id)
}

// 新增园区规划
export function add(data) {
  return postRequest(cloud.dqbasic + '/industrialParkDraft/add', data)
}

// 修改园区规划
export function edit(data) {
  return postRequest(cloud.dqbasic + '/industrialParkDraft/edit', data)
}

// 删除园区规划
export function del(id) {
  return postRequest(cloud.dqbasic + '/industrialParkDraft/delete', { ids: id })
}
