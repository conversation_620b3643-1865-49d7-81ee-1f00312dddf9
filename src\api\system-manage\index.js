/*
 * @Author: du<PERSON><PERSON> <EMAIL>
 * @Date: 2024-05-28 15:37:58
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-05-28 15:42:20
 * @FilePath: \isrmp_vue\src\api\system-manage\index.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud} from '@/framework/utils/request'
// 【监控设备管理】分页查询所有数据
export function getList(query) {
  return request({
    url: cloud.dqbasic + '/deviceManager/page',
    method: 'get',
    params: query
  })
}

// 【监控设备管理】新增单条数据
export function add(data) {
  return request({
    url: cloud.dqbasic + '/deviceManager/add',
    method: 'post',
    data
  })
}

// 【监控设备管理】修改单条数据
export function edit(data) {
  return request({
    url: cloud.dqbasic + '/deviceManager/edit',
    method: 'post',
    data
  })
}
// 【监控设备管理】删除
export function deleteDevice(id) {
  return request({
    url: cloud.dqbasic + '/deviceManager/delete',
    method: 'post',
    data: { ids: id }
  })
}
// 【监控设备管理】获取单条数据详情
export function getDetail(id) {
  return request({
    url: cloud.dqbasic + '/deviceManager/detail',
    method: 'get',
    params: {id}

  })
}
