import { default as request, cloud } from '@/framework/utils/request'
/** 作业单据统计 */
export function getVoucher(params) {
  return request({
    url: cloud.dqbasic + '/specialWork/status/statistics',
    method: 'get',
    params
  })
}

/** *历史报警统计* */
export function getHistoryWarn(params) {
  return request({
    url: cloud.dqbasic + '/specialWork/warning/num',
    method: 'get',
    params
  })
}
/** *消警统计* */
export function getDealWarn(params) {
  return request({
    url: cloud.dqbasic + '/specialWork/deal/statistics',
    method: 'get',
    params
  })
}
/** *作业分类统计* */
export function getWorkType(params) {
  return request({
    url: cloud.dqbasic + '/specialWork/type/statistics',
    method: 'get',
    params
  })
}
/** *作业状态统计* */
export function getWorkStatus(params) {
  return request({
    url: cloud.dqbasic + '/specialWork/statistics',
    method: 'get',
    params
  })
}
/** *企业作业统计* */
export function getEnterprise(params) {
  return request({
    url: cloud.dqbasic + '/specialWork/enterprise/statistics',
    method: 'get',
    params
  })
}
/** *实时报警* */
export function getAlarm(params) {
  return request({
    url: cloud.dqbasic + '/specialWork/alarm/data',
    method: 'get',
    params
  })
}
// 大屏中间 数据报警详情
export function getDataAlarm(params) {
  return request({
    url: cloud.dqbasic + '/specialWork/map/data',
    method: 'get',
    params
  })
}
// 大屏中间 视频报警详情
export function getVideoAlarm(params) {
  return request({
    url: cloud.dqbasic + '/specialWork/map/video',
    method: 'get',
    params
  })
}

export function addAlarmResult(data) {
  return request({
    url: cloud.dqbasic + '/specialWork/result',
    method: 'post',
    data:data
  })
}
// 作业分类，作业状态  字典
export function workDict(dictCode) {
  return request({
    url: cloud.dqbasic + '/specialWork/dict',
    method: 'get',
    params: {
      dictCode
    }
  })
}
// 分页查询特殊作业管理所有数据
export function getWorkPage(params) {
  return request({
    url: cloud.dqbasic + '/specialWorkManager/page',
    method: 'get',
    params
  })
}
// 获取作业下预警列表
export function getalarmVideo(workId) {
  return request({
    url: cloud.dqbasic + '/specialWork/check/video',
    method: 'get',
    params:{
      workId
    }
  })
}

// 大屏  作业统计
export function getWorkStatistics() {
  return request({
    url: cloud.dqbasic + '/specialWork/work/statistics',
    method: 'get'
  })
}

// 大屏  作业统计 弹窗数据
export function getWorkDialogData(params) {
  return request({
    url: cloud.dqbasic + '/specialWork/work/page',
    method: 'get',
    params
  })
}

// 大屏  报警统计
export function getAlarmStatistics() {
  return request({
    url: cloud.dqbasic + '/specialWork/warning/num',
    method: 'get'
  })
}

// 大屏  报警统计 弹窗数据
export function getWarnDialogData(params) {
  return request({
    url: cloud.dqbasic + '/specialWork/alarm/page',
    method: 'get',
    params
  })
}

// 大屏  数据报警类  弹窗数据
export function getWarnDataDialogData(params) {
  return request({
    url: cloud.dqbasic + '/specialWorkDataWarning/detail',
    method: 'get',
    params
  })
}

// 大屏  视频报警类  弹窗数据
export function getWarnVideoDialogData(params) {
  return request({
    url: cloud.dqbasic + '/videoWarning/detail',
    method: 'get',
    params
  })
}

// 大屏  作业验收统计
export function getWorkCheckData() {
  return request({
    url: cloud.dqbasic + '/specialWork/check/statistics',
    method: 'get'
  })
}

// 大屏  作业验收统计  弹窗数据
export function getWorkCheckDialogData(params) {
  return request({
    url: cloud.dqbasic + '/specialWork/check/page',
    method: 'get',
    params
  })
}

// 大屏中间  地图上报警点
export function getMapAlarm() {
  return request({
    url: cloud.dqbasic + '/specialWork/map/statistics',
    method: 'get'
  })
}

// 根据当前作业管理id 查询 监控设备、相关详情、点位信息、实时报警信息
export function getPanelDialogData(params) {
  return request({
    url: cloud.dqbasic + '/specialWorkManager/onlineCheckWork',
    method: 'get',
    params
  })
}

// 数据类 报警处理
export function dataDeal(data) {
  return request({
    url: cloud.dqbasic + '/specialWorkDataWarning/deal',
    method: 'post',
    data
  })
}

// 视频类 报警处理
export function videoDeal(data) {
  return request({
    url: cloud.dqbasic + '/videoWarning/deal',
    method: 'post',
    data
  })
}

// 【管理端】 分页查询数据预警所有数据
export function getDataWarnList(params) {
  return request({
    url: cloud.dqbasic + '/dataWarning/page',
    method: 'get',
    params
  })
}

// 【管理端】 分页查询视频预警所有数据
export function getVideoWarnList(params) {
  return request({
    url: cloud.dqbasic + '/videoWarning/page',
    method: 'get',
    params
  })
}
