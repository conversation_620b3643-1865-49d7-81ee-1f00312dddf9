<template>
  <div class="training-time-selector">
    <el-select
      class="type-selector"
      placeholder="请选择"
      style="width: 120px;"
      :value="timeType"
      @change="handleTimeTypeChange"
    >
      <el-option label="按月" :value="0" />

      <el-option label="其他" :value="1" />
    </el-select>

    <el-select
      v-if="timeType === TYPES.MONTH"
      v-model="range"
      class="range-selector"
      multiple
      placeholder="请选择月份"
    >
      <el-option
        v-for="month in monthOptions"
        :key="month.value"
        :label="month.label"
        :value="month.value"
      />
    </el-select>

    <el-input
      v-else v-model.trim="range" class="range-input"
      placeholder="请输入"
    />
  </div>
</template>

<script>
export default {
  props: {
    timeType: {
      type: Number,
      required: true,
      default: 0,
      validator: (val) => [0, 1].includes(val)
    },

    timeRange: {
      type: String,
      required: true,
      default: () => ''
    }
  },

  data() {
    return {
      TYPES: {
        MONTH: 0,
        OTHER: 1
      },

      monthOptions: Array.from({ length: 12 }, (_, i) => ({
        label: `${i + 1}月`,
        value: `${i + 1}`
      }))
    }
  },

  computed: {
    range: {
      get() {
        if (this.timeType === this.TYPES.MONTH) {
          return this.timeRange ? this.timeRange.split(',') : []
        } else {
          return this.timeRange
        }
      },

      set(val) {
        if (this.timeType === this.TYPES.MONTH) {
          this.$emit('update:timeRange', val.join(','))
        } else {
          this.$emit('update:timeRange', val)
        }
      }
    }
  },

  methods: {
    handleTimeTypeChange(newType) {
      this.$emit('update:timeType', newType)
      this.$emit('update:timeRange', '')
    }
  }
}
</script>

<style lang="scss" scoped>
.training-time-selector {
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;

  .type-selector {
    flex: 0 0 80px;
    min-width: 0;
  }

  .time-selector{
    flex: 1 1 auto;
  }
}
</style>
