import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 查询列表
  customFormPluginPage(params) {
    return request({
      url: `${cloud.devcenter}/customFormPlugin/page`,
      method: 'get',
      params
    })
  },
  // 插件删除
  customFormPluginDelete(data) {
    return request({
      url: `${cloud.devcenter}/customFormPlugin/delete`,
      method: 'POST',
      data
    })
  },
  // 插件新增
  customFormPluginAdd(data) {
    return request({
      url: `${cloud.devcenter}/customFormPlugin/add`,
      method: 'POST',
      data
    })
  },
  // 插件修改
  customFormPluginEdit(data) {
    return request({
      url: `${cloud.devcenter}/customFormPlugin/edit`,
      method: 'POST',
      data
    })
  },
  // 查询详细信息
  customFormPluginDetail(params) {
    return request({
      url: `${cloud.devcenter}/customFormPlugin/detail`,
      method: 'get',
      params
    })
  },
  resolveFieldAssociatContent(data) {
    return request({
      url: `${cloud.devcenter}/customFormInfo/resolveFieldAssociatContent`,
      method: 'POST',
      data
    })
  }

})
