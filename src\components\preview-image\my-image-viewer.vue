<!--
  * @Author: jiadongjin <EMAIL>
  * @Date: 2024-05-16 11:19:31
  * @LastEditors: gaoyu <EMAIL>
  * @LastEditTime: 2025-05-06 10:55:33
  * @Description:
  * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
-->
<template>
  <transition name="viewer-fade">
    <div
      ref="el-image-viewer__wrapper" tabindex="-1" class="el-image-viewer__wrapper"
      :style="{ 'z-index': viewerZIndex }"
    >
      <div class="el-image-viewer__mask" @click.self="handleMaskClick" />
      <!-- ARROW -->
      <template v-if="!isSingle">
        <span
          class="el-image-viewer__btn el-image-viewer__prev"
          :class="{ 'is-disabled': !infinite && isFirst }"
          @click="prev"
        >
          <i class="el-icon-arrow-left" />
        </span>

        <span
          class="el-image-viewer__btn el-image-viewer__next"
          :class="{ 'is-disabled': !infinite && isLast }"
          @click="next"
        >
          <i class="el-icon-arrow-right" />
        </span>
      </template>
      <!-- ACTIONS -->
      <div class="el-image-viewer__btn el-image-viewer__actions">
        <div class="el-image-viewer__actions__inner">
          <i class="el-icon-zoom-out" @click="handleActions('zoomOut')" />

          <i class="el-icon-zoom-in" @click="handleActions('zoomIn')" />

          <i class="el-image-viewer__actions__divider" />
          <!-- <i :class="mode.icon" @click="toggleMode"></i> -->
          <i class="el-image-viewer__actions__divider" />

          <i class="el-icon-refresh-left" @click="handleActions('anticlocelise')" />

          <i class="el-icon-refresh-right" @click="handleActions('clocelise')" />
          <!-- ARROW -->
          <i v-if="!isSingle " class="el-icon-arrow-left" @click="prev" />

          <i v-if="!isSingle" class="el-icon-arrow-right" @click="next" />

          <i class="el-image-viewer__actions__divider" />
          <!-- CLOSE -->
          <i class="el-icon-close" @click="hide" />
        </div>
      </div>
      <!-- CANVAS -->
      <div class="el-image-viewer__canvas">
        <template v-for="(url, i) in urlList">
          <div
            v-if="i === index"
            :key="url"
            ref="img"
            class="el-image-viewer__img"
            :style="Object.assign({
              'background-image': `url(${currentImg})`,
              'background-repeat': 'no-repeat',
              'object-fit': 'fill',
              'width': `${naturalWidth}px`,
              'height': `${naturalHeight}px
                `}, imgStyle) "
            :draggable="true"
            referrerpolicy="no-referrer"
            @mousedown="handleMouseDown"
          >
            <img
              id="inner_img" class="el-image-viewer" alt=""
              :src="currentImg"
            >

            <div class="mask" />
          </div>
        </template>
      </div>
    </div>
  </transition>
</template>

<script>
// 继承element-ui image-viewer，修改缩放阻尼 原0.015 -> 新0.2

import ImageViewer from 'element-ui/packages/image/src/image-viewer'
import { rafThrottle, isFirefox } from 'element-ui/src/utils/util'
import { on, off } from 'element-ui/src/utils/dom'

const Mode = {
  CONTAIN: {
    name: 'contain',
    icon: 'el-icon-full-screen'
  },
  ORIGINAL: {
    name: 'original',
    icon: 'el-icon-c-scale-to-original'
  }
}

const mousewheelEventName = isFirefox() ? 'DOMMouseScroll' : 'mousewheel'

export default {
  name: 'MyImageViewer',
  extends: ImageViewer,
  data() {
    return {
      naturalWidth: 0, // 图片原始宽度
      naturalHeight: 0, // 图片原始高度

      mode: Mode.ORIGINAL, // 原图模式

      largeImageFlag: false // 是否为高分辨率大图
    }
  },

  computed: {
    currentImg() {
      const _item = this.urlList[this.index]
      this.loadImage(_item)
      return _item
    },

    // 重写 imgStyle
    imgStyle() {
      const { scale, deg, offsetX, offsetY, enableTransition } = this.transform

      const style = {
        transform: `scale(${scale}) rotate(${deg}deg)`,
        transition: enableTransition ? 'transform .3s' : '',
        'position': 'absolute',
        'left': `${offsetX}px`,
        'top': `${offsetY}px`
      }

      if (this.mode === Mode.CONTAIN) {
        style.maxWidth = '100%'
        style.maxHeight = '100%'
        // style.maxWidth = style.maxHeight = '100%'
      }
      return style
    }
  },

  watch: {
    // 每次切换图片之后 重新绑定双击事件
    index(val) {
      this.$nextTick().then(() => {
        if (this.$refs.img && this.$refs.img.length > 0) {
          this.$refs.img[0].addEventListener('dblclick', this.onImgDblClick, false)
        }
      })
    }
  },

  mounted() {
    // 注册初始图片双击事件
    if (this.$refs.img && this.$refs.img.length > 0) {
      this.$refs.img[0].addEventListener('dblclick', this.onImgDblClick, false)
    }
  },

  beforeDestroy() {
    // 移除图片双击事件
    if (this.$refs.img && this.$refs.img.length > 0) {
      this.$refs.img[0].removeEventListener('dblclick', this.onImgDblClick)
    }
  },

  methods: {
    // 方法重写
    deviceSupportInstall() {
      this._keyDownHandler = (e) => {
        e.stopPropagation()
        const keyCode = e.keyCode
        switch (keyCode) {
          // ESC
          case 27:
            this.hide()
            break
            // SPACE
          case 32:
            this.toggleMode()
            break
            // LEFT_ARROW
          case 37:
            this.prev()
            break
            // UP_ARROW
          case 38:
            this.handleActions('zoomIn')
            break
            // RIGHT_ARROW
          case 39:
            this.next()
            break
            // DOWN_ARROW
          case 40:
            this.handleActions('zoomOut')
            break
        }
      }
      this._mouseWheelHandler = rafThrottle((e) => {
        const delta = e.wheelDelta ? e.wheelDelta : -e.detail
        if (delta > 0) {
          this.handleActions('zoomIn', {
            // 修改缩放阻尼 原0.015 -> 新0.2
            zoomRate: 0.2,
            enableTransition: false
          })
        } else {
          this.handleActions('zoomOut', {
            // 修改缩放阻尼 原0.015 -> 新0.2
            zoomRate: 0.2,
            enableTransition: false
          })
        }
      })
      on(document, 'keydown', this._keyDownHandler)
      on(document, mousewheelEventName, this._mouseWheelHandler)
    },

    // 方法重写； 改成 第一张时 不能继续点击上一张
    prev() {
      if (this.isFirst && !this.infinite) return
      const len = this.urlList.length

      if (this.index == 0) {
        this.$message.warning('当前为第一张图片')
        return
      }

      this.index = (this.index - 1 + len) % len
    },

    // 方法重写； 改成 最后一张时 不能继续点击下一张
    next() {
      if (this.isLast && !this.infinite) return
      const len = this.urlList.length

      if (this.index == len - 1) {
        this.$message.warning('当前为最后一张图片')
        return
      }

      this.index = (this.index + 1) % len
    },

    // 双击图片
    onImgDblClick() {
      // 双击关闭图片
      this.hide()
    },

    // 重写 关闭图片
    hide() {
      this.deviceSupportUninstall()
      this.onClose()
    },

    // 重写 单击图片
    handleMouseDown(e) {
      // 只允许鼠标左键移动图片
      if (this.loading || e.button !== 0) return

      const { offsetX, offsetY } = this.transform
      const startX = e.pageX
      const startY = e.pageY
      this._dragHandler = rafThrottle((ev) => {
        this.transform.offsetX = offsetX + ev.pageX - startX
        this.transform.offsetY = offsetY + ev.pageY - startY
      })
      on(document, 'mousemove', this._dragHandler)
      on(document, 'mouseup', (ev) => {
        off(document, 'mousemove', this._dragHandler)
      })

      e.preventDefault()
    },

    // 重写 图片onload
    handleImgLoad(e) {
      this.loading = false

      // 获取图片原始宽高
      this.naturalWidth = e.target.width
      this.naturalHeight = e.target.height

      // 以高度为基准计算缩放比例
      this.calculateScaleWithHeight()
    },

    // 以宽度为基准计算缩放比例
    calculateScaleWithWidth() {
      // 以图片宽度为基准，宽度缩放为 【浏览器可视区域宽度 - 右侧标签所占宽度】 计算缩放比例
      const _scale = document.body.clientWidth * 0.7 / this.naturalWidth
      // 计算offsetX
      const _offsetX = (document.body.clientWidth * 0.7 - this.naturalWidth) / 2
      // 计算offsetY
      const _offsetY = (document.body.clientHeight - this.naturalHeight) / 2

      this.transform.scale = _scale
      this.transform.offsetX = _offsetX
      this.transform.offsetY = _offsetY

      this.largeImageFlag = _scale < 0.7
    },

    // 以高度为基准计算缩放比例
    calculateScaleWithHeight() {
      // 以图片高度为基准，高度缩放为浏览器可视区域一样高 计算缩放比例
      const _scale = document.body.clientHeight / this.naturalHeight
      // 计算offsetX
      const _offsetX = (document.body.clientWidth - this.naturalWidth) / 2
      // 计算offsetY
      const _offsetY = (document.body.clientHeight - this.naturalHeight) / 2

      this.transform.scale = _scale
      this.transform.offsetX = _offsetX
      this.transform.offsetY = _offsetY

      this.largeImageFlag = _scale < 0.7
    },

    // 重写reset方法 避免切换图片时闪烁
    reset() {

    },

    // 加载图片
    loadImage(src) {
      // reset status
      this.loading = true

      const img = new Image()
      img.onload = (e) => this.handleImgLoad(e, img)
      img.onerror = this.handleImgError.bind(this)

      img.src = src
    }
  }
}
</script>

<style lang="scss" scoped>
    .el-image-viewer__wrapper {
      z-index: 3000 !important;

      .el-image-viewer__btn.el-image-viewer__actions {
        transform: translateX(-50%) scale(2);
      }
    }

    .el-image-viewer__btn.el-image-viewer__prev {
      left: 20%;
      transform: translateY(-50%) scale(2);
    }

    .el-image-viewer__btn.el-image-viewer__next {
      right: 20%;
      transform: translateY(-50%) scale(2);
    }

    .el-image-viewer__canvas {
      #inner_img {
        opacity: 0;
      }

      .mask {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
        width: 100%;
        height: 100%;
      }
    }
</style>
