import request, { cloud } from '@/framework/utils/request'

// 查询积分明细列表
export function listIntegrationDetail(query) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationDetail/page',
    method: 'get',
    params: query
  })
}

// 查询积分排行列表
export function listIntegrationDetailRanking(query) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationDetail/ranking',
    method: 'get',
    params: query
  })
}
// 获取用户积分排名
export function getUserRanking() {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationDetail/getUserRanking',
    method: 'get',
  })
}

// 查询积分明细详细
export function getIntegrationDetail(id) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationDetail/detail?id=' + id,
    method: 'get'
  })
}

// 新增积分明细
export function addIntegrationDetail(data) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationDetail/add',
    method: 'post',
    data: data
  })
}

// 修改积分明细
export function updateIntegrationDetail(data) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationDetail/edit',
    method: 'post',
    data: data
  })
}

// 删除积分明细
export function delIntegrationDetail(id) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationDetail/delete',
    method: 'post',
    data: { ids: id }
  })
}
