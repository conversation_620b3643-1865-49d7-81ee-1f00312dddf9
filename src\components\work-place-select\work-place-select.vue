<!--
  * @Author: jiadongjin <EMAIL>
  * @Date: 2024-05-17 13:43:32
  * @LastEditors: gaoyu <EMAIL>
  * @LastEditTime: 2025-04-30 16:50:08
  * @Description:
  * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
-->
<template>
  <el-popover
    v-model="popoverVisible"
    placement="top-start"
    trigger="click"
  >
    <el-tree
      class="tree"
      width="100%"
      :data="treePopover"
      :load="loadPopover"
      lazy
      :props="defaultProps"
      :default-expand-all="false"
      :expand-on-click-node="false"
      @node-click="selectOrg"
    />

    <el-input
      slot="reference"
      v-model="placeName"
      :readonly="true"
      placeholder="请选择作业场所"
      :disabled="disabled"
      @focus="orgFocus"
    />
  </el-popover>
</template>

<script>
import { bizWorkPlaceSelect } from '@/api/bussiness-data/workplace-manage/workplace-manage'

export default {
  props: {
    /**
     * 场所id
     */
    value: {
      type: String,
      default: ''
    },

    /**
     * 默认场所名称
     */
    defalutPlaceName: {
      type: String,
      default: ''
    },

    /**
     * 是否禁用
     */
    disabled: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      popoverVisible: false,
      treePopover: [],
      defaultProps: {
        children: 'children',
        label: 'placeName'
      },

      placeName: this.defalutPlaceName || ''
    }
  },

  watch: {
    value(val) {
      if (!val) {
        this.placeName = ''
      }
    },

    defalutPlaceName(val) {
      this.placeName = val
    }
  },

  methods: {
    /**
     * @description: 获取作业场所
     * @param {*} tree
     * @param {*} resolve
     */
    loadPopover(tree, resolve) {
      let parentPlaceId = ''
      if (!tree.data || tree.data.length == 0) {
        parentPlaceId = -1
      } else {
        parentPlaceId = tree.data.placeId
      }
      bizWorkPlaceSelect({ parentPlaceId }).then((res) => {
        const { data } = res
        resolve(data)
      })
    },

    // 选择场所
    selectOrg(data) {
      this.popoverVisible = false
      this.placeName = data.placeName

      this.$emit('input', data.placeId)
    },

    orgFocus() {
      bizWorkPlaceSelect({ parentPlaceId:  -1 })
        .then((res) => {
          this.placeList = res.data
        })
    }
  }
}
</script>
