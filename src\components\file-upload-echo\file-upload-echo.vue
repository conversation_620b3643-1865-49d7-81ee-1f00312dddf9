<!--
  * @Author: jiadongjin <EMAIL>
  * @Date: 2024-05-16 15:41:01
  * @LastEditors: gaoyu <EMAIL>
  * @LastEditTime: 2025-04-28 16:00:38
  * @Description:
  * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
-->
<template>
  <div class="upload-file">
    <el-upload
      ref="upload" multiple :action="uploadFileUrl"
      :before-upload="handleBeforeUpload" :file-list="fileList"
      :limit="fileLimit" :on-error="handleUploadError" :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false" :headers="headers" :data="paramsData"
      :disabled="uploadDisabled" class="upload-file-uploader" :style="{'display': uploadDisabled ? 'none' : 'block'}"
    >
      <!-- 上传按钮 -->
      <el-button
        v-if="!uploadDisabled" :disabled="uploadDisabled" size="mini"
        type="primary"
      >
        选取文件
      </el-button>
      <!-- 上传提示 -->
      <div v-if="showTip && !uploadDisabled" slot="tip" class="el-upload__tip">
        请上传
        <template v-if="fileSize!=0">
          大小不超过 <b>{{ fileSize }}MB</b>
        </template>

        <template v-if="fileType">
          格式为 <b>{{ fileType.join("/") }}</b>
        </template>
        的文件
      </div>
    </el-upload>

    <!-- 文件列表 -->
    <transition-group
      v-if="showFileList && fileList.length > 0 && !showPreview" class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear"
      tag="ul"
    >
      <li
        v-for="(file, index) in fileList" :key="file.url" class="el-upload-list__item ele-upload-list__item-content"
        :class="{'upload-disabled': uploadDisabled}" :style="{top: `${index * 40}px`}"
        @click.capture="showVideoPreviewModal($event, file.url)"
      >
        <el-link :href="`${file.url}`" :underline="false" target="_blank">
          <span class="el-icon-document">
            {{ getFileName(file.name) }}
          </span>
        </el-link>

        <div v-if="!uploadDisabled" class="ele-upload-list__item-content-action">
          <el-link :underline="false" type="danger" @click="handleDelete(index)">
            删除
          </el-link>
        </div>
      </li>
    </transition-group>

    <div v-if="showFileList && fileList.length > 0 && showPreview">
      <div
        v-for="(file, index) in fileList" :key="file.url" class="previewName"
        :style="{top: `${index * 40}px`}"
      >
        <div style="display: flex;">
          <div @click.capture="showDocPreviewModal($event, file.url,file)">
            {{ getFileName(file.name) }}
          </div>

          <div
            v-if="!uploadDisabled" class="ele-upload-list__item-content-action" style="width: 50px;margin-left: 15px;"
          >
            <el-link :underline="false" type="danger" @click="handleDelete(index)">
              删除
            </el-link>
          </div>
        </div>
      </div>
    </div>

    <!-- 视频 模态框 -->
    <VideoPreviewModal ref="videoPreviewModal" :detect-video="currentDetectVideo" />

    <DocPreviewModal ref="dovPreviewModal" />

    <FileDetail ref="fileDetailRef" :check-evidence="filesId" />
  </div>
</template>

<script>
import { getToken } from '@/framework/utils/auth' // get token from cookie
import { cloud } from '@/framework/utils/request'
import emitter from 'element-ui/src/mixins/emitter'
import VideoPreviewModal from './video-preview-modal'
import { getFileDetail } from '@/api/common/index'
import DocPreviewModal from './doc-preview-modal.vue'
import FileDetail from '@/views/work-safety/work-check/components/file-detail.vue'

export default {
  components: { VideoPreviewModal, DocPreviewModal, FileDetail },
  mixins: [emitter],
  inject: {
    elForm: {
      default: ''
    }
  },

  props: {
    /**
     * 数量限制
     */
    fileLimit: {
      type: Number,
      default: 5
    },

    /**
     * 大小限制(MB)
     */
    fileSize: {
      type: Number,
      default: 5
    },

    /**
     * 文件类型, 例如['png', 'jpg', 'jpeg']
     */
    fileType: {
      type: Array,
      default: () => ['doc', 'docx', 'xlsx', 'xls', 'ppt', 'txt', 'pdf']
    },

    /**
     * 是否显示提示
     */
    isShowTip: {
      type: Boolean,
      default: true
    },

    /**
     * 是否禁用
     */
    disabled: {
      type: Boolean,
      default: false
    },

    /**
     * 上传接口
     */
    fileUrl:{
      type: String,
      default: '/project/bizFileInfo/put-file'
    },

    /**
     * 是否显示文件列表
     */
    showFileList:{
      type: Boolean,
      default: true
    },

    /**
     * 文件id
     */
    fileId: {
      type: String,
      default: ''
    },

    /**
     * 是否点击显示视频弹窗
     */
    showVideo: {
      type: Boolean,
      default: false
    },

    /** 是否文件预览 */
    showPreview: {
      type: Boolean,
      default: false
    },

    /**
     * @description: 是否为培训课程
     * @return {*}
     */
    isTrain:{
      type: Boolean,
      default: false
    }

  },

  data() {
    return {
      number: 0,
      uploadList: [],
      /*
       * baseUrl:  process.env.VUE_APP_BASE_API,
       * uploadFileUrl: process.env.VUE_APP_BASE_API + cloud.file + '/sysFileInfo/uploadfile', // 上传地址，必填,
       */
      paramsData: {
        secretFlag: 'N'
      }, // 上传携带的参数，看需求要不要

      headers: {
        'Authorization': getToken()
      },

      fileList: [],

      // 当前点击的视频地址
      currentDetectVideo: '',
      // 当前点击的文档预览地址
      currentDocUrl: '',
      filesId:'111'
    }
  },

  computed: {
    uploadFileUrl() {
      try {
        return process.env.VUE_APP_BASE_API + this.fileUrl
      } catch (error) {
        return this.globalAttr.VUE_APP_BASE_API + this.fileUrl
      }
    },

    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize)
    },

    uploadDisabled() {
      return this.disabled || (this.elForm || {}).disabled
    }
  },

  watch: {
    fileId: {
      handler(newVal) {
        if (!newVal) {
          this.fileList = []
          return
        }
        if (this.isFileListUpdated(newVal)) {
          Promise.all(newVal.split(',').map((v) => v !== 'null' ? getFileDetail(v) : Promise.resolve({}))).then((resList) => {
            this.fileList = resList.map((item) => {
              return {
                id: item.data?.id || '',
                name: item.data?.fileOriginName || '',
                url: item.data?.fileLink || ''
              }
            })
          })
        }
      },

      immediate: true
    },

    fileList(val) {
      let _fileId

      if (val && val.length > 0) {
        _fileId = val.map((v) => v.id).join(',')
      } else {
        _fileId = ''
      }

      this.$emit('update:fileId', _fileId)

      if (!this.disabled) {
        this.dispatch('ElFormItem', 'el.form.change', _fileId)
      }
    }
  },

  methods: {
    /**
     * @description: 检查文件列表是否需要更新
     * @param {*} newVal
     * @return {boolean} 检查文件列表是否需要更新
     */
    isFileListUpdated(newVal) {
      return this.fileList.map((v) => v.id).indexOf(newVal) == -1
    },

    /**
     * @description: 更新文件列表
     * @param {*} newVal
     */
    updateFileList(newVal) {
      Promise.all(newVal.split(',').map((v) => v !== 'null' ? getFileDetail(v) : Promise.resolve({}))).then((resList) => {
        this.fileList = resList.map((item) => {
          return {
            id: item.data ? item.data.id : '',
            name: item.data ? item.data.fileOriginName : '',
            url: item.data ? item.data.fileLink : ''
          }
        })
      })
    },

    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true
          if (fileExtension && fileExtension.indexOf(type) > -1) return true
          return false
        })
        if (!isTypeOk) {
          this.$dtModal.msgError(`文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`)
          return false
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$dtModal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      this.$dtModal.loading('正在上传文件，请稍候...')
      this.number++
      return true
    },

    // 文件个数超出
    handleExceed() {
      this.$dtModal.msgError(`上传文件数量不能超过 ${this.fileLimit} 个!`)
    },

    // 上传失败
    handleUploadError(err) {
      this.$dtModal.msgError('上传文件失败，请重试')
      this.$dtModal.closeLoading()
    },

    // 上传成功回调
    handleUploadSuccess(res) {
      this.uploadList.push({ name: res.data.fileOriginName, url: res.data.fileLink, id: res.data.id })
      if (this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList)
        this.uploadList = []
        this.number = 0

        this.$dtModal.closeLoading()
      }
    },

    // 删除文件
    handleDelete(index) {
      this.fileList.splice(index, 1)
    },

    // 获取文件名称
    getFileName(name) {
      if (name.lastIndexOf('/') > -1) {
        return name.slice(name.lastIndexOf('/') + 1)
      } else {
        return name
      }
    },

    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = ''
      separator = separator || ','
      for (const i in list) {
        strs += list[i].url + separator
      }
      return strs != '' ? strs.substr(0, strs.length - 1) : ''
    },

    // 显示视频预览
    showVideoPreviewModal(event, detectVideo) {
      if (this.showVideo) {
        event.preventDefault()
        event.stopPropagation()
        this.currentDetectVideo = detectVideo
        this.$refs.videoPreviewModal.show()
      }
    },

    // 显示文件预览
    showDocPreviewModal(event, url, file) {
      this.filesId = file.id
      if (this.showPreview) {
        if (this.isTrain) {
          this.$nextTick().then(() => {
            this.$set(this, 'filesId', file.id)
            this.$refs.fileDetailRef.openDialog()
          })
          return
        }
        event.preventDefault()
        event.stopPropagation()
        this.$refs.dovPreviewModal.show(url)
      }
    }

  }
}
</script>

<style scoped lang="scss">
  .upload-file-uploader {
    margin-bottom: 5px;
  }

  .upload-file-list .el-upload-list__item {
    position: relative;
    margin-bottom: 10px;
    line-height: 2;
    border: 1px solid #e4e7ed;

    &.upload-disabled {
      margin: 0;
    }
  }

  .upload-file-list .ele-upload-list__item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: inherit;
  }

  .upload-file-list {
    position: relative;
    width: 100%;
    height: 30px;

    .el-upload-list__item {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      width: 100%;
      height: 100%;

      .el-link {
        flex: auto;
        width: 0;
      }

      .ele-upload-list__item-content-action {
        flex: 0 0 50px;
        width: 0;

        a {
          width: 100%;
          text-align: center;

          ::v-deep &>span {
            position: relative;
            top: -3px;
          }
        }
      }
    }
  }

  .ele-upload-list__item-content-action .el-link {
    margin-right: 10px;
  }

  ::v-deep .el-upload-list__item {
    width: 100%;

    a {
      width: 100%;

      .el-link--inner {
        width: 100%;

        .el-icon-document {
          display: inline-block;
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .previewName {
    color: #3461ff;
    cursor: pointer;
  }
</style>
