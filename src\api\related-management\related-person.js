/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-07-03 16:59:31
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-07-10 15:25:18
 * @FilePath: \isrmp_vue\src\api\related-management\related-person.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import { default as request, cloud } from '@/framework/utils/request'

// 查询相关方-人员列表
export function listPerson(query) {
  return request({
    url: cloud.dqbasic + '/relatePerson/page',
    method: 'get',
    params: query
  })
}

// 查询相关方-人员详细
export function getPerson(id) {
  return request({
    url: cloud.dqbasic + '/relatePerson/detail?id=' + id,
    method: 'get'
  })
}

// 新增相关方-人员
export function addPerson(data) {
  return request({
    url: cloud.dqbasic + '/relatePerson/add',
    method: 'post',
    data: data
  })
}

// 修改相关方-人员
export function updatePerson(data) {
  return request({
    url: cloud.dqbasic + '/relatePerson/edit',
    method: 'post',
    data: data
  })
}

// 删除相关方-人员
export function delPerson(id) {
  return request({
    url: cloud.dqbasic + '/relatePerson/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 修改相关方人员状态
export function updatePersonStatus(data) {
  return request({
    url: cloud.dqbasic + '/relatePerson/updateStatus',
    method: 'post',
    data: data
  })
}

// 下载模板
export function downTemplate() {
  return request({
    url: cloud.dqbasic + '/relatePerson/exportTemplate',
    method: 'get',
    responseType: 'arraybuffer'
  })
}

// 导入
export function importTeamExcel(data) {
  return request({
    url: cloud.dqbasic + '/relatePerson/import',
    method: 'post',
    data
  })
}

// 导出
export function exportExcel(query) {
  return request({
    url: cloud.dqbasic + '/relatePerson/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
