/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-07-24 13:56:27
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-08-29 15:48:32
 * @FilePath: \isrmp_vue\src\api\hidden-danger-solve\statistic.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud}  from '@/framework/utils/request'
/** 数量统计 */
export function getQuantity(query) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/statistics/num',
    method: 'get',
    params: query
  })
}

/** 上报排名 */
export function getRank(query) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/statistics/rank',
    method: 'get',
    params: query
  })
}

/** 整改统计 */
export function getRectify(query) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/statistics/rectify',
    method: 'get',
    params: query
  })
}

/** 趋势统计 */
export function getTrend(query) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/statistics/trend',
    method: 'get',
    params: query
  })
}

/** 类别统计 */
export function getType(query) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/statistics/type',
    method: 'get',
    params: query
  })
}
/** 隐患统计清单 */
export function hiddenDangerStatisticsDetail(query) {
  return request({
    url: cloud.dqbasic + '/bizHiddenDanger/statistics/enterpriseRank',
    method: 'get',
    params: query
  })
}

