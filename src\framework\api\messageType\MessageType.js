import { default as request, cloud } from '@/framework/utils/request'

// 查询消息类型列表
export function listMessageType(query) {
  return request({
    url: `${cloud.notice}/sysMessageType/page/V2`,
    method: 'get',
    params: query
  })
}

// 查询消息类型详细
export function getMessageType(typeId) {
  return request({
    url: `${cloud.notice}/sysMessageType/detail?typeId=${typeId}`,
    method: 'get'
  })
}

// 新增消息类型
export function addMessageType(data) {
  return request({
    url: `${cloud.notice}/sysMessageType/add`,
    method: 'post',
    data
  })
}

// 修改消息类型
export function updateMessageType(data) {
  return request({
    url: `${cloud.notice}/sysMessageType/edit`,
    method: 'post',
    data
  })
}

// 删除消息类型
export function delMessageType(typeId) {
  return request({
    url: `${cloud.notice}/sysMessageType/delete`,
    method: 'post',
    data: { typeId }
  })
}
// 获取渠道类型
export function listChannelType(query) {
  return request({
    url: `${cloud.notice}/sysMessageChannel/list/V2`,
    method: 'get',
    data: query
  })
}
