<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="70px"
          style="width: 100%;display: flex;align-items: center;justify-content: space-between;"
          @submit.native.prevent
        >
          <el-form-item label="试卷名称" prop="paperName">
            <el-input
              v-model.trim="queryParams.paperName"
              maxlength="30"
              placeholder="请输入"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <div class="fr">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">
              搜索
            </el-button>

            <el-button icon="el-icon-refresh" @click="handleReset">
              重置
            </el-button>
          </div>
        </el-form>
      </div>

      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <el-dropdown @command="handleAdd">
            <el-button type="primary" style="margin-right: 10px;">
              新增<i class="el-icon-arrow-down el-icon--right" />
            </el-button>

            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="1">
                随机题库
              </el-dropdown-item>

              <el-dropdown-item command="2">
                固定题库
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <div class="flex-1" />
        </div>

        <el-table
          ref="table"
          v-loading="loading"
          style="width: 100%;"
          border
          highlight-current-row
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            type="index"
            label="序号"
            width="70"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            label="试卷名称"
            show-overflow-tooltip
            align="center"
            prop="paperName"
            min-width="300"
          >
            <template slot-scope="scope">
              <span style="color: #409EFF;cursor: pointer;" @click="handleDetail(scope.row)">
                {{ scope.row.paperName }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="出题时间"
            show-overflow-tooltip
            align="center"
            prop="createTime"
            min-width="120"
          />

          <el-table-column
            label="单选题"
            align="center"
            prop="singleChoiceCount"
            min-width="60"
          />

          <el-table-column
            label="多选题"
            align="center"
            prop="multipleChoiceCount"
            min-width="60"
          />

          <el-table-column
            label="判断题"
            align="center"
            prop="judgeCount"
            min-width="60"
          />

          <el-table-column
            label="类型"
            show-overflow-tooltip
            align="center"
            prop="paperType"
            min-width="100"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.paperType == 1">
                随机题库
              </span>

              <span v-else-if="scope.row.paperType == 2">
                固定题库
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            min-width="110"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>

              <el-button
                type="text"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 随机题库弹框 -->
    <RandomPaperDialog ref="randomRef" @update="handleQuery" />

    <!-- 固定题库弹框 -->
    <FixedPaperDialog ref="fixedRef" @update="handleQuery" />

    <!-- 固定题库详情弹框 -->
    <FixedPaperDetailDialog ref="fixedDetailRef" />
  </div>
</template>

<script>
import {
  getTrainingExamMainPaperPage,
  trainingExamMainPaperEditCheck,
  trainingExamMainPaperDelete
} from '@/api/exam-manage/paper-manage'
import FixedPaperDialog from './components/fixed-paper-dialog.vue'
import RandomPaperDialog from './components/random-paper-dialog.vue'
import FixedPaperDetailDialog from './components/fixed-paper-detail-dialog.vue'

export default {
  name:'PaperManage',
  components:{
    FixedPaperDialog,
    RandomPaperDialog,
    FixedPaperDetailDialog
  },

  data() {
    return {
      // 表格
      loading:false,
      tableData:[],
      total:0,
      queryParams:{
        paperName:'',
        pageNo:1,
        pageSize:10
      }
    }
  },

  created() {
    this.handleQuery()
  },

  methods: {
    // 查询方法
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 重置方法
    handleReset() {
      this.queryParams = {
        paperName:'',
        pageNo:1,
        pageSize:10
      }
      this.getList()
    },

    // 查询列表
    getList() {
      const query = JSON.parse(JSON.stringify(this.queryParams))
      this.loading = true
      getTrainingExamMainPaperPage(query).then((data) => {
        this.loading = false
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      }).catch(() => {
        this.loading = false
      })
    },

    // 新增按钮操作
    handleAdd(val) {
      if (val == 1) {
        // 随机
        this.$refs.randomRef.init()
      } else if (val == 2) {
        // 固定
        this.$refs.fixedRef.init()
      }
    },

    // 编辑按钮操作
    handleEdit(row) {
      trainingExamMainPaperEditCheck({ id: row.id }).then((res) => {
        if (row.paperType == 1) {
          // 随机
          this.$refs.randomRef.init(JSON.parse(JSON.stringify(row)))
        } else if (row.paperType == 2) {
          // 固定
          this.$refs.fixedRef.init(JSON.parse(JSON.stringify(row)))
        }
      })
    },

    // 删除按钮操作
    handleDelete(row) {
      this.$dtModal
        .confirm(`是否确认删除试卷名称为"${row.paperName}"的数据项？`)
        .then(() => {
          row.ids = []
          row.ids.push(row.id)
          return trainingExamMainPaperDelete(row)
        })
        .then((res) => {
          if (res.success == true) {
            this.getList()
            this.$dtModal.msgSuccess('删除成功')
          }
        })
        .catch(() => {})
    },

    // 查看详情
    handleDetail(row) {
      if (row.paperType == 1) {
        // 随机
        this.$refs.randomRef.disabled = true
        this.$refs.randomRef.init(JSON.parse(JSON.stringify(row)))
      } else if (row.paperType == 2) {
        // 固定
        this.$refs.fixedDetailRef.init(JSON.parse(JSON.stringify(row)))
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dropdown-menu__item {
  padding: 0 16px;
}
</style>
