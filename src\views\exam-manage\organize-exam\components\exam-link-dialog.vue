<template>
  <el-dialog
    title="相关方考试链接"
    :visible.sync="dialogVisible"
    width="30%"
    :before-close="handleClose"
  >
    <div v-for="item in links" :key="item.id" class="exam_link">
      <el-divider>
        {{ item.label }}
      </el-divider>

      <div style="color:#409EFF">
        {{ item.url }}
      </div>

      <div class="btn">
        <el-button type="primary" size="small" @click="copyLink(item.url)">
          复 制 链 接
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      // 弹框
      dialogVisible: false,

      links:[
        {
          id:1,
          label:'web端考试链接',
          url:`${window.location.origin}/externalLogin?type=exam`
        },
        {
          id:2,
          label:'H5端考试链接',
          url:`${window.location.origin}/h5/#/pages/login/external-login`
        }
      ]
    }
  },

  methods: {
    // 初始化
    init() {
      this.dialogVisible = true
    },

    // 复制链接
    copyLink(val) {
      const url = val // 链接地址
      const inputNode = document.createElement('input') // 创建input
      inputNode.value = url // 赋值给 input 值
      document.body.appendChild(inputNode) // 插入进去
      inputNode.select() // 选择对象
      document.execCommand('Copy') // 原生调用执行浏览器复制命令
      inputNode.className = 'oInput'
      inputNode.style.display = 'none' // 隐藏
      this.$message.success('复制成功！')
    },

    // 关闭弹框
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.exam_link{
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40px;

    .btn{
        margin-top: 20px;
    }
}
</style>
