<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="70px"
          @submit.native.prevent
        >
          <el-form-item label="练习名称" prop="exerciseName">
            <el-input
              v-model.trim="queryParams.exerciseName"
              maxlength="30"
              clearable
              placeholder="请输入"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-form>

        <div class="flex-1" />

        <div class="fr">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            搜索
          </el-button>

          <el-button icon="el-icon-refresh" @click="handleReset">
            重置
          </el-button>
        </div>
      </div>

      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <div class="flex-1" />
        </div>

        <el-table
          ref="table"
          v-loading="loading"
          style="width: 100%;"
          border
          highlight-current-row
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            type="index"
            label="序号"
            width="70"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            label="练习名称"
            show-overflow-tooltip
            align="center"
            prop="exerciseName"
            min-width="300"
          />

          <el-table-column
            label="题目数量"
            show-overflow-tooltip
            align="center"
            prop="questionCount"
            min-width="100"
          />

          <el-table-column
            label="及格线"
            show-overflow-tooltip
            align="center"
            prop="passLine"
            min-width="100"
          />

          <el-table-column
            label="最后一次练习时间"
            show-overflow-tooltip
            align="center"
            prop="endTime"
            min-width="180"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.endTime == null">
                --
              </span>

              <span v-else>
                {{ scope.row.endTime }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="用时"
            show-overflow-tooltip
            align="center"
            prop="exerciseTime"
            min-width="160"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.exerciseTime == null">
                --
              </span>

              <span v-else>
                {{ getFormattedUseTime(scope.row.exerciseTime) }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="得分"
            show-overflow-tooltip
            align="center"
            prop="userScore"
            min-width="100"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.userScore == null">
                --
              </span>

              <span v-else>
                {{ scope.row.userScore }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="练习次数"
            show-overflow-tooltip
            align="center"
            prop="exerciseCount"
            min-width="100"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.exerciseCount == null">
                --
              </span>

              <span v-else>
                {{ scope.row.exerciseCount }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            min-width="200"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="goExam(scope.row)">
                {{ scope.row.isContinue > 0 ? '继续答题' : '开始答题' }}
              </el-button>

              <el-button v-if="scope.row.exerciseCount > 0" type="text" @click="showResult(scope.row)">
                查看结果
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 查看结果弹框 -->
    <PaperResultDialog ref="paperResultRef" />
  </div>
</template>

<script>
import { getFormattedUseTime } from '@/utils/paper'
import {
  getMyExerciseVOList,
  trainingExerciseCheck
} from '@/api/training-examination/my-practice.js'
import PaperResultDialog from '@/views/training-examination/components/paper-result-dialog.vue'

export default {
  name: 'MyPractice',
  components: { PaperResultDialog },

  data() {
    return {
      // 表格查询
      loading:true,
      tableData:[],
      total:0,
      queryParams:{
        exerciseName:'',
        pageNo:1,
        pageSize:10
      }
    }
  },


  created() {
    this.handleQuery()
  },

  methods: {
    getFormattedUseTime,

    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 重置
    handleReset() {
      this.queryParams = {
        exerciseName:'',
        pageNo:1,
        pageSize:10
      }
      this.getList()
    },

    // 获取列表数据
    getList() {
      this.loading = true
      getMyExerciseVOList(this.queryParams).then((data) => {
        this.loading = false
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      }).catch(() => {
        this.loading = false
      })
    },

    // 立即答题
    goExam(row) {
      trainingExerciseCheck({
        exerciseId:row.exerciseId
      }).then(() => {
        // 跳转答题练习页面
        this.$router.push({
          path: '/trainingExamination/takePractice',
          query: {
            id:row.exerciseId,
            exerciseName:encodeURIComponent(row.exerciseName)
          }
        })
      }).catch(() => {})
    },

    // 查看结果
    showResult(row) {
      this.$refs.paperResultRef.init(JSON.parse(JSON.stringify(row)))
    }
  }
}
</script>

