<template>
  <div :class="{fullscreen:fullscreen}" class="tinymce-container" :style="{width:containerWidth}">
    <div class="editor-custom-btn-container" v-if="isRefresh">
      <Editor :id="tinymceId" ref="inputValueRef" v-model="tinymceHtml" :disabled="inputDisabled" :init="editorInit" />
    </div>
  </div>
</template>

<script>
import editorImage from './components/EditorImage'
import plugins from './plugins'
import toolbar from './toolbar'
import load from './dynamicLoadScript'
import api from '@/framework/api/resource'
// import tinymce from 'tinymce/tinymce.js'
import tinymce from 'tinymce/tinymce'
import 'tinymce/themes/silver/theme'
import Editor from '@tinymce/tinymce-vue'
import emitter from 'element-ui/src/mixins/emitter'
import 'tinymce/icons/default'
import 'tinymce/themes/silver'
import 'tinymce/plugins/code'
import 'tinymce/plugins/print'
import 'tinymce/plugins/preview'
import 'tinymce/plugins/fullscreen'
import 'tinymce/plugins/paste'
import 'tinymce/plugins/searchreplace'
import 'tinymce/plugins/save'
import 'tinymce/plugins/autosave'
import 'tinymce/plugins/link'
import 'tinymce/plugins/autolink'
import 'tinymce/plugins/image'
import 'tinymce/plugins/imagetools'
import 'tinymce/plugins/media'
import 'tinymce/plugins/table'
import 'tinymce/plugins/codesample'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/advlist'
import 'tinymce/plugins/hr'
import 'tinymce/plugins/charmap'
import 'tinymce/plugins/emoticons'
import 'tinymce/plugins/anchor'
import 'tinymce/plugins/directionality'
import 'tinymce/plugins/pagebreak'
import 'tinymce/plugins/quickbars'
import 'tinymce/plugins/nonbreaking'
import 'tinymce/plugins/visualblocks'
import 'tinymce/plugins/visualchars'
import 'tinymce/plugins/wordcount'
import 'tinymce/plugins/emoticons/js/emojis'
import 'tinymce/skins/ui/oxide/skin.min.css'
import 'tinymce/skins/ui/oxide/content.min.css'
import 'tinymce/skins/content/default/content.min.css'
import './zh_CN.js'
export default {
  name: 'DtTinymce',
  components: { Editor },
  mixins: [emitter],
  props: {
    id: {
      type: String,
      default: function() {
        return 'vue-tinymce-' + +new Date() + ((Math.random() * 1000).toFixed(0) + '')
      }
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: String,
      default: ''
    },
    height: {
      type: [Number, String],
      required: false,
      default: 360
    },
    width: {
      type: [Number, String],
      required: false,
      default: 'auto'
    }
  },
  data() {
    return {
      hasChange: false,
      hasInit: false,
      tinymceId: this.id,
      tinymceHtml: '',
      isRefresh: true,
      fullscreen: false,
      languageTypeList: {
        'en': 'en',
        'zh': 'zh_CN',
        'es': 'es_MX',
        'ja': 'ja'
      }

    }
  },
  inject: {
    elForm: {
      default: ''
    },
    elFormItem: {
      default: ''
    }
  },
  computed: {
    editorInit() {
      return {
        branding: false,
        // language_url: '/Tinymce/zh_CN.js',
        language: this.languageTypeList['zh'],
        // skin_url: '/Tinymce/skins/ui/oxide',
        height: this.height,
        body_class: 'panel-body ',
        plugins: [
          'code',
          'print',
          'preview',
          'fullscreen',
          'paste',
          'searchreplace',
          'save',
          'autosave',
          'link',
          'autolink',
          'image',
          'imagetools',
          'media',
          'table',
          'codesample',
          'lists',
          'advlist',
          'hr',
          'charmap',
          'emoticons',
          'anchor',
          'directionality',
          'pagebreak',
          'quickbars',
          'nonbreaking',
          'visualblocks',
          'visualchars'
          // 'wordcount'
        ].join(' '),
        toolbar: [
          'fullscreen',
          'preview',
          'code',
          '|',
          'undo',
          'redo',
          '|',
          'forecolor',
          'backcolor',
          '|',
          'bold',
          'italic',
          'underline',
          'strikethrough',
          '|',
          'alignleft',
          'aligncenter',
          'alignright',
          'alignjustify',
          '|',
          'outdent',
          'indent',
          '|',
          'numlist',
          'bullist',
          '|',
          'formatselect',
          'fontselect',
          'fontsizeselect',
          '|',
          'link',
          'image',
          'media',
          'emoticons',
          'charmap',
          'anchor',
          'pagebreak',
          'codesample',
          '|',
          'ltr',
          'rtl'
        ].join(' '),
        draggable_modal: true,
        toolbar_mode: 'sliding',
        images_upload_handler: (blobInfo, success, failure) => {
          this.handleImgUpload(blobInfo, success, failure)
        },
        file_picker_types: 'media',
        file_picker_callback: (callback, value, meta) => {
          this.handleVideoUpload(callback, value, meta)
        }
      }
    },
    containerWidth() {
      const width = this.width
      if (/^[\d]+(\.[\d]+)?$/.test(width)) { // matches `100`, `'100'`
        return `${width}px`
      }
      return width
    },
    inputDisabled() {
      return this.disabled || (this.elForm || {}).disabled
    }
  },
  watch: {
    value: {
      handler: function(newVal, oldVa) {
        this.tinymceHtml = newVal
        this.dispatch('ElFormItem', 'el.form.change', newVal)
      },
      immediate: true,
      deep: true
    },
    tinymceHtml(val) {
      if (val !== null) {
        this.$emit('input', val)
      }
    },
    height(val) {
      if (val) {
        this.isRefresh = false
        this.$nextTick(() => {
          this.isRefresh = true
        })
      }
    }
  },
  mounted() {
    tinymce.init({ branding: false })
  },
  methods: {
    handleImgUpload(blobInfo, success, failure) {
      // success('data:image/jpeg;base64,' + blobInfo.base64())
      // 改为自定义上传
      let self = this
      let file = blobInfo.blob()
      const isLt2M = file.size / 1024 < 5000
      if (!isLt2M) {
        this.$message.error('上传失败，图片不可超过5MB')
        return false
      }

      const formData = new FormData()
      formData.append('file', blobInfo.blob())
      formData.append('secretFlag', 'N')

      api.fileUpload(formData).then(({ data }) => {
        success(data.fileUrl)
      })
    },
    handleVideoUpload(callback, value, meta) {
      if (meta.filetype === 'media') {
        // 模拟上传本地视频
        const input = document.createElement('input')
        input.setAttribute('type', 'file')
        input.setAttribute('accept', '.mp4')
        input.onchange = function() {
          const file = this.files[0]
          const fd = new FormData()
          fd.append('file', file)
          fd.append('secretFlag', 'N')
          api.fileUpload(fd).then(({ data }) => {
            callback(data.fileUrl)
          })
        }
        input.click()
      }
    }
  }
}
</script>
<style>
body .tox-tinymce-aux {
  z-index: 19892000;
}
body{
  margin: 0;
}
</style>
<style lang="scss" scoped>
.tinymce-container {
  position: relative;
  line-height: normal;
    // height: 360px;
}

.tinymce-container {
  ::v-deep {
    .mce-fullscreen {
      z-index: 10000;
    }
  }
}

.tinymce-textarea {
  visibility: hidden;
  z-index: -1;
}

.editor-custom-btn-container {
  // position: absolute;
  // right: 4px;
  top: 4px;
  /*z-index: 2005;*/

}

.fullscreen .editor-custom-btn-container {
  z-index: 10000;
  position: fixed;
}

.editor-upload-btn {
  display: inline-block;
}
.v-modal{
  z-index: 0;
}
</style>
