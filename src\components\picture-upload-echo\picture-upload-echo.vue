<!--
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-16 15:54:36
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-05-27 14:20:45
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
-->
<script>
// add 根据fileId回显
import PictureUpload from '../picture-upload/index.vue'
import { getFileDetail } from '@/api/common/index'

export default {
  extends: PictureUpload,
  props: {
    /**
     * 文件id
     */
    fileId: {
      type: String,
      default: ''
    }
  },

  watch: {
    fileId: {
      handler: function(newVal, oldVal) {
        if(newVal) {
          getFileDetail(newVal).then(res => {
            this.fileList = [{
              id: res.data.id,
              name: res.data.fileOriginName,
              url: res.data.fileLink
            }]
          })
        } else {
          this.fileList = []
        }
      },

      immediate: true
    },

    fileList(val, oldVal) {
      if(val.length == 0 && oldVal.length == 0) return
      
      let _fileId = ''

      if(val && val.length > 0) {
        _fileId = val.map(v => v.id).join(',')
      } else {
        _fileId = ''
      }

      this.$emit('update:fileId', _fileId)
      this.dispatch('ElFormItem', 'el.form.change', _fileId)
    }
  },

  methods: {
    changeFileList(fileList) {
      const tempFileList = fileList.map(item => {
        const tempItem = {
          name: item.name,
          url: item.response ? item.response.data.fileLink : item.fileLink,
          id: item.response ? item.response.data.id: item.id
        }
        return tempItem
      })

      this.fileList = tempFileList
    }
  }
}
</script>