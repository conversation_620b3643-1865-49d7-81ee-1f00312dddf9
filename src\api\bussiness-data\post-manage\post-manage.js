import request from '@/framework/utils/request'

// 查询岗位管理列表
export function bizPostManagePage(query) {
  return request({
    url: '/project/bizPostManage/page',
    method: 'get',
    params: query
  })
}

// 查询岗位管理详细
export function bizPostManageDetail(params) {
  return request({
    url: '/project/bizPostManage/detail',
    method: 'get',
    params
  })
}

// 新增岗位管理
export function bizPostManageAdd(data) {
  return request({
    url: '/project/bizPostManage/add',
    method: 'post',
    data: data
  })
}

// 修改岗位管理
export function bizPostManageEdit(data) {
  return request({
    url: '/project/bizPostManage/edit',
    method: 'post',
    data: data
  })
}

// 删除岗位管理
export function bizPostManageDel(postId) {
  return request({
    url: '/project/bizPostManage/delete',
    method: 'post',
    data: { postIds: postId }
  })
}

// 分页查询业务-风险告知与警示信息
export function bizRiskExternalPage(query) {
  return request({
    url: '/project/bizRiskExternal/page',
    method: 'get',
    params: query
  })
}

// 岗位关联设备活动后的回显
export function GetbizRiskExternal(data) {
  return request({
    url: '/project/bizRiskExternal/dataEcho',
    method: 'post',
    data: data
  })
}

// 查询可能导致的事故类型列表
export function bizPossibleAccidentType(params) {
  return request({
    url: '/project/bizPossibleAccidentType/page',
    method: 'get',
    params
  })
}


