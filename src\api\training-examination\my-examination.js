/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-08-14 17:14:54
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-08-15 11:04:56
 * @Description: 我的考试
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud}  from '@/framework/utils/request'
// 分页查询培训课程信息
export function getMyExamListPage(query) {
  return request({
    url: cloud.dqbasic + '/trainingExamPlan/getMyExamListPage',
    method: 'get',
    params: query
  })
}
// 根据考试计划id获取试卷信息
export function getExamPaperByExamPlanId(query) {
  return request({
    url: cloud.dqbasic + '/trainingExamPaper/getExamPaperByExamPlanId',
    method: 'get',
    params: query
  })
}
// 根据考试计划id获取试卷信息
export function commitPaperAndScore(data) {
  return request({
    url: cloud.dqbasic + '/trainingExamUser/commitPaperAndScore',
    method: 'post',
    data: data
  })
}