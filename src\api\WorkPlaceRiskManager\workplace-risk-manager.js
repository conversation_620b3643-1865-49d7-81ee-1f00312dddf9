import request from "@/framework/utils/request"

// 查询作业场所风险列表
export function queryWorkLocationRiskList(params) {
  return request({
    url: "/project/bizRiskWorkLocation/page",
    method: "get",
    params
  })
}

// 新增作业场所风险
export function addWorkLocationRisk(data) {
  return request({
    url: "/project/bizRiskWorkLocation/add",
    method: "post",
    data
  })
}

// 编辑作业场所风险
export function editWorkLocationRisk(data) {
  return request({
    url: "/project/bizRiskWorkLocation/edit",
    method: "post",
    data
  })
}

// 删除作业场所风险
export function deleteWorkLocationRisk(data) {
  return request({
    url: "/project/bizRiskWorkLocation/delete",
    method: "post",
    data
  })
}

// 查询作业场所风险详情
export function queryWorkLocationRiskDetailById(params) {
  return request({
    url: "/project/bizRiskWorkLocation/detail",
    method: "get",
    params
  })
}
