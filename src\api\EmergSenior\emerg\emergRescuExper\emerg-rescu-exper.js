/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-23 10:24:41
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-05-23 10:25:07
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'

// 新增专家
export function listEmergRescuExper(query) {
  return request({
    url: '/project/emergRescuExper/getListPage',
    method: 'get',
    params: query
  })
}

// 查询专家详细
export function getEmergRescuExper(id) {
  return request({
    url: '/project/emergRescuExper/detail?id=' + id,
    method: 'get'
  })
}

// 新增专家
export function addEmergRescuExper(data) {
  return request({
    url: '/project/emergRescuExper/add',
    method: 'post',
    data: data
  })
}

// 修改专家
export function updateEmergRescuExper(data) {
  return request({
    url: '/project/emergRescuExper/edit',
    method: 'post',
    data: data
  })
}

// 删除专家
export function delemEmergRescuExper(id) {
  return request({  
    url: '/project/emergRescuExper/delete',
    method: 'post',
    data: { ids: id }
  })
}
