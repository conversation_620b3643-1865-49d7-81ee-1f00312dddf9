export default class QuestionPickerButtonComponent {
  constructor({ pauseOnAddQuestion = true, onPickQuestion }) {
    console.log('初始化 QuestionPickerButtonComponent')
    this.pauseOnAddQuestion = pauseOnAddQuestion
    this.onPickQuestion = onPickQuestion
    this.player = null
  }

  createEl(el) {
    const button = document.createElement('div')
    const controlBar = el.querySelector('.prism-controlbar')
    console.log(controlBar.children)
    button.classList.add('prism-add-question')
    button.style.cssText = `
      vertical-align: middle;
      float: right;
      height: 28px;
      margin-top: 10px;
      margin-right: 10px;
      line-height: 28px;
      padding: 0 10px;
    `
    button.textContent = '添加试题'
    button.addEventListener('click', this.handleButtonClick.bind(this))
    controlBar.append(button)
  }

  handleButtonClick(e) {
    if (!this.onPickQuestion) {
      console.warn('没有添加回调函数')
      return
    }

    if (this.player.fullscreenService.getIsFullScreen()) {
      this.player.fullscreenService.cancelFullScreen()
    }

    if (this.pauseOnAddQuestion) {
      this.player.pause()
    }

    this.onPickQuestion(this.player.getCurrentTime())
  }


  ready(player, e) {
    this.player = player
  }
}
