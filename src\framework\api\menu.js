import { default as request, cloud } from '@/framework/utils/request'

const getSysMenuList = (params) => request({ url: `${cloud.permission}/sysAcMenu/list`, method: 'get', params })
const getLazyMenuList = (params) => request({ url: `${cloud.manage}/sysMenu/listByParentId`, method: 'get', params })
const getMenuList = (params) => request({ url: `${cloud.manage}/sysMenu/menuList`, method: 'get', params }) // 代码生成查询菜单
const getSysMenuTree = (params) => request({ url: `${cloud.permission}/sysAcMenu/tree`, method: 'get', params })
const addSysMenu = (params) => request({ url: `${cloud.permission}/sysAcMenu/add`, method: 'post', data: params })
const deleteSysMenu = (params) => request({ url: `${cloud.permission}/sysAcMenu/delete`, method: 'post', data: params })
const updateStatusSysMenu = (params) => request({ url: `${cloud.permission}/sysAcMenu/updateStatus`, method: 'post', data: params })
const getSysMenuDetail = (params) => request({ url: `${cloud.permission}/sysAcMenu/detail`, method: 'get', params })
const editSysMenu = (params) => request({ url: `${cloud.permission}/sysAcMenu/edit`, method: 'post', data: params })
const getLeftMenu = (params) => request({ url: `${cloud.permission}/sysAcInfo/getFunctionalInfo`, method: 'get', params })
const getAppList = async (params) => request({ url: `${cloud.dqbasic}/sysMenu/getTopAppList`, method: 'get', params })
const getSysAppList = (params) => request({ url: `${cloud.manage}/sysApp/platformlist`, method: 'get', params })
const getSysMenuButton = (params) => request({ url: `${cloud.permission}/sysAcButton/pageList`, method: 'get', params })
const addSysMenuButton = (params) => request({ url: `${cloud.permission}/sysAcButton/add`, method: 'post', data: params })
const deleteSysMenuButton = (params) => request({ url: `${cloud.permission}/sysAcButton/delete`, method: 'post', data: params })
const editSysMenuButton = (params) => request({ url: `${cloud.permission}/sysAcButton/edit`, method: 'post', data: params })
const addSysDefaultMenuButton = (params) => request({ url: `${cloud.permission}/sysAcButton/addSystemDefaultButton`, method: 'post', data: params })
const batchDeleteMenuButton = (params) => request({ url: `${cloud.permission}/sysAcButton/batchDelete`, method: 'post', data: params })
const getServesMenu = (params) => request({ url: `${cloud.permission}/sysAcInfo/getPortalApp`, method: 'get', params }) // 门户 应用card
const getFindShortCutsMenu = (params) => request({ url: `${cloud.permission}/sysAcInfo/findShortCutsMenu`, method: 'get', params }) // 门户常用菜单

export {
  getSysMenuList,
  getMenuList,
  getSysMenuTree,
  addSysMenu,
  deleteSysMenu,
  updateStatusSysMenu,
  getSysMenuDetail,
  editSysMenu,
  getLeftMenu,
  getAppList,
  getSysAppList,
  getSysMenuButton,
  addSysMenuButton,
  deleteSysMenuButton,
  editSysMenuButton,
  addSysDefaultMenuButton,
  batchDeleteMenuButton,
  getServesMenu,
  getFindShortCutsMenu,
  getLazyMenuList
}
