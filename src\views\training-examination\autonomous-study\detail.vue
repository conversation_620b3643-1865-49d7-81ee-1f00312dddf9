<template>
  <div v-loading="isTrainingLoading" class="attend-training">
    <div class="title-box">
      <div class="title-left" />
      <div class="title">
        {{ trainingName }}
      </div>

      <div class="title-actions">
        <el-button type="" @click="$router.go(-1)"> 返回 </el-button>
      </div>
    </div>

    <div class="training-content">
      <div v-loading="isContentLoading" class="training-content-right">
        <AliyunPlayer
          ref="playerRef"
          :is-forward-seek-allowed="false"
          :source="currentPlaybackUrl"
          :max-playback-time.sync="videoMaxPlaybackTime"
          :config="playerConfig"
          @player-ready="handlePlayerReady"
          @player-request-full-screen="handlePlayerRequestFullScreen"
          @player-cancel-full-screen="handlePlayerCancelFullScreen"
        />

        <QuestionItem v-if="showQuestion" :question="currentQuestion" />
      </div>
    </div>
  </div>
</template>

<script>
import { getTrainingVideoDetail } from "@/api/training-examination/training-course";
import { getPlayInfo } from "@/api/aliyun-vod";
import { getFileDetail } from "@/api/common";
import AliyunPlayer from "@/components/aliyun-player";
import QuestionItem from "@/components/question-item";

export default {
  components: { AliyunPlayer, QuestionItem },

  data() {
    return {
      isTrainingLoading: false,
      isCourseLoading: false,
      isPlaybackUrlLoading: false,
      isFileUrlLoading: false,
      trainingInfo: {
        trainName: "",
      },

      playerConfig: {
        height: "600px",
      },

      currentCourseId: "",
      currentCourse: {},
      currentPlaybackUrl: "",
      currentPreviewUrl: "",
      currentTime: "",
      videoInfo: {},
      videoMaxPlaybackTime: 0,
      isPlayerFullScreen: false,
      currentQuestionIndex: 0,

      fileInfo: {},
    };
  },

  computed: {
    showPlayer() {
      return (
        this.currentCourse.resourceType === "0" &&
        this.currentCourse.videoPath &&
        this.currentPlaybackUrl
      );
    },

    showQuestion() {
      return this.currentTime === 0;
    },

    questionList() {
      return this.currentCourse ? this.currentCourse.questionDTOList : [];
    },

    currentQuestion() {
      return this.questionList[this.currentQuestionIndex];
    },

    trainingName() {
      return this.trainingInfo.trainName || "未知";
    },

    isContentLoading() {
      return (
        this.isCourseLoading ||
        this.isFileUrlLoading ||
        this.isPlaybackUrlLoading
      );
    },
  },

  mounted() {
    this.getCourseDetail(this.$route.query.id);
  },

  methods: {
    getVideoPlaybackUrl(id) {
      const requestId = id;
      this.isPlaybackUrlLoading = true;
      getPlayInfo(id)
        .then((res) => {
          if (requestId !== this.currentCourse.videoPath) return;
          this.videoInfo = res.data.body;
          this.currentPlaybackUrl = this.videoInfo.playInfoList.playInfo[0].playURL;
        })
        .catch((err) => {
          console.log(err);
          this.$message.error("获取视频播放信息失败");
        })
        .finally(() => {
          this.isPlaybackUrlLoading = false;
        });
    },

    getCourseFileUrl(course) {
      return this.getVideoPlaybackUrl(course.videoPath);
    },

    getCourseDetail(id) {
      const requestId = id;
      this.isCourseLoading = true;
      getTrainingVideoDetail({ id })
        .then((res) => {
          // if (this.currentCourseId !== requestId) return;
          this.currentCourse = res.data;
          return this.getCourseFileUrl(res.data);
        })
        .catch((err) => {
          this.$message.error("获取课程信息失败");
          console.log(err);
        })
        .finally(() => {
          // if (this.currentCourseId !== requestId) return;
          this.isCourseLoading = false;
        });
    },

    handleBack() {
      this.$emit("back", false);
    },

    handlePlayerReady({ player, args }) {
      console.log("course player ready", player, args);
    },

    handlePlayerRequestFullScreen({ player, args }) {
      console.log("player enter full screen");
    },

    handlePlayerCancelFullScreen({ player, args }) {
      console.log("player exit full screen");
    },
  },
};
</script>

<style lang="scss" scoped>
.attend-training {
  background-color: #fff;
  padding: 20px;
  height: calc(100vh - 00px);
  display: flex;
  flex-direction: column;
  font-size: 14px;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;

    .title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .title-actions {
      .el-button {
        margin-left: 10px;
      }
    }
  }

  .training-content {
    flex: 1;
    display: flex;
    gap: 20px;
    min-height: 0;
    --border-color: #ebeef5;

    .course-list {
      margin: 10px 0 0;
      padding: 0;
      height: calc(100% - 10px - 36px);
      overflow: auto;
      border-radius: 4px;
      list-style: none;
    }

    .course-list-item {
      padding: 10px;
      cursor: pointer;
      border-left: 1px solid var(--border-color);
      // border-right: 1px solid var(--border-color);
      border-bottom: 1px solid var(--border-color);

      &:first-child {
        border-top: 1px solid var(--border-color);
      }

      &:hover {
        background-color: #f5f7fa;
      }

      &--current {
        background-color: var(
          --primary-background-hover,
          rgba(11, 204, 39, 0.1)
        );
      }
    }
    .training-content-left {
      width: 240px;
      border-right: 1px solid #ebeef5;
    }

    .training-content-right {
      flex: 1;
      position: relative;

      .video-container {
        .video-player {
          height: 400px;
          background: #f5f7fa;
          margin-bottom: 20px;
        }

        .video-info {
          .video-rules {
            background: #fdf6ec;
            padding: 15px;
            border-radius: 4px;

            .rules-title {
              color: #e6a23c;
              font-weight: 500;
              margin-bottom: 10px;
            }

            .rules-content {
              color: #666;
              line-height: 1.8;

              .sub-rule {
                padding-left: 20px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
