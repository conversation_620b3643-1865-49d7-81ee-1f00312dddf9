import request, {cloud} from '@/framework/utils/request'

// 查询仓库管理列表
export function listWarehouseManagement(query) {
  return request({
    url: cloud.dqbasic +'/maWarehouseManagement/page',
    method: 'get',
    params: query
  })
}

// 查询仓库管理详细
export function getWarehouseManagement(id) {
  return request({
    url: cloud.dqbasic +'/maWarehouseManagement/detail?id=' + id,
    method: 'get'
  })
}

// 新增仓库管理
export function addWarehouseManagement(data) {
  return request({
    url: cloud.dqbasic +'/maWarehouseManagement/add',
    method: 'post',
    data: data
  })
}

// 修改仓库管理
export function updateWarehouseManagement(data) {
  return request({
    url: cloud.dqbasic +'/maWarehouseManagement/edit',
    method: 'post',
    data: data
  })
}

// 删除仓库管理
export function delWarehouseManagement(data) {
  return request({
    url: cloud.dqbasic +'/maWarehouseManagement/delete',
    method: 'post',
    data: data
  })
}
