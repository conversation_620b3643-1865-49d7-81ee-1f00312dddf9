import { default as request, cloud } from '@/framework/utils/request'

const getLogList = (params) => request({ url: `${cloud.dqbasic}/logManager/page`, method: 'get', params })
const getLogDetail = (params) => request({ url: `${cloud.dqbasic}/logManager/detail`, method: 'get', params })
const getLogLoginList = (params) => request({ url: `${cloud.dqbasic}/loginLog/page`, method: 'get', params })
const deleteLog = (params) => request({ url: `${cloud.dqbasic}/logManager/delete`, method: 'post', data: params })
const deleteAllLoginLog = () => request({ url: `${cloud.dqbasic}/loginLog/deleteAll`, method: 'post' })
export {
  getLogList,
  getLogDetail,
  getLogLoginList,
  deleteLog,
  deleteAllLoginLog
}
