<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="60%"
      :before-close="handleClose"
      class="custom_dialog paper"
      top="5vh"
    >
      <div class="total">
        {{ row.exerciseId ? '练习' : '考试' }}名称：{{ row.exerciseId ? row.exerciseName : row.examName }}
      </div>

      <el-table
        v-loading="loading"
        style="width: 100%;"
        border
        highlight-current-row
        :header-cell-style="{ backgroundColor: '#f2f2f2' }"
        :data="tableData"
      >
        <template slot="empty">
          <p>{{ $store.getters.dataText }}</p>
        </template>

        <el-table-column
          label="开始时间"
          show-overflow-tooltip
          align="center"
          prop="startTime"
        />

        <el-table-column
          label="结束时间"
          show-overflow-tooltip
          align="center"
          prop="endTime"
        />

        <el-table-column
          v-if="row.exerciseId"
          label="用时"
          show-overflow-tooltip
          align="center"
          prop="exerciseTime"
        >
          <template slot-scope="scope">
            <span v-if="!scope.row.exerciseTime">
              --
            </span>

            <span v-else>
              {{ getFormattedUseTime(scope.row.exerciseTime) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          v-if="row.examPlanId"
          label="用时"
          show-overflow-tooltip
          align="center"
          prop="examTime"
        >
          <template slot-scope="scope">
            <span v-if="!scope.row.examTime">
              --
            </span>

            <span v-else>
              {{ getFormattedUseTime(scope.row.examTime) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="分数"
          align="center"
          prop="userScore"
        />

        <el-table-column
          label="操作"
          align="center"
          width="140px"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="showAnswerDetail(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <dt-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNo"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-dialog>

    <!-- 答题详情弹框 -->
    <PaperAnswerDetailDialog ref="paperAnswerDetailRef" />
  </div>
</template>

<script>
import { getFormattedUseTime } from '@/utils/paper'
import {
  getTrainingExercisePaperPage
} from '@/api/training-examination/my-practice'
import {
  getTrainingExamUserPaperPage
} from '@/api/training-examination/my-exam'
import PaperAnswerDetailDialog from '@/views/training-examination/components/paper-answer-detail-dialog.vue'

export default {
  components: { PaperAnswerDetailDialog },
  data() {
    return {
      // 弹框
      dialogVisible: false,

      // 标题
      title:'',

      row:{},

      // 表格
      loading: false,
      tableData: [],
      total: 0,
      queryParams:{
        pageNo:1,
        pageSize:10
      }
    }
  },

  methods: {
    getFormattedUseTime,

    // 初始化
    init(row) {
      this.row = row
      if (row.exerciseId) {
        this.title = '练习结果'
        this.queryParams.exerciseId = row.exerciseId
      }
      if (row.examPlanId) {
        this.title = '考试结果'
        this.queryParams.examPlanId = row.examPlanId
      }
      this.handleQuery()
      this.dialogVisible = true
    },

    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 获取列表
    async getList() {
      const query = JSON.parse(JSON.stringify(this.queryParams))
      this.loading = true
      let res
      if (this.row.exerciseId) {
        res = await getTrainingExercisePaperPage(query)
      } else {
        res = await getTrainingExamUserPaperPage(query)
      }
      this.loading = false
      if (!res) return
      this.tableData = res.data.rows
      this.total = res.data.totalRows
    },

    // 查看答题详情
    showAnswerDetail(row) {
      this.$refs.paperAnswerDetailRef.init(JSON.parse(JSON.stringify(row)))
    },

    // 关闭弹框
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.custom_dialog {
  ::v-deep .el-dialog__header {
    padding: 16px 24px !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ebeef5;
  }

  ::v-deep .el-dialog__header .el-dialog__headerbtn {
    position: relative;
    top: unset;
    right: unset;
  }

  &.paper{
    ::v-deep .el-dialog__body{
        padding: 20px !important;
    }
  }
}

.total{
    margin-bottom: 10px;
    font-size: 14px;
    color: #333;
    font-weight: 700;
}
</style>
