/*
 * @Author: miteng <EMAIL>
 * @Date: 2024-05-15 16:13:32
 * @LastEditors: 高宇 <EMAIL>
 * @LastEditTime: 2024-05-21 15:26:10
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'

// 查询管控主体列表
export function bizControlSubjectList(data) {
  return request({
    url: '/project/bizControlSubject/selectList',
    method: 'POST',
    data
  })
}


// 修改管控主体数据
export function bizControlSubjectEdit(data) {
  return request({
    url: '/project/bizControlSubject/edit',
    method: 'post',
    data: data
  })
}
