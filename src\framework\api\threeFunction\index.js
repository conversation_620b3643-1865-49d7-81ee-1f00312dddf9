import { default as request, cloud } from '@/framework/utils/request'

// table列表
export function getAppServiceList(query) {
  return request({
    url: `${cloud.permission}/third/getSubFunctionList`,
    method: 'get',
    params: query
  })
}

// 查询三方应用列表
export function getAppList(query) {
  return request({
    url: `${cloud.permission}/third/getAppList`,
    method: 'get',
    params: query
  })
}

// 新增
export function addFunction(query) {
  return request({
    url: `${cloud.permission}/third/addFunction`,
    method: 'post',
    data: query
  })
}
// 编辑
export function editFunction(query) {
  return request({
    url: `${cloud.permission}/third/editFunction`,
    method: 'post',
    data: query
  })
}

// 删除
export function removeFunction(query) {
  return request({
    url: `${cloud.permission}/third/removeFunction`,
    method: 'post',
    data: query
  })
}

// 导入
export function importFunction(query) {
  return request({
    url: `${cloud.permission}/third/importFunction`,
    method: 'post',
    data: query,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导入下载模板
export function templateFile(data) {
  return request({
    url: `${cloud.permission}/third/downloadTemplate`,
    method: 'post',
    data,
    responseType: 'arraybuffer'
  })
}

// 导出
export function exportExcel(data) {
  return request({
    url: `${cloud.permission}/third/exportExcel`,
    method: 'post',
    data,
    responseType: 'arraybuffer'
  })
}

// 启用、禁用
export function updateStatus(data) {
  return request({
    url: `${cloud.permission}/third/updateStatus`,
    method: 'post',
    data
  })
}

// 父级
export function getSubFunctionTree(query) {
  return request({
    url: `${cloud.permission}/third/getSubFunctionTree`,
    method: 'get',
    params: query
  })
}

