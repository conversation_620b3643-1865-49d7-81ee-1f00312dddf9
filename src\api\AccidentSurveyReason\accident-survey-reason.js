/*
 * @Author: duhong<PERSON> <EMAIL>
 * @Date: 2024-04-22 17:04:45
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-05-09 16:43:07
 * @FilePath: \isrmp_vue\src\api\AccidentSurveyReason\accident-survey-reason.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import { default as request, cloud } from '@/framework/utils/request'

// 查询业务-事故调查事故原因列表
export function listAccidentSurveyReason(query) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyReason/list',
    method: 'get',
    params: query
  })
}

// 查询业务-事故调查事故原因详细
export function getAccidentSurveyReason(id) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyReason/detail?id=' + id,
    method: 'get'
  })
}

// 新增业务-事故调查事故原因
export function addAccidentSurveyReason(data) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyReason/add',
    method: 'post',
    data: data
  })
}

// 修改业务-事故调查事故原因
export function updateAccidentSurveyReason(data) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyReason/edit',
    method: 'post',
    data: data
  })
}

// 删除业务-事故调查事故原因
export function delAccidentSurveyReason(id) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyReason/delete',
    method: 'post',
    data: { ids: id }
  })
}
