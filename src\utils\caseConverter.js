/**
 * 将驼峰命名转换为连字符命名
 * 例如: 'fooBar' → 'foo-bar'
 *
 * @param {string} str - 要转换的驼峰命名字符串
 * @returns {string} 转换后的连字符命名字符串
 */
export function camelToKebab(str) {
  if (typeof str !== 'string') {
    throw new TypeError('Expected a string')
  }

  return str.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()
}

/**
 * 将连字符命名转换为驼峰命名
 * 例如: 'foo-bar' → 'fooBar'
 *
 * @param {string} str - 要转换的连字符命名字符串
 * @returns {string} 转换后的驼峰命名字符串
 */
export function kebabToCamel(str) {
  if (typeof str !== 'string') {
    throw new TypeError('Expected a string')
  }

  return str.replace(/-([a-z])/g, (_, char) => char.toUpperCase())
}
