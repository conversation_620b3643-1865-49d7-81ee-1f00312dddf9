import { CONSTANTS } from '@/constants/question'
function numToLetter(index) {
  return String.fromCharCode('A'.charCodeAt(0) + index)
}

const trueOrFalseOptions = [
  { label: '正确', value: '0', option: true },
  { label: '错误', value: '1', option: false }
]


function getOptions(question) {
  return Object.keys(question)
    .filter((key) => key.startsWith('option'))
    .map((key, index) => ({
      label: question[key],
      value: `${index}`,
      option: numToLetter(index)
    }))
    .filter((option) => !!option.label)
}

export function adapt(question) {
  const remark = question.remark
  let options = []
  let answer

  switch (question.quesType) {
    case CONSTANTS.QUESTION_TYPE_MULTIPLE_CHOICE:
      answer = question.multiRight.split(',')
      options = getOptions(question)
      break
    case CONSTANTS.QUESTION_TYPE_SINGLE_CHOICE: {
      answer = question.monoRight
      options = getOptions(question)
      break
    }
    case CONSTANTS.QUESTION_TYPE_TRUE_OR_FALSE: {
      answer = question.judgeRight
      options = trueOrFalseOptions
      break
    }
    // case CONSTANTS.QUESTION_TYPE_FILL_IN_THE_BLANK:
    // case CONSTANTS.QUESTION_TYPE_SHORT_ANSWER:
  }

  return {
    id: question.questionId,
    title: question.content,
    options,
    type: question.quesType,
    answer,
    remark
  }
}
