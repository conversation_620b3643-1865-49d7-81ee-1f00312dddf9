<!--
  * @Author: jiadongjin <EMAIL>
  * @Date: 2024-08-20 09:47:57
  * @LastEditors: gaoyu <EMAIL>
  * @LastEditTime: 2025-05-06 11:00:13
  * @Description:
  * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
  * @FilePath: \shared_warehouse_web\src\components\ue-single-instance\index.vue
-->
<template>
  <div />
</template>

<script>
import { appLoad, apiRegister, apiSend } from '@/components/ue-player'
import { EventBus } from '@/utils/event-bus'
export default {
  name: 'UESingleInstance',
  props: {
    /**
     ** @type {Object}
     ** @description 发送参数
     */
    params: {
      type: Object,
      default: null
    }
  },

  data() {
    this.UEContainter = null

    return {}
  },

  mounted() {
    this._fullScreenChangeHandler = () => {
      this.fullScreenFlag = !!document.fullscreenElement
    }
    // 注册全屏事件监听
    this.initFullScreenEvent()

    this.init()
    this.showOrHide()
  },

  destroyed() {
    // 注销全屏事件监听
    this.destroyFullScreenEvent()

    this.showOrHide()
  },

  beforeDestroy() {
    this.UEContainter && this.UEContainter.$off('loaded', null)
    this.UEContainter && this.UEContainter.$off('getMessige', null)
  },

  methods: {
    initFullScreenEvent() {
      document.onwebkitfullscreenchange = this._fullScreenChangeHandler
      document.onfullscreenchange = this._fullScreenChangeHandler
    },

    destroyFullScreenEvent() {
      document.onwebkitfullscreenchange = null
      document.onfullscreenchange = null
    },

    // 找到UE容器
    init() {
      if (this.$parent._inactive) return

      if (this.UEContainter) {
        this.UEContainter.$off('loaded', null)
      }

      this.UEContainter = this.findComponentDownward(this.$root, 'UEContainer')
      this.UEContainter.$on('getMessige', (data) => {
        // 获取到虚幻消息后传递给子组件
        this.$emit('getMessige', data)
      })
      /*
       * if(this.UEContainter) {
       *   // 已经加载完成 直接发送消息
       *   if(this.UEContainter.loaded) {
       *     // 浏览器向虚幻发送消息
       *     if(this.params) {
       *       this.UEContainter.sendToUE(this.params)
       *     }
       *   }
       *   // 等待加载完成回调
       *   else {
       *     this.UEContainter.$on('loaded', () => {
       *       // 浏览器向虚幻发送消息
       *       if(this.params) {
       *         this.UEContainter.sendToUE(this.params)
       *       }
       *     })
       *   }
       * }
       */
    },

    sendToUe(val) {
      this.UEContainter.sendToUE(val)
    },

    // 显示/隐藏UE容器
    showOrHide() {
      const arr = this.findComponentsDownward(this.$root, 'UESingleInstance')
      // 找到视频容器
      const element = document.getElementById('playerUI')
      if (arr.length == 0 || arr.every((v) => v.$parent._inactive)) {
        element.style.visibility = 'hidden'
      } else {
        element.style.visibility = 'visible'
      }
    },

    // 由一个组件，向下找到最近的指定组件
    findComponentDownward(context, componentName) {
      const childrens = context.$children
      let children = null

      if (childrens.length) {
        for (const child of childrens) {
          const name = child.$options.name

          if (name === componentName) {
            children = child
            break
          } else {
            children = this.findComponentDownward(child, componentName)
            if (children) break
          }
        }
      }

      return children
    },


    // 由一个组件，向下找到所有指定的组件
    findComponentsDownward(context, componentName) {
      return context.$children.reduce((components, child) => {
        if (child.$options.name === componentName) components.push(child)
        const foundChilds = this.findComponentsDownward(child, componentName)
        return components.concat(foundChilds)
      }, [])
    },

    // 全屏
    handleFullScreen() {
      // 找到视频容器
      const element = document.getElementById('playerUI')

      // 非全屏状态 请求全屏
      if (!this.fullScreenFlag) {
        /*
         * 调用全屏方法
         * HTML W3C 提议
         */
        if (element.requestFullScreen) {
          element.requestFullScreen()
        } else if (element.msRequestFullscreen) {// IE11
          element.msRequestFullscreen()
        } else if (element.webkitRequestFullScreen) { // Webkit (works in Safari5.1 and Chrome 15)
          element.webkitRequestFullScreen()
        } else if (element.mozRequestFullScreen) {// Firefox (works in nightly)
          element.mozRequestFullScreen()
        }
        return
      }
      /*
       * 全屏状态 退出全屏
       * HTML5 W3C 提议
       */
      if (element.requestFullScreen) {
        document.exitFullscreen()
      } else if (element.msRequestFullscreen) {// IE 11
        document.msExitFullscreen()
      } else if (element.webkitRequestFullScreen) { // Webkit (works in Safari5.1 and Chrome 15)
        document.webkitCancelFullScreen()
      } else if (element.mozRequestFullScreen) {// Firefox (works in nightly)
        document.mozCancelFullScreen()
      }
    }
  }
}
</script>
