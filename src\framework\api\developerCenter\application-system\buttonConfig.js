import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 查询按钮列表
  getLowCodeButtonList(params) {
    return request({
      url: `${cloud.devcenter}/sysAcButton/getLowCodeButtonList/${params.formId}`,
      method: 'get',
      params
    })
  },
  // 按钮显示设置
  setLowCodeButtonStatus(data) {
    return request({
      url: `${cloud.devcenter}/sysAcButton/setLowCodeButtonStatus`,
      method: 'POST',
      data
    })
  },
  // 新增按钮-查询本应用内生成的菜单列表
  queryButtonMenuList(params) {
    return request({
      url: `${cloud.devcenter}/sysAcMenu/queryButtonMenuList`,
      method: 'get',
      params
    })
  },
  // 按钮详情
  sysAcButtonDetail(params) {
    return request({
      url: `${cloud.permission}/sysAcButton/detail`,
      method: 'get',
      params
    })
  },
  // 新增按钮
  lowCodeAddButton(data) {
    return request({
      url: `${cloud.permission}/sysAcButton/lowCodeAdd`,
      method: 'POST',
      data
    })
  }
})
