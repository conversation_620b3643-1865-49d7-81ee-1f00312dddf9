/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-14 14:51:15
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 17:23:43
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud}  from '@/framework/utils/request'
import {getRequest, postRequest} from '@/framework/utils/request'

// 分页查询
export function getList(query) {
  return getRequest(cloud.dqbasic + '/hazardGasDataMonitor/page', query)
}

// 查询单条数据详情
export function getDetail(id) {
  return getRequest(cloud.dqbasic + '/hazardGasDataMonitor/detail?id=' + id)
}

// 新增
export function add(data) {
  return postRequest(cloud.dqbasic + '/hazardGasDataMonitor/add', data)
}

// 修改
export function edit(data) {
  return postRequest(cloud.dqbasic + '/hazardGasDataMonitor/edit', data)
}

// 删除
export function del(id) {
  return postRequest(cloud.dqbasic + '/hazardGasDataMonitor/delete', { ids: id })
}
