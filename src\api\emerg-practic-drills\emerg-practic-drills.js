/*
 * @Author: mxt
 * @Date: 2024-04-22 15:53:40
 * @LastEditors:
 * @LastEditTime: 2024-04-22 16:42:26
 * @FilePath: \isrmp_vue\src\api\HealthExaminePlan\health-examine-plan.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud} from '@/framework/utils/request'

// 查询实战应急演练列表
export function emPraDrillsGetPage(query) {
  return request({
    url: cloud.dqbasic + '/emergPracticDrills/getListPage',
    method: 'get',
    params: query
  })
}

// 查询实战应急演练详细
export function getEmPraDrill(id) {
  return request({
    url: cloud.dqbasic + '/emergPracticDrills/detail?id=' + id,
    method: 'get'
  })
}
// 文件预览
export function showDecument(params) {
  return request({
    url: cloud.dqbasic + '/filePriview',
    method: 'get',
    params
  })
}
// 新增实战应急演练数据
export function addEmPraDrill(data) {
  return request({
    url: cloud.dqbasic + '/emergPracticDrills/add',
    method: 'post',
    data: data
  })
}

// 修改实战应急演练数据
export function updateEmPraDrill(data) {
  return request({
    url: cloud.dqbasic + '/emergPracticDrills/edit',
    method: 'post',
    data: data
  })
}

// 删除实战应急演练
export function delEmPraDrill(id) {
  return request({
    url: cloud.dqbasic + '/emergPracticDrills/delete',
    method: 'post',
    data: { ids: id }
  })
}
