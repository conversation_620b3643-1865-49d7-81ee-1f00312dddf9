<template>
  <el-dialog
    title="统计表"
    :visible.sync="dialogVisible"
    width="50%"
    :before-close="handleClose"
    top="10vh"
  >
    <div class="search">
      <el-button type="primary" @click="exportOut">
        导出
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      style="width: 100%;"
      border
      highlight-current-row
      :header-cell-style="{ backgroundColor: '#f2f2f2' }"
      :data="tableData"
    >
      <template slot="empty">
        <p>{{ $store.getters.dataText }}</p>
      </template>

      <el-table-column
        type="index"
        label="序号"
        width="70"
        :index=" (index) => index + 1"
      />

      <el-table-column
        label="所在组织"
        show-overflow-tooltip
        align="center"
        prop="orgName"
        min-width="300"
      />

      <el-table-column
        label="考试人数"
        align="center"
        prop="userCount"
        min-width="60"
      />

      <el-table-column
        label="平均分数"
        align="center"
        prop="averageScore"
        min-width="60"
      />
    </el-table>
  </el-dialog>
</template>

<script>
import {
  getExamOrgStatistics,
  exportExamOrgStatistics
} from '@/api/exam-manage/organize-exam'

export default {
  data() {
    return {
      // 弹框
      dialogVisible: false,

      // 表格
      loading:false,
      tableData:[],
      queryParams:{
        examId:''
      }
    }
  },

  methods: {
    // 初始化
    init(row) {
      this.queryParams.examId = row.examId
      this.getList()
      this.dialogVisible = true
    },

    // 获取列表
    getList() {
      const query = JSON.parse(JSON.stringify(this.queryParams))
      this.loading = true
      getExamOrgStatistics(query).then((res) => {
        this.loading = false
        this.tableData = res.data
      }).catch(() => {
        this.loading = false
      })
    },

    // 导出
    exportOut() {
      const data = JSON.parse(JSON.stringify(this.queryParams))
      exportExamOrgStatistics(data).then((res) => {
        const blob = new Blob([res.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const objectUrl = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = objectUrl // 指定下载链接
        a.download = '组织考试统计表' // 指定下载文件名
        a.click()
        URL.revokeObjectURL(a.href) // 释放URL对象
      })
    },

    // 关闭弹框
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.search{
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}
</style>
