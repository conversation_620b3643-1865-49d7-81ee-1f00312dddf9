/*
 * @Author: mxt
 * @Date: 2024-04-22 15:53:40
 * @LastEditors:
 * @LastEditTime: 2024-04-22 16:42:26
 * @FilePath: \isrmp_vue\src\api\HealthExaminePlan\health-examine-plan.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud} from '@/framework/utils/request'

// 查询应急事件归档数据列表
export function emPraDrillsGetPage(query) {
  return request({
    url: cloud.dqbasic + '/emergEventsArchiving/getListPage',
    method: 'get',
    params: query
  })
}

// 查询应急事件归档数据详细
export function getEmPraDrill(id) {
  return request({
    url: cloud.dqbasic + '/emergEventsArchiving/detail?id=' + id,
    method: 'get'
  })
}

// 新增应急事件归档数据
export function addEmPraDrill(data) {
  return request({
    url: cloud.dqbasic + '/emergEventsArchiving/add',
    method: 'post',
    data: data
  })
}

// 修改应急事件归档数据
export function updateEmPraDrill(data) {
  return request({
    url: cloud.dqbasic + '/emergEventsArchiving/edit',
    method: 'post',
    data: data
  })
}

// 删除应急事件归档数据
export function delEmPraDrill(id) {
  return request({
    url: cloud.dqbasic + '/emergEventsArchiving/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 导出应急事件归档数据
export function exportExcel(query) {
  return request({
    url: cloud.dqbasic + '/emergEventsArchiving/exportExcel',
    method: 'get',
    params: query,
    responseType: 'blob'
  })

  // return fetch(cloud.dqbasic + '/emergEventsArchiving/exportExcel', {
  //   method:'GET', // 请求类型
  //   type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  // })
}

