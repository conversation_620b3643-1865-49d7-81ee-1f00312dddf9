<template>
  <div class="upload-container">
    <el-button :style="{background:color,borderColor:color}" icon="el-icon-upload" size="mini" type="primary" @click=" dialogVisible=true">
      上传图片
    </el-button>
    <el-dialog :visible.sync="dialogVisible" :top="&quot;20vh&quot;" :close-on-click-modal="false" :modal="false">
      <el-upload
        :multiple="true"
        :file-list="fileList"
        :show-file-list="true"
        :headers="uplaodHeaders"
        :on-remove="handleRemove"
        :accept="'.jpg,.jpeg,.png,.PNG,.JPG,.bmp'"
        :on-success="handleSuccess"
        :before-upload="beforeUpload"
        class="editor-slide-upload"
        :action="baseURL() +'/sysFileInfo/upload?secretFlag=N'"
        list-type="picture-card"
      >
        <el-button size="small" type="primary">
          点击上传
        </el-button>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelFile">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </span>
      <!-- <el-button style="margin-bottom:20px" @click="dialogVisible = false">
        取消
      </el-button>
      <el-button type="primary" style="margin-bottom:20px" @click="handleSubmit">
        提交
      </el-button> -->
    </el-dialog>
  </div>
</template>

<script>
// import { getToken } from 'api/qiniu'
import { getToken } from '@/framework/utils/auth'
import { cloud } from '@/framework/utils/request'
export default {
  name: 'EditorSlideUpload',
  props: {
    color: {
      type: String,
      default: '#3461FF'
    }
  },
  data() {
    return {
      dialogVisible: false,
      listObj: {},
      fileList: [],
      uplaodHeaders: {
        Authorization: getToken()
      }
    }
  },
  computed: {
    uploadUrl() {
      return `${process.env.VUE_APP_BASE_API}/sysFileInfo/upload?secretFlag=N`
      // return baseURL() +'/sysFileInfo/upload?secretFlag=N'
    }
  },
  methods: {
    baseURL() {
      return process.env.VUE_APP_BASE_API + cloud.file
    },
    checkAllSuccess() {
      return Object.keys(this.listObj).every(item => this.listObj[item].hasSuccess)
    },
    cancelFile() {
      this.dialogVisible = false
      this.listObj = {}
      this.fileList = []
    },
    handleSubmit() {
      const arr = Object.keys(this.listObj).map(v => this.listObj[v])
      if (!this.checkAllSuccess()) {
        this.$message('Please wait for all images to be uploaded successfully. If there is a network problem, please refresh the page and upload again!')
        return
      }
      this.$emit('successCBK', arr)
      this.listObj = {}
      this.fileList = []
      this.dialogVisible = false
    },
    handleSuccess(response, file) {
      console.log('response', response)
      console.log('file', file)
      const uid = file.uid
      const objKeyArr = Object.keys(this.listObj)
      for (let i = 0, len = objKeyArr.length; i < len; i++) {
        if (this.listObj[objKeyArr[i]].uid === uid) {
          this.listObj[objKeyArr[i]].url = response.data.fileUrl
          this.listObj[objKeyArr[i]].hasSuccess = true
          return
        }
      }
    },
    handleRemove(file) {
      const uid = file.uid
      const objKeyArr = Object.keys(this.listObj)
      for (let i = 0, len = objKeyArr.length; i < len; i++) {
        if (this.listObj[objKeyArr[i]].uid === uid) {
          delete this.listObj[objKeyArr[i]]
          return
        }
      }
    },
    beforeUpload(file) {
      const arr = ['jpg', 'jpeg', 'png', 'PNG', 'JPG', 'bmp']
      const s = arr.filter(el => {
        return file.name.includes(el)
      })
      if (s.length == 0) {
        this.$message.error('图片格式不正确')
        return false
      } else {
        // 不符合文件

      }
      console.log('file', file)
      const _self = this
      const _URL = window.URL || window.webkitURL
      const fileName = file.uid
      this.listObj[fileName] = {}
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.src = _URL.createObjectURL(file)
        img.onload = function() {
          _self.listObj[fileName] = { hasSuccess: false, uid: file.uid, width: this.width, height: this.height }
          console.log(_self.listObj)
        }
        resolve(true)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.editor-slide-upload {
  margin-bottom: 20px;
  ::v-deep .el-upload--picture-card {
    width: 100%;
  }
}
</style>
