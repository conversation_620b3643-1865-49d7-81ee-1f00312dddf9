import { default as request, cloud } from '@/framework/utils/request'

export default ({
  fetchPage(params) {
    return request({
      url: `${cloud.usercenter}/sysUser/page/V2`,
      method: 'get',
      params
    })
  },
  exportUser(params) {
    return request({
      url: `${cloud.usercenter}/sysUser/exportUser`,
      method: 'post',
      // params,
      params,
      responseType: 'arraybuffer'
    })
  },
  exportAllUser(params) {
    return request({
      url: `${cloud.usercenter}/sysUser/exportAllUser/V2`,
      method: 'post',
      params,
      responseType: 'arraybuffer'
    })
  },
  changeStatus(data) {
    return request({
      url: `${cloud.usercenter}/sysUser/changeStatus`,
      method: 'POST',
      data
    })
  },
  detail(id) {
    return request({
      url: `${cloud.usercenter}/sysUser/detail`,
      method: 'get',
      params: {
        userId: id
      }
    })
  },
  edit(data) {
    return request({
      url: `${cloud.usercenter}/sysUser/edit`,
      method: 'POST',
      data,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    })
  },
  add(data) {
    return request({
      url: `${cloud.usercenter}/sysUser/add`,
      method: 'POST',
      data,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    })
  },
  delete(data) {
    return request({
      url: `${cloud.usercenter}/sysUser/delete`,
      method: 'post',
      data
    })
  },
  grantRole(data) {
    return request({
      url: `${cloud.usercenter}/sysUser/grantRole`,
      method: 'post',
      data
    })
  },
  resetPwd(data) {
    return request({
      url: `${cloud.usercenter}/sysUser/resetPwd`,
      method: 'post',
      data
    })
  },
  onlineUserList(params) {
    return request({
      url: `${cloud.usercenter}/sysUser/onlineUserList`,
      method: 'get',
      params
    })
  },
  removeSession(token) {
    return request({
      url: `${cloud.usercenter}/sysUser/removeSession`,
      method: 'post',
      data: {
        token
      }
    })
  },
  getUserListByIds(ids) {
    return request({
      url: `${cloud.usercenter}/sysUser/getUserListByIds`,
      method: 'get',
      params: {
        ids
      }
    })
  },

  // 导入
  uploadFile(data) {
    return request({
      url: `${cloud.usercenter}/sysUser/uploadFile`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  // 下载导入失败的数据
  downloadFailData(data) {
    return request({
      url: `${cloud.usercenter}sysUser/downloadFailData`,
      method: 'post',
      data
    })
  },
  // 下载导入模板
  downloadTemplate(params) {
    return request({
      url: `${cloud.usercenter}/sysUser/downloadTemplate`,
      method: 'get',
      params: {
        enclosure: '用户信息导入模板.xls'
      },
      responseType: 'arraybuffer'
    })
  }

})
// 用户自助中心---密码策略
export function getPwdRole(params) {
  return request({
    url: `${cloud.usercenter}/pwdConfig/getPwdConfig`,
    method: 'get',
    params
  })
}

// 发送验证码
export function sendMailCode(data) {
  return request({
    url: `${cloud.usercenter}/sysUser/sendMailCode`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
// 发送验证码
export function authSendMailCode(data) {
  return request({
    url: `${cloud.auth}/sendMailCode`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
// 检验验证码
export function validatorAuthCode(data) {
  return request({
    url: `${cloud.usercenter}/sysUser/validatorAuthCode`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 修改密码
export function setNewPassword(data) {
  return request({
    url: `${cloud.usercenter}/sysUser/setNewPassword`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 实时校验用户手机号，邮箱，账号是否唯一
export function checkPhoneOrEmailOnly(data) {
  return request({
    url: `${cloud.usercenter}/sysUser/checkPhoneOrEmailOnly`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 用户概览-数据统计分析
export function homePageCountByUser(params) {
  return request({
    url: `${cloud.usercenter}/sysUser/homePageCountByUser`,
    method: 'get',
    params
  })
}

// 用户概览-折线图
export function statisticalAdded(params) {
  return request({
    url: `${cloud.usercenter}/sysUser/statisticalAdded`,
    method: 'get',
    params
  })
}
export function getUserListByIds(ids) {
  return request({
    url: `${cloud.usercenter}/sysUser/getUserListByIds`,
    method: 'get',
    params: {
      ids
    }
  })
}

// 工作台-数据统计分析
export function homePageCount(params) {
  return request({
    url: `${cloud.usercenter}/sysUser/homePageCount`,
    method: 'get',
    params
  })
}

