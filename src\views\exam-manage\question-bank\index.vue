<template>
  <div class="app-container">
    <div class="mainbox1">
      <!-- 栏目管理 -->
      <div class="left">
        <div class="search">
          <el-button
            type="primary"
            style="margin-right: 8px"
            @click="showColumnsDialog"
          >
            栏目管理
          </el-button>

          <el-input
            v-model="query.classificationName"
            placeholder="搜索"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter.native="getColumnsList"
          />
        </div>

        <div class="items">
          <div
            v-for="item in columnsList"
            :key="item.id"
            class="col_item"
            :class="{ active: queryParams.classification == item.id }"
            @click="queryParams.classification = item.id,handleQuery()"
          >
            <span v-if="item.classificationName.length < 15">
              {{ item.classificationName }}
            </span>


            <el-tooltip
              v-else
              class="item"
              effect="dark"
              :content="item.classificationName"
              placement="top"
            >
              <span>{{ item.classificationName.slice(0,15) + '...' }}</span>
            </el-tooltip>
          </div>
        </div>
      </div>

      <!-- 题库列表 -->
      <div class="right">
        <div class="right_top">
          <div class="questions_sort">
            <div class="sort_item">
              单选题 <span>{{ typeCount.singleChoiceCount || 0 }}</span> 题
            </div>

            <div class="sort_item">
              多选题 <span>{{ typeCount.multipleChoiceCount || 0 }}</span> 题
            </div>

            <div class="sort_item">
              判断题 <span>{{ typeCount.judgeCount || 0 }}</span> 题
            </div>
          </div>

          <el-form
            ref="queryForm"
            :model="queryParams"
            :inline="true"
            label-width="40px"
            @submit.native.prevent
          >
            <el-form-item label="题型" prop="quesType">
              <el-select
                v-model="queryParams.quesType"
                placeholder="请选择"
                clearable
              >
                <el-option label="单选题" value="0" />

                <el-option label="多选题" value="1" />

                <el-option label="判断题" value="2" />
              </el-select>
            </el-form-item>

            <div class="fr">
              <el-button
                type="primary"
                icon="el-icon-search"
                @click="handleQuery"
              >
                搜索
              </el-button>

              <el-button icon="el-icon-refresh" @click="handleReset">
                重置
              </el-button>
            </div>
          </el-form>
        </div>

        <div class="right_bottom">
          <div class="opt_btns">
            <el-dropdown @command="handleAdd">
              <el-button type="primary" style="margin-right: 10px">
                新增<i class="el-icon-arrow-down el-icon--right" />
              </el-button>

              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="0">
                  单选题
                </el-dropdown-item>

                <el-dropdown-item command="1">
                  多选题
                </el-dropdown-item>

                <el-dropdown-item command="2">
                  判断题
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

            <el-button icon="el-icon-download" @click="handleDownloadTemplate">
              下载导入模板
            </el-button>

            <el-button
              class="filter-item"
              :loading="importLoading"
              icon="el-icon-upload2"
              @click="handleImport"
            >
              导入
            </el-button>

            <el-button
              type="primary"
              icon="el-icon-connection"
              @click="generateByAI"
            >
              AI生成题库
            </el-button>

            <el-button icon="el-icon-download" @click="exportOut">
              导出
            </el-button>

            <div class="flex-1" />
          </div>

          <el-table
            ref="table"
            v-loading="loading"
            style="width: 100%"
            border
            highlight-current-row
            :header-cell-style="{ backgroundColor: '#f2f2f2' }"
            :data="tableData"
          >
            <template slot="empty">
              <p>{{ $store.getters.dataText }}</p>
            </template>

            <el-table-column
              type="index"
              label="序号"
              width="70"
              :index="
                (index) =>
                  (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
              "
            />

            <el-table-column
              label="试题"
              show-overflow-tooltip
              align="center"
              prop="content"
              min-width="560"
            />

            <el-table-column
              label="题型"
              show-overflow-tooltip
              align="center"
              prop="quesType"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.quesType == '0'">
                  单选题
                </span>

                <span v-else-if="scope.row.quesType == '1'">
                  多选题
                </span>

                <span v-else-if="scope.row.quesType == '2'">
                  判断题
                </span>
              </template>
            </el-table-column>

            <el-table-column
              label="状态"
              show-overflow-tooltip
              align="center"
              prop="rememberNum"
              min-width="100"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.status == 1">
                  启用
                </span>

                <span v-else-if="scope.row.status == 2">
                  停用
                </span>
              </template>
            </el-table-column>

            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
              width="220"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button type="text" @click="handleDetail(scope.row)">
                  详情
                </el-button>

                <el-button
                  v-if="scope.row.status == 2"
                  type="text"
                  @click="handleEnable(scope.row)"
                >
                  启用
                </el-button>

                <el-button
                  v-if="scope.row.status == 1"
                  type="text"
                  @click="handleDisable(scope.row)"
                >
                  停用
                </el-button>

                <el-button
                  v-if="scope.row.status == 2"
                  type="text"
                  @click="handleDelete(scope.row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <dt-pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNo"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </div>

    <!-- 栏目管理弹框 -->
    <ColumnsDialog ref="columnsRef" @update="getColumnsList" />

    <!-- 新增弹框 -->
    <EditDialog ref="editRef" @update="handleQuery" />

    <!-- 详情弹框 -->
    <QuestionAnalysisDialog ref="questionAnalysisRef" />

    <!-- 导入弹框 -->
    <dtDialog
      title="考试题库导入"
      :visible.sync="fileUploadVisible"
      width="620px"
      :is-loading="uploadLoading"
      comfirm-label="提 交"
      @closeDialog="handleCloseImport"
      @comfirmBtn="importDataSave"
    >
      <el-form slot="content">
        <div class="">
          <dt-importFile
            ref="dtImportFileRef"
            :down-load-template="handleImportTemplate"
            :type="'考试题库'"
          />
        </div>
      </el-form>
    </dtDialog>
  </div>
</template>

<script>
import {
  getTrainingQuestionClassificationList,
  getTrainingQuestionPage,
  trainingQuestionDelete,
  trainingQuestionUpdateStatus,
  getCountQuestionByType,
  trainingQuestionExport,
  trainingQuestionTemplate,
  trainingQuestionImport
} from '@/api/exam-manage/question-bank'
import ColumnsDialog from './components/columns-dialog.vue'
import EditDialog from './components/edit-dialog.vue'
import QuestionAnalysisDialog from '@/components/question-analysis/index.vue'

export default {
  name: 'QuestionBank',
  components: {
    ColumnsDialog,
    EditDialog,
    QuestionAnalysisDialog
  },

  data() {
    return {
      // 栏目管理
      query: {
        classificationName: '', // 栏目名称
        status:1 // 栏目状态-启用
      },

      columnsList: [],

      // 查询表单参数
      queryParams: {
        quesType: '', // 题型
        classification: '', // 栏目
        pageNo: 1,
        pageSize: 10
      },

      // 表格loading
      loading: false,
      // 表格数据源
      tableData: [],
      // 表格总条数
      total: 0,

      // 题型统计
      typeCount:{},

      // 导入
      importLoading: false,
      fileUploadVisible: false,
      uploadLoading: false,
      handleImportTemplate: trainingQuestionTemplate
    }
  },

  watch: {
    fileUploadVisible: {
      handler(n, o) {
        if (!n) {
          this.$nextTick().then(() => {
            this.$refs.dtImportFileRef.fileList = []
          })
        }
      }
    }
  },

  created() {
    this.getColumnsList()
  },

  methods: {
    // 栏目管理查询
    getColumnsList() {
      getTrainingQuestionClassificationList(this.query).then((res) => {
        this.columnsList = res.data
        if (this.columnsList.length && this.columnsList.length > 0) {
          this.queryParams.classification = this.columnsList[0].id
          this.handleQuery()
        }
      })
    },

    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 重置
    handleReset() {
      this.queryParams = {
        quesType: '', // 题型
        classification: this.columnsList[0].id, // 栏目
        pageNo: 1,
        pageSize: 10
      }
      this.getList()
    },

    // 获取列表数据
    getList() {
      const query = JSON.parse(JSON.stringify(this.queryParams))
      this.loading = true
      getTrainingQuestionPage(query).then((res) => {
        this.loading = false
        this.tableData = res.data.rows
        this.total = res.data.totalRows
        this.getTypeCount()
      })
    },

    // 获取不同类型题目数量
    getTypeCount() {
      getCountQuestionByType({
        classification:this.queryParams.classification
      }).then((res) => {
        this.typeCount = res.data
      })
    },

    // 显示栏目管理弹框
    showColumnsDialog() {
      this.$refs.columnsRef.init()
    },

    // 新增
    handleAdd(val) {
      this.$refs.editRef.form.quesType = val
      this.$refs.editRef.form.classification = this.queryParams.classification
      this.$nextTick().then(() => {
        this.$refs.editRef.init()
      })
    },

    // 查看
    handleDetail(row) {
      // eslint-disable-next-line no-nested-ternary
      this.$refs.questionAnalysisRef.title = row.quesType == 0 ? '查看单选题' : row.quesType == 1 ? '查看多选题' : '查看判断题'
      this.$nextTick().then(() => {
        this.$refs.questionAnalysisRef.init(row.id)
      })
    },

    // 删除
    handleDelete(row) {
      this.$dtModal
        .confirm(`是否确认删除试题名称为"${row.content}"的数据项？`)
        .then(() => {
          row.ids = []
          row.ids.push(row.id)
          return trainingQuestionDelete(row)
        })
        .then((res) => {
          if (res.success == true) {
            this.getList()
            this.$dtModal.msgSuccess('删除成功')
          }
        })
        .catch(() => {})
    },

    // 启用
    handleEnable(row) {
      this.$dtModal
        .confirm(`是否确认启用试题名称为"${row.content}"的数据项？`)
        .then(() => {
          row.status = 1
          return trainingQuestionUpdateStatus(row)
        })
        .then((res) => {
          if ((res.success = true)) {
            this.getList()
            this.$dtModal.msgSuccess('启用成功')
          }
        })
        .catch(() => {})
    },

    // 停用
    handleDisable(row) {
      this.$dtModal
        .confirm(`是否确认停用试题名称为"${row.content}"的数据项？`)
        .then(() => {
          row.status = 2
          return trainingQuestionUpdateStatus(row)
        })
        .then((res) => {
          if ((res.success = true)) {
            this.getList()
            this.$dtModal.msgSuccess('停用成功')
          }
        })
        .catch(() => {})
    },

    // 下载导入模板
    handleDownloadTemplate() {
      trainingQuestionTemplate().then((res) => {
        const blob = new Blob([res.data], {
          type: 'application/vnd.ms-excel'
        })
        const objectUrl = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = objectUrl // 指定下载链接
        a.download = '考试题库模板' // 指定下载文件名
        a.click()
        URL.revokeObjectURL(a.href) // 释放URL对象
      })
    },

    // 导入
    handleImport() {
      this.importLoading = true
      this.fileUploadVisible = true
      this.$nextTick().then(() => {
        this.$refs.dtImportFileRef.fileList = []
      })
    },

    // 导入数据
    importDataSave() {
      this.uploadLoading = true
      const file = this.$refs.dtImportFileRef.fileList
      if (file.length == 0) {
        this.$dtMessage({
          title: '失败',
          message: '请选择需要上传的文件',
          type: 'error',
          duration: 2000
        })
        return
      }
      const formData = new FormData()
      // 数据
      formData.append('file', file[0].raw)
      formData.append('name', file[0].name)
      trainingQuestionImport(formData)
        .then(async (res) => {
          this.importLoading = false
          this.uploadLoading = true
          this.fileUploadVisible = false
          this.handleQuery()

          if (res.data.failMap) {
            this.$alert(
              `<div> <div class="title">${
                res.data.tip
              }</div> <div class="content">${Object.keys(res.data.failMap)
                .map((v) => `【${v}】${res.data.failMap[v]}`)
                .join(' ; ')}</div> </div>`,
              '导入完成',
              {
                dangerouslyUseHTMLString: true
              }
            )
          } else {
            this.$alert(`<div> <div class="title">${res.data.tip}导入完成`, {
              dangerouslyUseHTMLString: true
            })
          }
          await this.$nextTick()
          this.uploadLoading = false
        })
        .catch((res) => {
          this.uploadLoading = false
        })
    },

    // 关闭导入弹窗
    handleCloseImport() {
      this.fileUploadVisible = false
      this.importLoading = false
    },

    // 导出按钮操作
    exportOut() {
      trainingQuestionExport({ classification:this.queryParams.classification, quesType:this.queryParams.quesType }).then((res) => {
        const blob = new Blob([res.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const objectUrl = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = objectUrl // 指定下载链接
        a.download = '考试题库' // 指定下载文件名
        a.click()
        URL.revokeObjectURL(a.href) // 释放URL对象
      })
    },

    // AI生成题库
    generateByAI() {
      this.$dtModal.msgSuccess('AI生成题库')
    }
  }
}
</script>

<style lang="scss" scoped>
.mainbox1 {
  width: 100%;
  align-self: stretch;
  display: flex;
  padding: 16px;
  box-sizing: border-box;
  min-height: 81vh;

  .left {
    width: 20%;
    max-height: 108vh;
    margin-right: 16px;
    padding: 20px 16px 0;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 8px 2px rgba(0, 0, 0, 0.04),
      0 2px 6px 0 rgba(0, 0, 0, 0.06), 0 0 4px 0 rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;

    .search {
      display: flex;
    }

    .items {
      margin: 10px 0;
      flex: 1;
      width: 100%;
      height: 100%;
      overflow-y: auto;
      font-size: 14px;

      .col_item {
        height: 50px;
        border: 1px solid #e8e8e8;
        padding: 8px 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &.active {
          background: #e6f7ff;
        }

        &:not(:first-of-type) {
          border-top: none;
        }
      }
    }
  }

  .right {
    width: calc(80% - 16px);
    display: flex;
    flex-direction: column;

    .right_top {
      margin-bottom: 16px;
      padding: 20px 16px 0;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 4px 8px 2px rgba(0, 0, 0, 0.04),
        0 2px 6px 0 rgba(0, 0, 0, 0.06), 0 0 4px 0 rgba(0, 0, 0, 0.08);

      .questions_sort {
        display: flex;
        margin-bottom: 20px;
        font-size: 16px;

        .sort_item {
          margin-right: 20px;

          span {
            color: #f59a23;
            font-weight: 700;
          }
        }
      }
    }

    .right_bottom {
      flex: 1;
      padding: 20px 16px;
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 4px 8px 2px rgba(0, 0, 0, 0.04),
        0 2px 6px 0 rgba(0, 0, 0, 0.06), 0 0 4px 0 rgba(0, 0, 0, 0.08);

      .opt_btns {
        margin-bottom: 16px;
      }
    }
  }
}

::v-deep .el-dropdown-menu__item {
  padding: 0 16px;
}
</style>
