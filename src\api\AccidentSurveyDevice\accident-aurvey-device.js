import { default as request, cloud } from '@/framework/utils/request'

// 查询业务-事故调查设备故障列表
export function listAccidentSurveyDevice(query) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyDevice/list',
    method: 'get',
    params: query
  })
}

// 查询业务-事故调查设备故障详细
export function getAccidentSurveyDevice(id) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyDevice/detail?id=' + id,
    method: 'get'
  })
}

// 新增业务-事故调查设备故障
export function addAccidentSurveyDevice(data) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyDevice/add',
    method: 'post',
    data: data
  })
}

// 修改业务-事故调查设备故障
export function updateAccidentSurveyDevice(data) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyDevice/edit',
    method: 'post',
    data: data
  })
}

// 删除业务-事故调查设备故障
export function delAccidentSurveyDevice(id) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyDevice/delete',
    method: 'post',
    data: { ids: id }
  })
}
