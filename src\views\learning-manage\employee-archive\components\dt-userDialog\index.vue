<template>
  <dtDialog
    :title="textMap[dialogStatus]"
    :visible.sync="dialogFormVisible"
    :is-loading="loading"
    :footer-slot="dialogStatus === 'detail'? true : false"
    @closeDialog="resetTemp"
    @comfirmBtn="submitForm"
  >
    <div slot="content">
      <DtUserForm ref="userFormRef" />
    </div>
  </dtDialog>
</template>
<script>
import DtUserForm from '../dt-userForm'
import sysUserApi from '@/framework/api/userCenter/sysUser'
import { encryption } from 'DQBasic-vue-component'

export default {
  name: 'DtUserDialog',
  components: { DtUserForm },
  data() {
    return {
      dialogFormVisible: false,
      textMap: {
        update: '修改用户',
        create: '新增用户',
        detail: '用户详情'
      },
      dialogStatus: '',
      loading: false
    }
  },
  methods: {
    submitForm() {
      this.$refs.userFormRef.$refs.dataForm.validate((valid) => {
        if (valid) {
          this.loading = true
          this.updateData()
        }
      })
    },
    async updateData() {
      try {
        const temp = encryption({
          data: this.$refs.userFormRef.temp
        })
        if (this.dialogStatus == 'create') {
          await sysUserApi.add(temp)
        } else {
          await sysUserApi.edit(temp)
        }
        this.$dtMessage({
          title: '成功',
          message: this.dialogStatus == 'create' ? '新增成功' : '修改成功',
          type: 'success',
          duration: 2000
        })
        this.dialogFormVisible = false
        this.$parent.getList()
        this.loading = false
      } catch (error) {
        this.loading = false
      }
    },
    resetTemp() {
      this.$nextTick(() => {
        this.$refs.userFormRef.reset()
      })
    },
    openDialog(selectedOrg, dialogStatus, row) {
      this.dialogFormVisible = true
      this.dialogStatus = dialogStatus
      if (this.dialogStatus == 'create') {
        this.$nextTick(() => {
          this.$refs.userFormRef.openForm(selectedOrg, this.dialogStatus)
        })
      } else {
        sysUserApi.detail(row.userId).then((res) => {
          this.$nextTick(() => {
            this.$refs.userFormRef.openForm(res.data, this.dialogStatus)
          })
        })
      }
    }
  }
}
</script>
