<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="100px"
          @submit.native.prevent
        >
          <el-form-item label="年份" prop="examName">
            <el-date-picker
              v-model="queryParams.planYear"
              type="year"
              value-format="yyyy"
            />
          </el-form-item>

          <div class="fr">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">
              搜索
            </el-button>

            <el-button icon="el-icon-refresh" @click="handleReset">
              重置
            </el-button>
          </div>
        </el-form>
      </div>

      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleAdd()"
          >
            新增
          </el-button>

          <div class="flex-1" />
        </div>

        <el-table
          ref="table"
          v-loading="loading"
          style="width: 100%;"
          border
          highlight-current-row
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            type="index"
            label="序号"
            width="70"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            key="planYear"
            label="年份"
            show-overflow-tooltip
            align="center"
            prop="planYear"
          >
            <template #default="scope">
              <span class="view-text" @click="handleViewDetail(scope.row)">
                {{ scope.row.planYear }}
              </span>
            </template>
          </el-table-column>


          <el-table-column
            key="orgName"
            label="创建部门"
            show-overflow-tooltip
            align="center"
            prop="orgName"
            min-width="200"
          />

          <el-table-column
            key="planFile"
            label="培训计划文件"
            show-overflow-tooltip
            align="center"
            prop="examType"
            min-width="90"
          >
            <template #default="scope">
              <el-button
                :loading="scope.row.isFileDetailLoading"
                type="text"
                @click="handlePreviewFile(scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            width="160"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="handleViewExecutionDetail(scope.row)"
              >
                执行详情
              </el-button>

              <el-button
                v-if="scope.row.status != 1 "
                type="text"
                :loading="scope.row.isDeleting"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <AnnualPlanCompanyModal
      :visible.sync="modalVisible"
      :mode="modalMode"
      :record="modalRecord"
      @refresh="handleQuery"
    />

    <DocPreviewModal ref="docPreviewModalRef" />
  </div>
</template>

<script>
import AnnualPlanCompanyModal from './components/annual-plan-company-modal'
import { deleteAnnualPlanById, getAnnualPlanList } from '@/api/learning-manage/annual-plan'
import DocPreviewModal from '@/components/file-upload-echo/doc-preview-modal.vue'
import { getFileDetail } from '@/api/common'


export default {
  name: 'AnnualPlanCompany',
  components: { AnnualPlanCompanyModal, DocPreviewModal },
  data() {
    return {
      queryParams:{
        planYear: '',
        pageNo:1,
        pageSize:10
      },

      total:0,
      loading:false,
      tableData:[],
      modalMode: 'add',
      modalRecord: {},
      modalVisible: false,
      currentFileId: ''
    }
  },

  computed: {
  },

  watch: {},
  created() {
    this.handleQuery()
  },

  mounted() {

  },

  methods: {
    openModal(mode, record) {
      this.modalMode = mode
      this.modalRecord = mode === 'add' ? {} : JSON.parse(JSON.stringify(record))
      this.modalVisible = true
    },

    handleAdd() {
      this.openModal('add')
    },

    handleViewDetail(record) {
      this.openModal('view', record)
    },

    handlePreviewFile(row) {
      row.isFileDetailLoading = true
      getFileDetail(row.planFile)
        .then((res) => {
          this.$refs.docPreviewModalRef.show(res.data.fileLink)
        })
        .finally(() => {
          row.isFileDetailLoading = false
        })
    },

    handleDelete(record) {
      this.$confirm('删除该年度培训计划后，部门账号下的年度培训计划将同步删除，如有部门已完成计划，则需重新操作完成，确定删除？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          record.isDeleting = true
          deleteAnnualPlanById(record.id)
            .then(() => {
              record.isDeleting = false
            })
            .then(() => this.handleQuery())
        })
        .catch(() => {
          console.log('操作取消')
        })
    },

    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    handleReset() {
      this.queryParams = {
        planYear: '',
        pageNo:1,
        pageSize:10
      }
      this.getList()
    },

    transformListData(listData) {
      return listData.map((item) => ({
        ...item,
        isDeleting: false,
        isFileDetailLoading: false
      }))
    },

    getList() {
      const loadData = (data) => {
        this.tableData = this.transformListData(data.data.rows)
        this.total = data.data.totalRows
      }

      this.loading = true
      getAnnualPlanList(this.queryParams)
        .then(loadData)
        .finally(() => {
          this.loading = false
        })
    },

    handleViewExecutionDetail(row) {
      const params = {
        yearPlanId: row.id,
        fileId: row.planFile,
        orgId: row.planOrgId,
        orgName: row.orgName,
        planYear: row.planYear
      }
      const searchParams = new URLSearchParams(Object.entries(params))
      this.$router.push(`/annualPlanExecutionDetail?${searchParams.toString()}`)
    }
  }
}
</script>

<style scoped lang="scss">
.view-text {
  color: var(--primary);
  cursor: pointer;
  font-weight: 500;
}
</style>
