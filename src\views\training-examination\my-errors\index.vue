<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="80px"
          @submit.native.prevent
        >
          <el-form-item label="试题" prop="content">
            <el-input
              v-model.trim="queryParams.content"
              maxlength="30"
              clearable
              placeholder="请输入"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item label="题型" prop="quesType">
            <el-select v-model="queryParams.quesType" placeholder="请选择" clearable>
              <el-option label="单选题" value="0" />

              <el-option label="多选题" value="1" />

              <el-option label="判断题" value="2" />
            </el-select>
          </el-form-item>

          <el-form-item label="来源" prop="source">
            <el-select v-model="queryParams.source" placeholder="请选择" clearable>
              <el-option label="练习" value="1" />

              <el-option label="考试" value="2" />
            </el-select>
          </el-form-item>

          <el-form-item label="来源名称" prop="sourceName">
            <el-input
              v-model.trim="queryParams.sourceName"
              maxlength="30"
              clearable
              placeholder="请输入"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-form>

        <div class="flex-1" />

        <div class="fr">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            搜索
          </el-button>

          <el-button icon="el-icon-refresh" @click="handleReset">
            重置
          </el-button>
        </div>
      </div>

      <div class="table-container table-fullscreen">
        <el-table
          ref="table"
          v-loading="loading"
          style="width: 100%;"
          border
          highlight-current-row
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            type="index"
            label="序号"
            width="70"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            label="试题"
            show-overflow-tooltip
            align="center"
            prop="content"
            min-width="300"
          />

          <el-table-column
            label="题型"
            show-overflow-tooltip
            align="center"
            prop="quesType"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.quesType == 0">
                单选题
              </span>

              <span v-else-if="scope.row.quesType == 1">
                多选题
              </span>

              <span v-else-if="scope.row.quesType == 2">
                判断题
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="答题时间"
            show-overflow-tooltip
            align="center"
            prop="examTime"
            min-width="100"
          />

          <el-table-column
            label="来源"
            show-overflow-tooltip
            align="center"
            prop="source"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.source == 1">
                练习
              </span>

              <span v-else-if="scope.row.source == 2">
                考试
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="来源名称"
            show-overflow-tooltip
            align="center"
            prop="sourceName"
          />

          <el-table-column
            label="操作"
            align="center"
            min-width="100"
          >
            <template slot-scope="scope">
              <el-button type="text" @click="showAnalysis(scope.row)">
                查看解析
              </el-button>

              <el-button type="text" @click="handleDelete(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 查看解析弹框 -->
    <QuestionAnalysisDialog ref="questionAnalysisRef" :show-user-answer="true" />
  </div>
</template>

<script>
import {
  getTrainingMyErrorQuestionPage,
  trainingMyErrorQuestionDelete
} from '@/api/training-examination/my-errors'
import QuestionAnalysisDialog from '@/components/question-analysis/index.vue'

export default {
  name: 'MyErrors',
  components: {
    QuestionAnalysisDialog
  },

  data() {
    return {
      // 表格
      loading: false,
      tableData: [],
      total: 0,
      queryParams:{
        content:'', // 试题
        quesType:'', // 题型
        source:'', // 来源
        sourceName:'', // 来源名称
        pageNo:1,
        pageSize:10
      }
    }
  },

  mounted() {
    this.handleQuery()
  },

  methods: {
    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 重置
    handleReset() {
      this.queryParams = {
        exerciseName:'',
        pageNo:1,
        pageSize:10
      }
      this.getList()
    },

    // 获取列表
    getList() {
      this.loading = true
      getTrainingMyErrorQuestionPage(this.queryParams).then((data) => {
        this.loading = false
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      }).catch(() => {
        this.loading = false
      })
    },

    // 查看解析
    showAnalysis(row) {
      this.$refs.questionAnalysisRef.init(row.questionId, JSON.parse(JSON.stringify(row)))
    },

    // 删除
    handleDelete(row) {
      this.$dtModal
        .confirm(`是否确认删除错题名称为"${row.content}"的数据项？`)
        .then(() => {
          row.ids = []
          row.ids.push(row.id)
          return trainingMyErrorQuestionDelete(row)
        })
        .then((res) => {
          if (res.success == true) {
            this.getList()
            this.$dtModal.msgSuccess('删除成功')
          }
        })
        .catch(() => {})
    }
  }
}
</script>
