<!--
  * @Author: jiadongjin <EMAIL>
  * @Date: 2024-05-16 11:19:31
  * @LastEditors: jiadongjin <EMAIL>
  * @LastEditTime: 2024-05-16 11:52:43
  * @Description: 
  * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
-->
<template>
  <div ref="imageBox" class="el-image">
    <slot v-if="loading" name="placeholder">
      <div class="el-image__placeholder" />
    </slot>

    <slot v-else-if="error" name="error">
      <div class="el-image__error">
        {{ t('el.image.error') }}
      </div>
    </slot>

    <img
      v-else
      id="preview_img"
      class="el-image__inner"
      alt=""
      v-bind="$attrs"
      :src="src"
      :style="imageStyle"
      :class="{ 'el-image__inner--center': alignCenter, 'el-image__preview': preview }"
      v-on="$listeners"
      @click="clickHandler"
      @dblclick="clickHandler"
    >

    <div class="mask" @click="clickHandler" @dblclick="clickHandler" />

    <template v-if="preview">
      <!-- 改为使用本地组件 原image-viewer -> 新my-image-viewer -->
      <MyImageViewer
        v-if="showViewer"
        :z-index="zIndex"
        :initial-index="imageIndex"
        :on-close="closeViewer"
        :show-mark-box="$attrs['show-mark-box']"
        :url-list="previewSrcList"
        v-on="$listeners"
      />
    </template>
  </div>
</template>

<script>
// 继承element-ui el-image，使用本地修改阻尼后的my-image-viewer
import MyImageViewer from './my-image-viewer'
import ElImage from 'element-ui/packages/image/src/main'

const prevOverflow = ''
export default {
  name: 'MyImage',
  components: { MyImageViewer },
  extends: ElImage,
  methods: {
    // 重写图片关闭方法
    closeViewer(id) {
      document.body.style.overflow = prevOverflow
      this.showViewer = false
      // 通知父组件
      this.$emit('onClose', id)
    },

    // 重写handleLoad方法
    handleLoad(e, img) {
      this.imageWidth = img.width
      this.imageHeight = img.height
      this.loading = false
      this.error = false

      // 通知父组件图片的原始宽高
      this.$emit('onImageLoaded', { width: this.imageWidth, height: this.imageHeight })
    }
  }
}
</script>

<style lang="scss" scoped>
.el-image {
  position: relative;
  cursor: pointer;

  .el-image__inner {
    width: 100%;
    height: 100px;
    object-fit: contain; // 保持原比例缩小
  }

  .mask {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 2;
  }
}
</style>
