<!--
  * @Author: du<PERSON>yan <EMAIL>
  * @Date: 2024-07-24 09:13:19
  * @LastEditors: duhongyan <EMAIL>
  * @LastEditTime: 2024-07-24 09:16:46
  * @FilePath: \isrmp_vue\src\components\file-upload-echo\doc-preview-modal.vue
  * @Description:
  * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
-->
<template>
  <DtDialog
    title="文件预览"
    :visible.sync="docVisible"
    :lock-scroll="false"
    :append-to-body="true"
    :confirm-hidden="true"
    width="960px"
    comfirm-label="确 定"
    :footer-slot="true"
    @closeDialog="closeDialog"
  >
    <div
      id="iframeCon"
      v-loading="loadingIframe"
      element-loading-text="拼命加载中..."
      style="width: 100%; height: 600px; overflow: hidden"
    />
  </DtDialog>
</template>

<script>
import { showDecument } from "@/api/common/index"
export default {
  data(){
    return{
      docVisible: false,
      loadingIframe: false
    }
  },

  methods: {
    show(url){
      this.docVisible = true
      this.showDocument(url)
    },

    /** 文档预览 */
    showDocument(url) {
      this.loadingIframe = true
      this.docVisible = true

      this.$nextTick().then(() => {
        this.addIframe(url)
      })
    },

    // 创建预览
    addIframe(url) {
      const targetDiv = document.getElementById("iframeCon")
      showDecument({ url: url }).then((res) => {
        const iframe = document.createElement("iframe")
        iframe.setAttribute("frameborder", 0)
        iframe.setAttribute("width", "100%")
        iframe.setAttribute("height", "100%")
        iframe.setAttribute("id", "ifm")
        iframe.setAttribute("src", res.message)
        targetDiv.appendChild(iframe)
        if (iframe.attachEvent) {
          // 兼容IE
          iframe.attachEvent("onload", () => {
            // 加载完成
            this.loadingIframe = false
          })
        } else {
          iframe.onload = () => {
            // 加载完成
            this.loadingIframe = false
          }
        }
      })
    },

    /** 关闭预览 */
    closeDialog() {
      this.docVisible = false
      this.loadingIframe = true
      const nodeToRemove = document.getElementById("ifm")
      const parentNode = nodeToRemove.parentNode
      parentNode.removeChild(nodeToRemove)
    }
  }
}
</script>
