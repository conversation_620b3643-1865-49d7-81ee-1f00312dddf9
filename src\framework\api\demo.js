import { default as request, cloud } from '@/framework/utils/request'

export default ({
  fetchList(params) {
    return request({
      url: `${cloud.dqbasic}/demo/list`,
      method: 'get',
      params
    })
  },
  fetchPage(params) {
    return request({
      url: `${cloud.dqbasic}/demo/page`,
      method: 'get',
      params
    })
  },
  updateStatus(data) {
    return request({
      url: `${cloud.dqbasic}/demo/updateStatus`,
      method: 'POST',
      data
    })
  },
  detail(id) {
    return request({
      url: `${cloud.dqbasic}/demo/detail`,
      method: 'get',
      params: {
        positionId: id
      }
    })
  },
  edit(data) {
    return request({
      url: `${cloud.dqbasic}/demo/edit`,
      method: 'POST',
      data
    })
  },
  add(data) {
    return request({
      url: `${cloud.dqbasic}/demo/add`,
      method: 'POST',
      data
    })
  },
  delete(data) {
    return request({
      url: `${cloud.dqbasic}/demo/delete`,
      method: 'post',
      data
    })
  },
  exportPostion(params) {
    return request({
      url: `${cloud.dqbasic}/hrPosition/exportPostion`,
      method: 'post',
      // params,
      params,
      responseType: 'arraybuffer'
    })
  }

})
