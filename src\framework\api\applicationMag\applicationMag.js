import { default as request, cloud } from '@/framework/utils/request'

// 查询应用管理列表
export function listApp(query) {
  return request({
    url: `${cloud.manage}/sysApp/page`,
    method: 'get',
    params: query
  })
}
// 查询不分页的应用管理列表
export function getAppMagList(query) {
  return request({
    url: `${cloud.manage}/sysApp/list`,
    method: 'get',
    params: query
  })
}

// 查询应用管理详细
export function getApp(appId) {
  return request({
    url: `${cloud.manage}/sysApp/detail?appId=${appId}`,
    method: 'get'
  })
}

// 新增应用管理
export function addApp(data) {
  return request({
    url: `${cloud.manage}/sysApp/add`,
    method: 'post',
    data
  })
}

// 修改应用管理
export function updateApp(data) {
  return request({
    url: `${cloud.manage}/sysApp/edit`,
    method: 'post',
    data
  })
}

// 删除应用管理
export function delApp(appId) {
  return request({
    url: `${cloud.manage}/sysApp/delete`,
    method: 'post',
    data: { appId }
  })
}

// 更新激活状态
// export function updateActiveFlag(appId) {
//   return request({
//     url: cloud.manage + '/sysApp/updateActiveFlag',
//     method: 'post',
//     data: { appId: appId }
//   })
// }

// 更新启用状态
export function updateStatus(appId, statusFlag) {
  return request({
    url: `${cloud.manage}/sysApp/updateStatus`,
    method: 'post',
    data: { appId, statusFlag }
  })
}
