import { default as request, cloud } from '@/framework/utils/request'

// 流程详情-获取流程详情基本信息
export function getTitleInfo(query) {
  return request({
    url: `${cloud.process}/myflow/instance/getTitleInfo`,
    method: 'get',
    params: query
  })
}
// 发起申请-查询发起申请时的表单数据
export function startFormData(query) {
  return request({
    url: `${cloud.process}/myflow/instance/getStartFormData`,
    method: 'get',
    params: query
  })
}
// 发起申请-开始流程
export function startForm(data) {
  return request({
    url: `${cloud.process}/myflow/flowable/startProcess`,
    method: 'post',
    data
  })
}
// 发起申请-暂存（新增/更新草稿）
export function addOrUpdate(data) {
  return request({
    url: `${cloud.process}/myflow/draft/save`,
    method: 'post',
    data
  })
}
// 申请草稿-草稿详情-数据回显
export function draftDetail(query) {
  return request({
    url: `${cloud.process}/myflow/draft/detail`,
    method: 'get',
    params: query
  })
}
// 申请草稿-草稿提交
export function draftApply(data) {
  return request({
    url: `${cloud.process}/myflow/draft/apply`,
    method: 'post',
    data
  })
}
// 已发申请-表单详情（已走过的节点的表单）
export function formApplyDetail(query) {
  return request({
    url: `${cloud.process}/myflow/instance/applyedFormDetail`,
    method: 'get',
    params: query
  })
}
// 已发申请-流程详情-流程设计图、流转信息
export function formApplyDetailImg(query) {
  return request({
    url: `${cloud.process}/flow/instance/image`,
    method: 'get',
    params: query
  })
}
// 已发申请-流程详情-任务详情信息列表展示
export function taskListDetail(query) {
  return request({
    url: `${cloud.process}/flow/instance/taskList`,
    method: 'get',
    params: query
  })
}
// 已发申请-流程详情-时间线图
export function taskViewList(query) {
  return request({
    url: `${cloud.process}/flow/instance/taskViewList`,
    method: 'get',
    params: query
  })
}
// 已发申请-流程详情-根据节点ID查询节点详情
export function getActivityDetail(query) {
  return request({
    url: `${cloud.process}/flow/instance/getActivityDetail`,
    method: 'get',
    params: query
  })
}
// 已办任务-流程详情-表单详情
export function doneTaskFormDetal(query) {
  return request({
    url: `${cloud.process}/myflow/instance/doneTaskFormDetail`,
    method: 'get',
    params: query
  })
}
// 抄送事宜-抄送详情
export function csFormDetail(query) {
  return request({
    url: `${cloud.process}/myflow/instance/csFormDetail`,
    method: 'get',
    params: query
  })
}
// 抄送事宜-抄送已读
export function flowableCsRead(data) {
  return request({
    url: `${cloud.process}/myflow/cs/read`,
    method: 'post',
    data
  })
}
// 待办任务-表单详情（当前正在处理的节点的表单）
export function getDetailById(query) {
  return request({
    url: `${cloud.process}/myflow/instance/currentFormDetail`,
    method: 'get',
    params: query
  })
}
// 待办任务-审批权限查询
export function getPermList(query) {
  return request({
    url: `${cloud.process}/myflow/flowableToDoTask/permList`,
    method: 'get',
    params: query
  })
}
// 待办任务-提交审批
export function handleTask(data) {
  return request({
    url: `${cloud.process}/myflow/flowableToDoTask/handleTask`,
    method: 'post',
    data
  })
}
// 待办任务-退回之后 再次提交审批
export function handleTaskRepeat(data) {
  return request({
    url: `${cloud.process}/myflow/flowableToDoTask/handleTask/repeat`,
    method: 'post',
    data
  })
}
// 选人组件-回显数据
export function getManagingOrgUserList(query) {
  return request({
    url: `${cloud.process}/flow/managerGroup/getManagingOrgUserList`,
    method: 'get',
    params: query
  })
}
// 根据用户IDs查询用户详情列表
export function getUserListByUserIds(ids) {
  return request({
    url: `${cloud.permission}/managerGroup/getUserListByUserIds`,
    method: 'get',
    params: {
      userIds: ids
    }
  })
}
// 选人组件-根据用户姓名或账号搜索，不进行是否选择检查
export function searchUserName(query) {
  return request({
    url: `${cloud.process}/flow/managerGroup/searchUserName`,
    method: 'get',
    params: {
      nameOrAccount: query.name,
      instanceId: query.instanceId,
      nodeId: query.nodeId,
      isFlowCarbonUser: query.isFlowCarbonUser,
      orgId: query.orgId,
      procdefId: query.procdefId
    }
  })
}
// 选人组件-根据组织名称进行搜索
export function searchOrgName(query) {
  return request({
    url: `${cloud.process}/flow/managerGroup/searchOrgName`,
    method: 'get',
    params: {
      orgName: query.name
      // 'instanceId': query.instanceId,
      // 'nodeId': query.nodeId,
      // 'isFlowCarbonUser': query.isFlowCarbonUser,
      // 'orgId': query.orgId,
      // 'procdefId': query.procdefId
    }
  })
}

