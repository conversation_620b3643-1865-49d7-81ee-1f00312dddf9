/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-22 18:07:36
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 18:02:19
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */

import request, {cloud}  from '@/framework/utils/request'
import {getRequest, postRequest} from '@/framework/utils/request'

// 查询健康数据预警列表
export function getList(query) {
  return getRequest(cloud.dqbasic + '/hazardDataWarning/page', query)
}

// 查询健康数据预警详细
export function getDetail(id) {
  return getRequest(cloud.dqbasic + '/hazardDataWarning/detail?id=' + id)
}

// 新增健康数据预警
export function add(data) {
  return postRequest(cloud.dqbasic + '/hazardDataWarning/add', data)
}

// 修改健康数据预警
export function edit(data) {
  return postRequest(cloud.dqbasic + '/hazardDataWarning/edit', data)
}

// 删除健康数据预警
export function del(id) {
  return postRequest(cloud.dqbasic + '/hazardDataWarning/delete', { ids: id })
}

// 修改--报警处理
export function deal(data) {
  return postRequest(cloud.dqbasic + '/hazardDataWarning/deal', data)
}
