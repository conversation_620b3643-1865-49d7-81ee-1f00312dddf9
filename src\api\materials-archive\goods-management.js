/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-17 14:27:22
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-19 14:34:38
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud} from '@/framework/utils/request'

// 查询物资管理列表
export function listGoodsManagement(query) {
  return request({
    url: cloud.dqbasic +'/maGoodsManagement/page',
    method: 'get',
    params: query
  })
}

// 查询物资管理详细
export function getGoodsManagement(id) {
  return request({
    url: cloud.dqbasic +'/maGoodsManagement/detail?id=' + id,
    method: 'get'
  })
}
// 去重查询物资管理中的类别
export function queryExistCategory() {
  return request({
    url: '/project/maGoodsManagement/queryExistCategory',
    method: 'get'
  })
}

// 新增物资管理
export function addGoodsManagement(data) {
  return request({
    url: cloud.dqbasic +'/maGoodsManagement/add',
    method: 'post',
    data: data
  })
}

// 修改物资管理
export function updateGoodsManagement(data) {
  return request({
    url: cloud.dqbasic +'/maGoodsManagement/edit',
    method: 'post',
    data: data
  })
}

// 删除物资管理
export function delGoodsManagement(data) {
  return request({
    url: cloud.dqbasic +'/maGoodsManagement/delete',
    method: 'post',
    data: data
  })
}
