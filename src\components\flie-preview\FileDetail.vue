<template>
  <el-dialog
    ref="dialogRef"
    :title="title"
    :visible.sync="dialogVisible"
    :append-to-body="true"
  >
    <div v-if="['jpg','jpeg','png','gif'].includes(fileInfo.fileSuffix)">
      <el-image
        :src="fileInfo.fileLink"
        fit="contain"
        :preview-src-list="[fileInfo.fileLink]"
      />
    </div>

    <div v-if="['pdf','pptx'].includes(fileInfo.fileSuffix)">
      <VueOfficePdf
        style="height: 100vh;"
        :src="fileInfo.fileLink"
      />
    </div>

    <div v-if="['docx','doc'].includes(fileInfo.fileSuffix)">
      <VueOfficeDocx
        style="height: 100vh;"
        :src="fileInfo.fileLink"
      />
    </div>

    <div v-if="['xls','xlsx'].includes(fileInfo.fileSuffix)">
      <VueOfficeExcel
        style="height: 100vh;"
        :src="fileInfo.fileLink"
        :options="{ xls: true }"
      />
    </div>


    <div v-if="fileInfo.fileSuffix=='mp4'||fileInfo.fileSuffix=='mov'">
      <video
        ref="videoPlayer"
        style="width: 100%;"
        controls :src="fileInfo.fileLink"
      />
    </div>
  </el-dialog>
</template>

<script>
import VueOfficePdf from '@vue-office/pdf'
import VueOfficeDocx from '@vue-office/docx'
import VueOfficeExcel from '@vue-office/excel'
import '@vue-office/docx/lib/index.css'
import '@vue-office/excel/lib/index.css'
import { getFileDetail } from '@/api/common/index'
import { Loading } from 'element-ui'

export default {
  name: 'FilePreview',
  components: {
    VueOfficePdf,
    VueOfficeDocx,
    VueOfficeExcel
  },

  props: {
    fileId: {
      type: String,
      default: ''
    },

    title: {
      type: String,
      default: '文件预览'
    }
  },

  data() {
    return {
      dialogVisible:false,
      fileInfo:{},
      fileTitle: ''
    }
  },

  computed: {
    dialogTitle() {
      return this.fileTitle || this.title
    }
  },

  methods: {
    openDialog() {
      this.dialogVisible = true
      this.getFileInfo()
    },

    async getFileInfo() {
      const loadingInstance = Loading.service({
        fullscreen: false,
        target: this.$refs.dialogRef.$el.querySelector('.el-dialog__body')
      })
      try {
        const res = await getFileDetail(this.fileId)
        this.fileInfo = res.data
        this.fileTitle = this.fileInfo.fileOriginName
      } finally {
        loadingInstance.close()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-image {
  display: flex;
  justify-content: center;
}
</style>
