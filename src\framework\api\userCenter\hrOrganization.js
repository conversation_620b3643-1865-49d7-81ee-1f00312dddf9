import { default as request, cloud } from '@/framework/utils/request'

export default ({

  // 组织回显
  getListByIds(ids) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/getListByIds`,
      method: 'get',
      params: {
        ids
      }
    })
  },
  fetchList(params) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/tree/V2`,
      method: 'get',
      params
    })
  },
  getOrgListTreeNode(params) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/roleBindOrgScopeLazyAntdv/V2`,
      method: 'get',
      params
    })
  },
  getOrgList(params) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/listTree/V2`,
      method: 'get',
      params
    })
  },
  // 懒加载
  listByDataScope(params) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/listByDataScope/V2`,
      method: 'get',
      params
    })
  },
  // 带参数查询分页
  pageList(params) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/pageList/V2`,
      method: 'get',
      params
    })
  },
  add(data) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/add`,
      method: 'POST',
      data
    })
  },
  edit(data) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/edit`,
      method: 'POST',
      data
    })
  },
  detail(id) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/detail`,
      method: 'get',
      params: {
        orgId: id
      }
    })
  },
  delete(data) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/delete`,
      method: 'post',
      data
    })
  },
  /**
   * 添加党组织
   * @param {*} data 组织信息
   * @returns res
   */
  saveOrg(data) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/saveOrg`,
      method: 'POST',
      data
    })
  },
  currentTree(params) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/currentTree`,
      method: 'get',
      params
    })
  },
  info(id) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/info`,
      method: 'get',
      params: {
        orgId: id
      }
    })
  },
  detailInfo(id) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/detailInfo`,
      method: 'get',
      params: {
        orgId: id
      }
    })
  },
  deleteOrg(data) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/deleteOrg`,
      method: 'post',
      data
    })
  },
  moveTree(data) {
    return request({
      url: `${cloud.usercenter}/hrOrganization/moveTree`,
      method: 'post',
      data
    })
  }

})
