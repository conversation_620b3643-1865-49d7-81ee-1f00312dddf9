/*
 * @Author: du<PERSON><PERSON> <EMAIL>
 * @Date: 2024-04-16 11:01:04
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-04-23 14:43:47
 * @FilePath: \isrmp_vue\src\api\AccidentReport\accident-report.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */

import { default as request, cloud } from '@/framework/utils/request'

// 查询业务-事故上报列表
export function listAccidentReport(query) {
  return request({
    url: cloud.dqbasic + '/bizAccidentReport/page',
    method: 'get',
    params: query
  })
}

// 查询业务-事故上报详细
export function getAccidentReport(id) {
  return request({
    url: cloud.dqbasic + '/bizAccidentReport/detail?id=' + id,
    method: 'get'
  })
}

// 新增业务-事故上报
export function addAccidentReport(data) {
  return request({
    url: cloud.dqbasic + '/bizAccidentReport/add',
    method: 'post',
    data: data
  })
}

// 修改业务-事故上报
export function updateAccidentReport(data) {
  return request({
    url: cloud.dqbasic + '/bizAccidentReport/edit',
    method: 'post',
    data: data
  })
}

// 删除业务-事故上报
export function delAccidentReport(id) {
  return request({
    url: cloud.dqbasic + '/bizAccidentReport/delete',
    method: 'post',
    data: { ids: id }
  })
}
// 提交事故上报
export function submitAccidentReport(data) {
  return request({
    url: cloud.process + '/myflow/flowable/startProcess/biz',
    method: 'post',
    data
  })
}

// 获取上报详情  展示审核页面
export function getAuditDetail(query) {
  return request({
    url: cloud.process + '/myflow/instance/applyedFormDetail/biz',
    method: 'get',
    params: query
  })
}
// 获取数据库表名
export function getTableName(query) {
  return request({
    url: cloud.dqbasic + '/bizAccidentReport/getTableName',
    method: 'get',
    params: query
  })
}

// 获取事故状态列表
export function getAccidentStatusList(query) {
  return request({
    url: cloud.dqbasic + '/bizAccidentReport/enums',
    method: 'get',
    params: query
  })
}

