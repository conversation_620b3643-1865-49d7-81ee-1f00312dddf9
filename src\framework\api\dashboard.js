import { default as request, cloud } from '@/framework/utils/request'

// 后台获取当前登录人，并根据登录人权限范围查询 平台用户数量 所能看到的组织数量 角色数量 菜单量
const getHomePageCountByUser = (params) => request({ url: `${cloud.dqbasic}/sysUser/homePageCountByUser`, method: 'get', data: params })
const checkPwd = (params) => request({ url: `${cloud.auth}/checkPwd`,
  method: 'post',
  data: params,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  } })

export {
  getHomePageCountByUser,
  checkPwd
}
