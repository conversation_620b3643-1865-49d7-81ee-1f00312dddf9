import request, {cloud} from '@/framework/utils/request'

// 查询值班值守列表
export function listOnGuardInfo(query) {
  return request({
    url: cloud.dqbasic + '/onGuardInfo/page',
    method: 'get',
    params: query
  })
}

// 查询值班值守详细
export function getOnGuardInfo(id) {
  return request({
    url: cloud.dqbasic + '/onGuardInfo/detail?id=' + id,
    method: 'get'
  })
}

// 新增值班值守
export function addOnGuardInfo(data) {
  return request({
    url: cloud.dqbasic + '/onGuardInfo/add',
    method: 'post',
    data: data
  })
}
// 新增值班值守
export function importTemplate(data) {
  return request({
    url: '/project/onGuardInfo/importExcel',
    method: 'post',
    data: data
  })
}
// 修改值班值守
export function updateOnGuardInfo(data) {
  return request({
    url: cloud.dqbasic + '/onGuardInfo/edit',
    method: 'post',
    data: data
  })
}

// 删除值班值守
export function delOnGuardInfo(id) {
  return request({
    url: cloud.dqbasic + '/onGuardInfo/delete',
    method: 'post',
    data: { ids: id }
  })
}
