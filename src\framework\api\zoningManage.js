import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 条件查询
  list(params) {
    return request({
      url: `${cloud.dqbasic}/area/list`,
      method: 'get',
      params
    })
  },
  queryCounty(params) {
    return request({
      url: `${cloud.dqbasic}/area/queryLowerLevel`,
      method: 'get',
      params
    })
  },
  // // 五级
  // queryFive(params) {
  //   return request({
  //     url: cloud.dqbasic + '/area/queryLowerLevel',
  //     method: 'get',
  //     params
  //   })
  // },
  add(data) {
    return request({
      url: `${cloud.dqbasic}/area/add`,
      method: 'post',
      data
    })
  },
  // 查看详情
  detail(params) {
    return request({
      url: `${cloud.dqbasic}/area/detail`,
      method: 'get',
      params
    })
  },
  // 上级区划名称查询
  queryHigherLevel(params) {
    return request({
      url: `${cloud.dqbasic}/area/queryHigherLevel`,
      method: 'get',
      params
    })
  },
  update(data) {
    return request({
      url: `${cloud.dqbasic}/area/update`,
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: `${cloud.dqbasic}/area/delete`,
      method: 'post',
      data
    })
  },
  // 导入下载模板
  template(params) {
    return request({
      url: `${cloud.dqbasic}/area/template`,
      method: 'get',
      params,
      responseType: 'arraybuffer'
    })
  },
  // 导出
  exportExcel(params) {
    return request({
      url: `${cloud.dqbasic}/area/exportExcel`,
      method: 'get',
      params,
      responseType: 'arraybuffer'
    })
  },
  // 导入
  importExcel(data) {
    return request({
      url: `${cloud.dqbasic}/area/importExcel`,
      method: 'post',
      data
    })
  },
  // 获取字典表
  getDictList(params) {
    return request({
      url: `${cloud.dqbasic}/dict/list`,
      method: 'get',
      params
    })
  }
})
