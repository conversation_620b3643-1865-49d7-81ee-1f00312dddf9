import { default as request, cloud } from '@/framework/utils/request'

// 查询列表
export function listprocessMaintain(query) {
  return request({
    url: `${cloud.process}/flow/instance/getPage`,
    method: 'get',
    params: query
  })
}

// 查询详细
export function getprocessMaintain(configId) {
  return request({
    url: `/sysConfig/detail?configId=${configId}`,
    method: 'get'
  })
}

// 终止
export function stopProcessMaintain(data) {
  return request({
    url: `${cloud.process}/flow/instance/end`,
    method: 'post',
    data
  })
}

// 查询流程类型
export function processCategory(query) {
  return request({
    url: `${cloud.process}/flow/flowCategory/list`,
    method: 'get',
    params: query
  })
}
