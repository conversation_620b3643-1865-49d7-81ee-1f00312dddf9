/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-20 16:43:48
 * @LastEditors: gaoyu <EMAIL>
 * @LastEditTime: 2025-04-29 13:29:54
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, { cloud } from '@/framework/utils/request'
import { Switch } from 'element-ui'

// 查询列表
export function getList(query) {
  return request({
    url: `${cloud.dqbasic}/bizSwEntrance/page`,
    method: 'get',
    params: query
  })
}

// 查询详细
export function getDetail(id) {
  return request({
    url: `${cloud.dqbasic}/specialWorkManager/detail?id=${id}`,
    method: 'get'
  })
}

// 新增
export function add(data) {
  return request({
    url: `${cloud.dqbasic}/specialWorkManager/add`,
    method: 'post',
    data
  })
}

// 修改
export function edit(data) {
  return request({
    url: `${cloud.dqbasic}/specialWorkManager/edit`,
    method: 'post',
    data
  })
}

// 删除
export function del(id) {
  return request({
    url: `${cloud.dqbasic}/specialWorkManager/delete`,
    method: 'post',
    data: { ids: id }
  })
}

// 查询列表-新
export function getListNew(query) {
  return request({
    url: `${cloud.dqbasic}/bizSwEntrance/page`,
    method: 'get',
    params: query
  })
}

// 根据作业分类 查询详细
export function getDetailByWorkType(id, workType) {
  let url
  switch (workType) {
    case '动火作业': url = '/bizSwFire/detail'; break
    case '盲板抽堵作业': url = '/bizSwFire/detail'; break
    case '高处作业': url = '/bizSwFire/detail'; break
    case '吊装作业': url = '/bizSwFire/detail'; break
    case '动土作业': url = '/bizSwFire/detail'; break
    case '断路作业': url = '/bizSwOpenCircuit/detail'; break
    case '临时用电作业': url = '/bizSwFire/detail'; break
    case '受限空间作业': url = '/bizSwFire/detail'; break
  }

  return request({
    url: `${cloud.dqbasic}${url}?flag=1&id=${id}`,
    method: 'get'
  })
}

/**
 * @description: 获取断路作业详情
 * @param {*} params
 * @return {*} 获取断路作业详情
 */
export function getBizSwOpenCircuitDetail(params) {
  return request({
    url: `${cloud.dqbasic}/bizSwOpenCircuit/detail`,
    method: 'get',
    params
  })
}
/**
 * @description: 断路作业开始作业
 * @param {*} data
 * @return {*} 断路作业开始作业
 */
export function bizSwOpenCircuitStart(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwOpenCircuit/start/work`,
    method: 'post',
    data
  })
}
/**
 * @description: 断路作业作业完成
 * @param {*} data
 * @return {*} 断路作业作业完成
 */
export function bizSwOpenCircuitSubmit(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwOpenCircuit/submit/check`,
    method: 'post',
    data
  })
}
/**
 * @description: 获取动土作业详情
 * @param {*} params
 * @return {*} 获取动土作业详情
 */
export function getBizSwBreakGroundDetail(params) {
  return request({
    url: `${cloud.dqbasic}/bizSwBreakGround/detail`,
    method: 'get',
    params
  })
}
/**
 * @description: 动土作业开始作业
 * @param {*} data
 * @return {*} 动土作业开始作业
 */
export function bizSwBreakGroundStart(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwBreakGround/start/work`,
    method: 'post',
    data
  })
}
/**
 * @description: 动土作业作业完成
 * @param {*} data
 * @return {*} 动土作业作业完成
 */
export function bizSwBreakGroundSubmit(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwBreakGround/submit/check`,
    method: 'post',
    data
  })
}
/**
 * @description: 获取临时用电作业详情
 * @param {*} data
 * @return {*} 获取临时用电作业详情
 */
export function getBizSwTemporaryPowerDetail(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwTemporaryPower/detail`,
    method: 'post',
    data
  })
}
/**
 * @description: 临时用电作业开始作业
 * @param {*} data
 * @return {*} 临时用电作业开始作业
 */
export function bizSwTemporaryPowerStart(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwTemporaryPower/start/work`,
    method: 'post',
    data
  })
}
/**
 * @description: 临时用电作业作业完成
 * @param {*} data
 * @return {*} 临时用电作业作业完成
 */
export function bizSwTemporaryPowerSubmit(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwTemporaryPower/submit/check`,
    method: 'post',
    data
  })
}
/**
 * @description: 获取吊装作业作业详情
 * @param {*} data
 * @return {*} 获取吊装作业作业详情
 */
export function getBizSwLiftingDetail(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwLifting/detail`,
    method: 'post',
    data
  })
}
/**
 * @description: 吊装作业开始作业
 * @param {*} data
 * @return {*} 吊装作业开始作业
 */
export function bizSwLiftingStart(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwLifting/start/work`,
    method: 'post',
    data
  })
}
/**
 * @description: 吊装作业作业完成
 * @param {*} data
 * @return {*} 吊装作业作业完成
 */
export function bizSwLiftingSubmit(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwLifting/submit/check`,
    method: 'post',
    data
  })
}
/**
 * @description: 获取高处作业作业详情
 * @param {*} data
 * @return {*} 获取高处作业作业详情
 */
export function getBizSwHighPlaceDetail(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwHighPlace/detail`,
    method: 'post',
    data
  })
}
/**
 * @description: 高处作业开始作业
 * @param {*} data
 * @return {*} 高处作业开始作业
 */
export function swHighPlaceStart(data) {
  return request({
    url: `${cloud.dqbasic}/swHighPlace/start/work`,
    method: 'post',
    data
  })
}
/**
 * @description: 高处作业作业完成
 * @param {*} data
 * @return {*} 高处作业作业完成
 */
export function swHighPlaceSubmit(data) {
  return request({
    url: `${cloud.dqbasic}/swHighPlace/submit/check`,
    method: 'post',
    data
  })
}
/**
 * @description: 获取盲板抽堵作业作业详情
 * @param {*} data
 * @return {*} 获取盲板抽堵作业作业详情
 */
export function getBizSwBlindBlockedDetail(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwBlindBlocked/detail`,
    method: 'post',
    data
  })
}
/**
 * @description: 盲板抽堵作业开始作业
 * @param {*} data
 * @return {*} 盲板抽堵作业开始作业
 */
export function bizSwBlindBlockedStart(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwBlindBlocked/start/work`,
    method: 'post',
    data
  })
}
/**
 * @description: 盲板抽堵作业作业完成
 * @param {*} data
 * @return {*} 盲板抽堵作业作业完成
 */
export function bizSwBlindBlockedSubmit(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwBlindBlocked/submit/check`,
    method: 'post',
    data
  })
}
/**
 * @description: 获取受限空间详情
 * @param {*} params
 * @return {*} 获取受限空间详情
 */
export function getBizSwConfinedSpaceDetail(params) {
  return request({
    url: `${cloud.dqbasic}/bizSwConfinedSpace/detail`,
    method: 'get',
    params
  })
}
/**
 * @description: 受限空间作业开始作业
 * @param {*} data
 * @return {*} 受限空间作业开始作业
 */
export function bizSwConfinedSpaceStart(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwConfinedSpace/start/work`,
    method: 'post',
    data
  })
}
/**
 * @description: 受限空间作业作业完成
 * @param {*} data
 * @return {*} 受限空间作业作业完成
 */
export function bizSwConfinedSpaceSubmit(data) {
  return request({
    url: `${cloud.dqbasic}/bizSwConfinedSpace/submit/check`,
    method: 'post',
    data
  })
}
