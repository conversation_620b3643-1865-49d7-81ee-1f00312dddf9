<template>
  <div class="auth-upload-container">
    <el-button
      type="primary"
      @click="handleUploadClick"
    >
      上传文件
    </el-button>

    <input
      ref="inputRef"
      class="inner-upload"
      type="file"
      accept="video/mp4,.mp4,.mov"
      @change="handleFileChange($event)"
    >

    <el-progress
      v-if="showProgress"
      :percentage="uploadProgress"
      :format="formatProgressText"
    />
  </div>
</template>

<script>
import { createUploadVideo, refreshUploadVideo } from '@/api/aliyun-vod'

export default {
  data() {
    return {
      // 请求过期时间（构造参数 timeout, 默认 60000ms）:
      timeout: '',
      // 分片大小（构造参数 partSize, 默认 1048576bit，最小100k）:
      partSize: '',
      // 上传分片数（构造参数 parallel, 默认 5）:
      parallel: '',
      // 网络失败重试次数（构造参数 retryCount, 默认 3）:
      retryCount: '',
      // 网络失败重试间隔（构造参数 retryDuration, 默认 2s）:
      retryDuration: '',
      region: 'cn-shanghai',
      file: null,
      fileName: '',
      uploadProgress: 0,
      uploadDisabled: true,
      resumeDisabled: true,
      pauseDisabled: true,
      uploader: null,
      statusText: '',
      showProgress: false
    }
  },

  computed: {
    userId() {
      return this.$store.getters.userId
    }
  },

  methods: {
    formatProgressText() {
      return `${this.uploadProgress.toFixed(2)}%`
    },

    handleUploadClick() {
      this.$refs.inputRef.click()
    },

    reset() {
      if (!this.uploader) return
      console.log('文件取消上传')

      this.uploadProgress = 0
      this.statusText = ''
      this.showProgress = false

      if (!this.uploader) return
      this.uploader.stopUpload()
      const files = this.uploader.listFiles()
      for (let i = 0; i < files.length; i++) {
        // 取消文件上传
        this.uploader.cancelFile(i)
        // 从文件列表删除
        this.uploader.deleteFile(i)
      }
      // 清空文件列表
      this.uploader.cleanList()
    },

    async handleFileChange(e) {
      this.file = e.target.files[0]
      if (!this.file) {
        this.$message.warning('请选择文件')
        return
      }
      //校验文件类型
      let fileExtension = ''
      if (this.file.name.lastIndexOf('.') > -1) {
        fileExtension = this.file.name.slice(this.file.name.lastIndexOf('.') + 1)
      }
      if (!".mp4,.mov".includes(fileExtension)) {
        this.$dtModal.msgError(`文件格式不正确, 请上传.mp4,.mov格式文件!`)
        return 
      }

      const userData = '{"Vod":{}}'
      this.fileName = this.file.name
      if (this.uploader) {
        this.reset()
        this.uploader.addFile(this.file, null, null, null, userData)
      } else {
        this.uploader = this.createUploader()
      }
      this.uploader.addFile(this.file, null, null, null, userData)
      this.uploadDisabled = false
      this.pauseDisabled = true
      this.resumeDisabled = true
      this.$emit('file-change', this.file)
      this.startUpload()
    },

    startUpload() {
      // 然后调用 startUpload 方法, 开始上传
      if (this.uploader !== null) {
        this.uploader.startUpload()
        this.uploadDisabled = true
        this.pauseDisabled = false
      }
    },

    // 暂停上传
    pauseUpload() {
      if (this.uploader !== null) {
        this.uploader.stopUpload()
        this.resumeDisabled = false
        this.pauseDisabled = true
      }
    },

    // 恢复上传
    resumeUpload() {
      if (this.uploader !== null) {
        this.uploader.startUpload()
        this.resumeDisabled = true
        this.pauseDisabled = false
      }
    },

    createUploader(type) {
      const self = this
      const uploader = new AliyunUpload.Vod({
        timeout: self.timeout || 60000,
        partSize: Math.round(self.partSize || 1048576),
        parallel: self.parallel || 5,
        retryCount: self.retryCount || 3,
        retryDuration: self.retryDuration || 2,
        region: self.region,
        userId: self.userId,
        localCheckpoint: true, // 此参数是禁用服务端缓存，不影响断点续传
        // 添加文件成功
        addFileSuccess(uploadInfo) {
          self.uploadDisabled = false
          self.resumeDisabled = false
          self.statusText = '添加文件成功, 等待上传...'
          console.log('files', self.uploader.listFiles())
          console.log(`addFileSuccess: ${uploadInfo.file.name}`)
          console.log('uploadInfo', uploadInfo)
        },

        // 开始上传
        onUploadstarted(uploadInfo) {
          console.log('开始上传', uploadInfo)
          self.$emit('upload-start')
          self.showProgress = true
          if (!uploadInfo.videoId) {
            createUploadVideo({
              fileName: self.fileName,
              title: self.fileName.split('.')[0]
            })
              .then(({ data: { body } }) => {
                console.log('获取凭证', body)
                const uploadAuth = body.uploadAuth
                const uploadAddress = body.uploadAddress
                const videoId = body.videoId
                uploader.setUploadAuthAndAddress(uploadInfo, uploadAuth, uploadAddress, videoId)
              })
          } else {
            refreshUploadVideo(uploadInfo.videoId)
              .then(({ data: { body } }) => {
                console.log('刷新凭证', body)
                const uploadAuth = body.uploadAuth
                const uploadAddress = body.uploadAddress
                const videoId = body.videoId
                uploader.setUploadAuthAndAddress(uploadInfo, uploadAuth, uploadAddress, videoId)
              })
          }
        },

        onUploadSucceed(uploadInfo) {
          console.log(`onUploadSucceed: ${uploadInfo.file.name}, endpoint:${uploadInfo.endpoint}, bucket:${uploadInfo.bucket}, object:${uploadInfo.object}`)
          self.$emit('upload-succeed', uploadInfo)
        },

        onUploadFailed(uploadInfo, code, message) {
          self.$emit('upload-failed', uploadInfo)
          console.log(`onUploadFailed: file:${uploadInfo.file.name},code:${code}, message:${message}`)
        },

        onUploadCanceled(uploadInfo, code, message) {
          console.log('upload-canceled', uploadInfo)
          console.log(`Canceled file: ${uploadInfo.file.name}, code: ${code}, message:${message}`)
        },

        // 文件上传进度，单位：字节, 可以在这个函数中拿到上传进度并显示在页面上
        onUploadProgress(uploadInfo, totalSize, progress) {
          console.log(`onUploadProgress:file:${uploadInfo.file.name}, fileSize:${totalSize}, percent:${Math.ceil(progress * 100)}%`)
          self.uploadProgress = progress * 100
          self.statusText = '文件上传中...'
          self.$emit('progress-update', progress)
        },

        // 上传凭证超时
        onUploadTokenExpired(uploadInfo) {
          refreshUploadVideo(uploadInfo.videoId)
            .then(({ data: { body } }) => {
              const uploadAuth = body.uploadAuth
              uploader.resumeUploadWithAuth(uploadAuth)
              console.log(`upload expired and resume upload with uploadauth ${uploadAuth}`)
            })
          self.statusText = '文件超时...'
        },

        // 全部文件上传结束
        onUploadEnd(uploadInfo) {
          console.log('onUploadEnd: uploaded all the files')
          self.statusText = '文件上传完毕'
        }
      })
      return uploader
    }
  }
}
</script>

<style lang="scss" scoped>
.auth-upload-container {
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;
  align-items: center;
  gap: 20px;

  .el-progress {
    flex: 1 1 auto;

    ::v-deep .el-progress-bar {
      padding-right: 65px;
      margin-right: -65px;
    }
  }

  .inner-upload {
    display: none;
  }

}
</style>

