import { default as request, cloud } from '@/framework/utils/request'

const getUserCard = (params) => request({ url: `${cloud.manage}/sysCustomWorkbench/detail`, method: 'get', params })
const getUserCardsetDefault = (params) => request({ url: `${cloud.manage}/sysCustomWorkbench/setDefault`, method: 'post', data: params })
const getUserCardedit = (params) => request({ url: `${cloud.manage}/sysCustomWorkbench/edit`, method: 'post', data: params })
export {
  getUserCard, getUserCardedit, getUserCardsetDefault
}
