<template>
  <div class="app-container">
    <div class="filter-container">
    </div>
    <div class="mainbox">
      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <el-form
            ref="form" :model="form" :rules="rules"
            label-width="80px">
            <el-row>
              <el-form-item label="积分规则" prop="contract">
                <editor v-model="form.contract" :min-height="192"/>
                <DtTinymce2 ref="editor" v-model="form.contract" :height="600" @input="handleEditorChange"
                           :width="'100%'"/>
              </el-form-item>
            </el-row>
            <el-row style="text-align: center;">
              <el-button type="primary" @click="submitForm">保存</el-button>
            </el-row>
          </el-form>

        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  listIntegrationRule,
  getIntegrationRule,
  addIntegrationRule,
} from '@/api/training-examination/integration/IntegrationRule';
import DtTinymce2 from './dt-tinymce2/index.vue'

export default {
  name: 'IntegrationRule',
  components: {
    DtTinymce2
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      //显隐表格
      isShowTable: true,
      // 选中数组
      ids: [],
      //列显隐数组
      showColumns: [
        {prop: "contract", label: "内容", show: true},
      ],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 积分规则表格数据
      IntegrationRuleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {
        contract: ''
      },
      // 表单校验
      rules: {
        contract: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入积分规则'
          }
        ]
      }
    };
  },
  computed: {
    //详情参数
    detailOption() {
      return {
        rows: [
          {
            label: '内容',
            prop: 'contract'

          },
        ],
        data: {}
      }
    },
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询积分规则列表 */
    getList() {
      this.loading = true;
      getIntegrationRule().then(res => {
        this.form = res.data
        this.form.id = null
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.reset();
    },
    handleEditorChange(val){
      this.form.contract = val
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.contract  = this.form.contract.replace('<img src="prod-api', '<img src="/prod-api')
          addIntegrationRule(this.form).then(response => {
            this.$dtModal.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
  }
};
</script>
