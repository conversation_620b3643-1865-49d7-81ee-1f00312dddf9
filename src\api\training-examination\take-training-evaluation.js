import request, { cloud } from '@/framework/utils/request'

// 获取培训评价试卷
export function getEvaluationQuestions(query) {
  return request({
    url: `${cloud.dqbasic}/application/trainingBaseInfo/getEvaluationQuestions`,
    method: 'get',
    params: query
  })
}

// 获取保存培训评价详情
export function getTrainingRememberDetailByTrainingId(query) {
  return request({
    url: `${cloud.dqbasic}/application/trainingRemember/detailByTrainingId`,
    method: 'get',
    params: query
  })
}

// 保存/提交培训评价
export function trainingRememberSave(data) {
  return request({
    url: `${cloud.dqbasic}/application/trainingRemember/edit`,
    method: 'post',
    data
  })
}

