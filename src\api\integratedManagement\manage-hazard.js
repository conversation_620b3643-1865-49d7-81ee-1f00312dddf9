/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-25 16:04:27
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-25 16:40:11
 * @Description: 两重点一重大
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */

import request from '@/framework/utils/request'

// 查询两重点一重大列表
export function listManageHazard(query) {
  return request({
    url: '/project/safetyManageHazard/page',
    method: 'get',
    params: query
  })
}

// 查询两重点一重大详细
export function getManageHazard(id) {
  return request({
    url: '/project/safetyManageHazard/detail?id=' + id,
    method: 'get'
  })
}

// 新增两重点一重大
export function addManageHazard(data) {
  return request({
    url: '/project/safetyManageHazard/add',
    method: 'post',
    data: data
  })
}

// 修改两重点一重大
export function updateManageHazard(data) {
  return request({
    url: '/project/safetyManageHazard/edit',
    method: 'post',
    data: data
  })
}

// 删除两重点一重大
export function delManageHazard(id) {
  return request({
    url: '/project/safetyManageHazard/delete',
    method: 'post',
    data: { ids: id }
  })
}
