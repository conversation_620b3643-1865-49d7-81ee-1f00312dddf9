import {
  default as request,
  cloud
} from '@/framework/utils/request'
  
export default ({
  // 园区规划
  getPDf() {
    return request({
      url: `${cloud.dqbasic}/customFormData/getList/1745253266270367746?pageNo=1&pageSize=10&formId=1745253266270367746&configPagination=1`,
      method: 'get'
    })
  },
  
  commitfile(data) {
    return request({
      url: `${cloud.dqbasic}/customFormData/add/1745253266270367746`,
      method: 'post',
      data
    })
  },
  // 园区管理架构
  getImg() {
    return request({
      url: `${cloud.dqbasic}/designerFormData/getList/1745360825694203906?pageNo=1&pageSize=10&formId=1745360825694203906&configPagination=1`,
      method: 'get'
    })
  },
  commitImg(data) {
    return request({
      url: `${cloud.dqbasic}/customFormData/add/1745360825694203906`,
      method: 'post',
      data
    })
  },
  // 园区简介
  getintroduceInfo() {
    return request({
      url: `${cloud.dqbasic}/designerFormData/getList?pageNo=1&pageSize=10&configPagination=1`,
      method: 'get'
    })
  },
  commit(data) {
    return request({
      url: `${cloud.dqbasic}/customFormData/edit/1745246058015010818`,
      method: 'post',
      data
    })
  },
  // 一企一档接口
  // 详情
  detail(params) {
    return request({
      url: `${cloud.dqbasic}/safetyManage/enterprise/detail`,
      method: 'get',
      params
    })
  },
  menulnfo(params) {
    return request({
      url: `${cloud.dqbasic}/safetyManage/link/menuInfo`,
      method: 'get',
      params
    })
  },
  // 开停车和检修统计分析
  // 装置危险源类型统计
  deviceHazardType(params) {
    return request({
      url: `${cloud.dqbasic}/safetyManage/stat/deviceHazardType`,
      method: 'get',
      params
    })
  },
  // 进行中开停车和大检修统计
  progressingOverhaul(params) {
    return request({
      url: `${cloud.dqbasic}/safetyManage/stat/progressingOverhaul`,
      method: 'get',
      params
    })
  },
  // 开停车趋势统计
  device(params) {
    return request({
      url: `${cloud.dqbasic}/safetyManage/stat/device`,
      method: 'get',
      params
    })
  },
  // 大检修趋势统计
  overhaul(params) {
    return request({
      url: `${cloud.dqbasic}/safetyManage/stat/overhaul`,
      method: 'get',
      params
    })
  }
})
  
// 公共管廊页面接口
// 管廊选择下拉选
export function list(params) {
  return request({
    url: `${cloud.dqbasic}/pipeGallery/list`,
    method: 'get',
    params
  })
}
// 视频监控事件列表及详情
export function getVideoAlterList(hazardId) {
  return request({
    url: `${cloud.dqbasic}/videoAlter/list`,
    method: 'get',
    params: { hazardId }
  })
}
// 3.预警处理接口 /hazard/warningDeal，post请求
export function dealWarn(data) {
  return request({
    url: `${cloud.dqbasic}/pipeScreen/dealWarn`,
    method: 'post',
    data
  })
}
  
  