import { default as request, cloud } from '@/framework/utils/request'

export function login(data) {
  return request({
    url: `${cloud.auth}/loginApi`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function getInfo(token) {
  return request({
    url: `${cloud.auth}/getCurrentLoginUserInfo/V2`,
    method: 'get',
    params: { token }
  })
}

export function getUserInfo() {
  return request({
    url: `${cloud.dqbasic}/sysUser/currentUserInfo`,
    method: 'get'
  })
}

export function logout() {
  return request({
    url: `${cloud.auth}/logoutAction`,
    method: 'post'
  })
}

export function getAuthentication() {
  return request({
    url: `${cloud.auth}/authentication/info`,
    method: 'get'
  })
}

// 图片校验
export function getcaptch() {
  return request({
    url: `${cloud.auth}/captcha`,
    method: 'get'
  })
}

// 验证邮箱是否正确
export function tovalidatorEmai(params) {
  return request({
    url: `${cloud.auth}/sysUser/validatorEmail`,
    method: 'post',
    data: params
  })
}

// 找回密码-验证邮箱通过-发送邮箱验证码
export function getsendMailAuthCode(params) {
  return request({
    url: `${cloud.auth}/sysUser/sendMailAuthCode`,
    method: 'get',
    params
  })
}

// 找回密码-邮箱验证码校验 验证码重置密码合一接口
export function validatorEmailAuthCode(params) {
  return request({
    url: `${cloud.auth}/sysUser/validatorEmailAuthCode`,
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 重置密码
export function resetPwdEmail(params) {
  return request({
    url: `${cloud.auth}/sysUser/resetPwdEmail`,
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
// 单点账号密码登录
export function loginApi(data) {
  return request({
    url: `${cloud.auth}/sso/loginApi`,
    method: 'post',
    data
  })
}
export function loginWithToken(data) {
  return request({
    url: `${cloud.auth}/sso/loginWithToken`,
    method: 'post',
    data
  })
}
// 单点短信登录
export function loginSms(data) {
  return request({
    url: `${cloud.auth}/sso/loginSms`,
    method: 'post',
    data
  })
}
// 发送短信验证码
export function loginSendSms(data) {
  return request({
    url: `${cloud.usercenter}/sysUser/sendMailCode`,
    method: 'post',
    data
  })
}
// 因子2 发送短信验证码
export function twoAuthWaySendLoginSms(data) {
  return request({
    url: '/loginSms/twoAuthWaySendLoginSms',
    method: 'post',
    data
  })
}
// 短信验证码登录
export function loginBySms(data) {
  return request({
    url: `${cloud.auth}/loginBySms`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
// 账号密码登录
export function loginByPwd(data) {
  return request({
    url: `${cloud.auth}/loginApi`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
// 获取滑块验证码配置
export function getSliderKaptcha(data) {
  return request({
    url: `${cloud.auth}/kaptcha/getSliderKaptcha`,
    method: 'get'
  })
}
// 校验滑块验证码是否通过
export function checkSliderKaptcha(data) {
  return request({
    url: `${cloud.auth}/kaptcha/checkSliderKaptcha`,
    method: 'post',
    data
  })
}

// 滑块成功后点击下一步调用 发送验证码接口
export function sendMailCode(params) {
  return request({
    url: `${cloud.auth}/sysUser/sendMailCode`,
    method: 'POST',
    data: params
  })
}
// 获取认证规则配置
export function getAuthRules(data) {
  return request({
    url: `${cloud.auth}/authRules/getAuthRules`,
    method: 'get',
    params: data
  })
}
// 修改认证规则配置
export function updateAuthRules(data) {
  return request({
    url: `${cloud.auth}/authRules/updateAuthRules`,
    method: 'post',
    data
  })
}

// 获取租户选择先拉框的接口
export function getTenantDropDownList(data) {
  return request({
    url: `${cloud.tenant}/sysTenant/getTenantDropDownList`,
    method: 'get'
  })
}

// 首次登录强制修改密码
export function updatePwdNoToken(data) {
  return request({
    url: `${cloud.usercenter}/sysUser/updatePwdNoToken`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

