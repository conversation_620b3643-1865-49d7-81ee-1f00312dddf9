import {
  default as request,
  cloud
} from '@/framework/utils/request'

export default ({
  // 值班值守数据列表
  onGuardInfo() {
    return request({
      url: `${cloud.dqbasic}/safetyManage/screen/onGuardInfo`,
      method: 'get'
    })
  },
  // 两重点一重大饼图统计
  safetyManageHazard() {
    return request({
      url: `${cloud.dqbasic}/safetyManage/screen/safetyManageHazard`,
      method: 'get'
    })
  },
  // 风险分级管控饼图统计
  hazardLevel() {
    return request({
      url: `${cloud.dqbasic}/safetyManage/screen/hazardLevel`,
      method: 'get'
    })
  },
  // 隐患排查统计
  hiddenDanger() {
    return request({
      url: `${cloud.dqbasic}/safetyManage/screen/hiddenDanger`,
      method: 'get'
    })
  },
  // 特殊作业数据列表
  specialWork() {
    return request({
      url: `${cloud.dqbasic}/safetyManage/screen/specialWork`,
      method: 'get'
    })
  },
  // 封闭化管理数据列表
  isolatingManage() {
    return request({
      url: `${cloud.dqbasic}/safetyManage/screen/isolatingManage`,
      method: 'get'
    })
  },
  // 装置大停车和检修数据列表
  deviceOverhaul() {
    return request({
      url: `${cloud.dqbasic}/safetyManage/screen/deviceOverhaul`,
      method: 'get'
    })
  },
  // 企业管理——企业名称列表
  enterpriseList() {
    return request({
      url: `${cloud.dqbasic}/safetyManage/screen/enterpriseList`,
      method: 'get'
    })
  },
  // 企业管理——企业基础信息查询
  enterpriseInfo(id) {
    return request({
      url: `${cloud.dqbasic}/safetyManage/screen/enterpriseInfo`,
      method: 'get',
      params: {
        orgId: id
      }
    })
  },

  // 综合管理大屏——隐患排查-数据交互
  hdInteractive(params) {
    return request({
      url: `${cloud.dqbasic}/safetyManage/screen/hdInteractive`,
      method: 'get',
      params
    })
  },

  // 综合管理大屏——两重点一重大-重大危险源 数据交互
  mhInteractive(params) {
    return request({
      url: `${cloud.dqbasic}/safetyManage/screen/mhInteractive`,
      method: 'get',
      params
    })
  }
})
