
import request from '@/framework/utils/request'
// 获取机器码的接口：/authentication/getMachineCode，get请求
export function getMachineCode(query) {
  return request({
    url: '/authentication/getMachineCode',
    method: 'get',
    params: query
  })
}
// 鉴权接口：/authentication/info，post请求，传参：file
export function authenticationInfo(data) {
  return request({
    url: '/authentication/info',
    method: 'post',
    data
  })
}
