/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-23 10:24:41
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-24 14:36:46
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'

// 新增应急预案数据
export function listEmergSeniorPlan(query) {
  return request({
    url: '/project/emergSeniorPlan/getListPage',
    method: 'get',
    params: query
  })
}

// 查询应急预案详细
export function getEmergSeniorPlan(id) {
  return request({
    url: '/project/emergSeniorPlan/detail?id=' + id,
    method: 'get'
  })
}

// 新增应急预案
export function addEmergSeniorPlan(data) {
  return request({
    url: '/project/emergSeniorPlan/add',
    method: 'post',
    data: data
  })
}

// 修改应急预案
export function updateEmergSeniorPlan(data) {
  return request({
    url: '/project/emergSeniorPlan/edit',
    method: 'post',
    data: data
  })
}
// 新增评估记录数据
export function addEvaluate(data) {
  return request({
    url: '/project/evaluate/add',
    method: 'post',
    data: data
  })
}
// 分页查询评估记录所有数据
export function getEvaluateList(query) {
  return request({
    url: '/project/evaluate/page',
    method: 'get',
    params: query
  })
}
// 新增应急预案修订记录数据
export function addPlanRevise(data) {
  return request({
    url: '/project/emergPlanRevise/add',
    method: 'post',
    data: data
  })
}
// 分页查询应急预案修订记录所有数据
export function getPlanReviseList(query) {
  return request({
    url: '/project/emergPlanRevise/page',
    method: 'get',
    params: query
  })
}
// 删除应急预案
export function delemEmergSeniorPlan(id) {
  return request({  
    url: '/project/emergSeniorPlan/delete',
    method: 'post',
    data: { ids: id }
  })
}
