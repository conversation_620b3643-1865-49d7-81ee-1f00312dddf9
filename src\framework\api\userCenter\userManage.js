import request from '@/framework/utils/request'

// 查询用户组织机构关联列表
export function listUserOrg(params) {
  return request({
    url: '/sysUser/page',
    method: 'get',
    params
  })
}

// 查询用户组织机构关联详细
export function getUserOrg(userOrgId) {
  return request({
    url: `/sysUserOrg/detail?userOrgId=${userOrgId}`,
    method: 'get'
  })
}

// 新增用户组织机构关联
export function addUserOrg(data) {
  return request({
    url: '/sysUserOrg/add',
    method: 'post',
    data
  })
}

// 修改用户组织机构关联
export function updateUserOrg(data) {
  return request({
    url: '/sysUserOrg/edit',
    method: 'post',
    data
  })
}

// 删除用户组织机构关联
export function delUserOrg(userOrgId) {
  return request({
    url: '/sysUserOrg/delete',
    method: 'post',
    data: { userOrgId }
  })
}
export function exportUser(params) {
  return request({
    url: '/sysUser/exportUser',
    method: 'post',
    // params,
    params,
    responseType: 'arraybuffer'
  })
}

export function exportAllUser(params) {
  return request({
    url: '/sysUser/exportAllUser',
    method: 'post',
    params,
    responseType: 'arraybuffer'
  })
}

export function getOrgListTreeNode(params) {
  return request({
    url: '/hrOrganization/roleBindOrgScopeLazyAntdv',
    method: 'get',
    params
  })
}
export function resetPwd(data) {
  return request({
    url: '/sysUser/resetPwd',
    method: 'post',
    data
  })
}

export function changeStatus(data) {
  return request({
    url: '/sysUser/changeStatus',
    method: 'POST',
    data
  })
}

export function edit(data) {
  return request({
    url: '/sysUser/edit',
    method: 'POST',
    data
  })
}
export function add(data) {
  return request({
    url: '/sysUser/add',
    method: 'POST',
    data
  })
}

export function fetchList(params) {
  return request({
    url: '/hrPosition/list',
    method: 'get',
    params
  })
}
