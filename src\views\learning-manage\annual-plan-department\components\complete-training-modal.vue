<template>
  <el-dialog
    title="完成培训计划"
    v-bind="$attrs"
    :close-on-click-modal="false"
    v-on="$listeners"
    @closed="handleClosed"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      v-loading="isDetailLoading || isOptionLoading"
      :model="formData"
      :rules="formRules"
      label-position="right"
      label-width="9em"
    >
      <el-form-item label="培训名称" prop="baseInfoId">
        <span v-if="mode === 'view'">
          {{ formData.realTrainName }}
        </span>

        <el-select v-else v-model="formData.baseInfoId" @change="handleTrainingChange">
          <el-option
            v-for="item in trainingOptions"
            :key="item.id"
            :label="item.trainName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="实际培训时间" prop="realTrainTime">
        <span v-if="mode === 'view'">
          {{ formData.realBeginTime }} - {{ formData.realEndTime }}
        </span>

        <el-date-picker
          v-else
          v-model="actualTrainingTime"
          disabled
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
        />
      </el-form-item>

      <el-form-item label="实际培训人数" prop="totalStuff">
        <span v-if="mode === 'view'">
          {{ formData.totalStuff }}
        </span>

        <el-input-number
          v-else
          disabled
          :value="formData.totalStuff"
          :controls="false"
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">
        取消
      </el-button>

      <el-button
        v-if="mode === 'edit'"
        type="primary"
        :loading="isSubmitLoading"
        @click="handleSubmit"
      >
        提交
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateCompletedStatus, getCompletedPlanList, getRealPlanById } from '@/api/learning-manage/annual-plan'

export default {
  name: 'CompleteTrainingModal',
  props: {
    mode: {
      required: true,
      type: String,
      validator: (val) => ['edit', 'view'].includes(val)
    },

    record: {
      required: true,
      type: Object,
      default: () => ({})
    }
  },

  data() {
    const realTrainTimeValidator = (rule, value, callback) => {
      if (this.formData.realBeginTime && this.formData.realEndTime) {
        callback()
      } else {
        callback(new Error('请选择实际培训时间'))
      }
    }
    return {
      trainingOptions: [
      ],

      isDetailLoading: false,
      isOptionLoading: false,
      isSubmitLoading: false,

      formData: {
        id: '',
        realTrainName: '',
        trainName: '',
        trainMonth: '',
        baseInfoId: '',
        realBeginTime: '',
        realEndTime: '',
        totalStuff: '',
        planYear: '',
        completeStatus: 1
      },

      formRules: {
        baseInfoId: [
          { required: true, message: '请选择培训名称' }
        ],

        trainingName: [
          { required: true, message: '请选择培训名称' }
        ],

        realTrainTime: [
          { required: true, message: '请选择实际培训时间', validator: realTrainTimeValidator }
        ],

        totalStuff: [
          { required: true, message: '请输入实际培训人数', validator: (rule, value, callback) => callback() }
        ]
      }
    }
  },

  computed: {
    actualTrainingTime: {
      set(value) {
        if (value && value.length === 2) {
          this.formData.realBeginTime = value[0]
          this.formData.realEndTime = value[1]
        } else {
          this.formData.realBeginTime = ''
          this.formData.realEndTime = ''
        }
      },

      get() {
        if (this.formData.realBeginTime && this.formData.realEndTime) {
          return [this.formData.realBeginTime, this.formData.realEndTime]
        } else {
          return []
        }
      }
    }
  },

  watch: {
    record() {
      this.handlePropsChange()
    },

    mode() {
      this.handlePropsChange()
    }
  },

  methods: {
    // getTrainingName(id) {
    //   const target = this.trainingOptions.find((item) => item.id === id)
    //   return target ? target.trainName : ''
    // },

    handleOpen() {
      this.getTrainingOptions()
      if (this.mode === 'view') {
        this.getDetail()
      }
    },

    getDetail() {
      this.isDetailLoading = true
      getRealPlanById(this.record.id)
        .then((res) => {
          this.formData = res.data
        })
        .finally(() => {
          this.isDetailLoading = false
        })
    },

    getTrainingOptions() {
      this.isOptionLoading = true
      getCompletedPlanList()
        .then((res) => {
          this.trainingOptions = res.data
        })
        .finally(() => {
          this.isOptionLoading = false
        })
    },

    reset() {
      this.formData = {
        id: '',
        trainMonth: '',
        baseInfoId: '',
        realBeginTime: '',
        realEndTime: '',
        totalStuff: '',
        completeStatus: 1
      }
      this.$nextTick().then(() => {
        this.$refs.formRef.clearValidate()
      })
    },

    handleClosed() {
      this.reset()
    },

    handleTrainingChange(value) {
      console.log('value', value)
      const trainingItem = this.trainingOptions.find((item) => item.id === value)
      if (!trainingItem) {
        this.$message.error('培训不存在')
        return
      }

      this.formData = Object.assign({}, this.formData, {
        realBeginTime: trainingItem.realBeginTime,
        realEndTime: trainingItem.realEndTime,
        totalStuff: trainingItem.totalStuff || 0,
        baseInfoId: trainingItem.id
      })
    },

    handlePropsChange() {
      this.formData.id = this.record.id
      this.formData.trainMonth = this.record.trainMonth
      this.formData.planYear = this.record.planYear
    },

    handleSubmit() {
      this.isSubmitLoading = true
      this.$refs.formRef.validate()
        .then(() => updateCompletedStatus(this.formData))
        .then(() => {
          this.$message.success('提交成功')
          this.$emit('update:visible', false)
          this.$emit('refresh')
        })
        .catch((err) => {
          console.log(err)
          if (!(err instanceof Error)) return
          this.$message.error('提交失败')
        })
        .finally(() => {
          this.isSubmitLoading = false
        })
    },

    handleCancel() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
