/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-11 11:22:59
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-08-26 09:41:09
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'

// 查询审批预约管理列表
export function listCarAppointment(query) {
  return request({
    url: '/project/hazardCarAppointment/page',
    method: 'get',
    params: query
  })
}

// 查询审批预约管理详细
export function getCarAppointment(id) {
  return request({
    url: '/project/hazardCarAppointment/detail?id=' + id,
    method: 'get'
  })
}
// 根据车牌号查询已有记录中该车牌号对应的危化品载运物料类型
export function getHazardousTypeByCarNumber(query) {
  return request({
    url: '/project/hazardCarAppointment/getHazardousTypeByCarNumber',
    method: 'get',
    params: query
  })
}

// 新增审批预约管理
export function addCarAppointment(data) {
  return request({
    url: '/project/hazardCarAppointment/add',
    method: 'post',
    data: data
  })
}

// 修改审批预约管理
export function updateCarAppointment(data) {
  return request({
    url: '/project/hazardCarAppointment/edit',
    method: 'post',
    data: data
  })
}

// 删除审批预约管理
export function delCarAppointment(id) {
  return request({
    url: '/project/hazardCarAppointment/delete',
    method: 'post',
    data: { ids: id }
  })
}
