import request, {cloud} from '@/framework/utils/request'

// 查询积分规则列表
export function listIntegrationRule(query) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationRule/page',
    method: 'get',
    params: query
  })
}

// 查询积分规则详细
export function getIntegrationRule() {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationRule/detail',
    method: 'get'
  })
}

// 新增积分规则
export function addIntegrationRule(data) {
  return request({
    url: cloud.dqbasic + '/trainingIntegrationRule/add',
    method: 'post',
    data: data
  })
}
