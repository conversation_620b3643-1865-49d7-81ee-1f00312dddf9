/*
 * @Author: miteng <EMAIL>
 * @Date: 2024-05-15 16:13:32
 * @LastEditors: miteng <EMAIL>
 * @LastEditTime: 2024-05-20 09:05:50
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'

// 查询业务--检查点管理列表
export function listCheckPointManager(query) {
  return request({
    url: '/project/bizCheckPointManager/page',
    method: 'get',
    params: query
  })
}
// 查询所在部位列表
export function listRiskExternal(query) {
  return request({
    url: '/project/bizRiskExternal/page',
    method: 'get',
    params: query
  })
}
// 查询作业场所列表 
export function listWorkPlace(data) {
  return request({
    url: '/project/bizWorkPlace/selectList',
    method: 'post',
    data: data
  })
}

// 查询业务-检查点管理详细
export function getCheckPointManager(id) {
  return request({
    url: '/project/bizCheckPointManager/detail?id=' + id,
    method: 'get'
  })
}
// 查询业务-获取业务-检查点管理多选的详情 ids
export function getCheckPointManagers(ids) {
  return request({
    url: '/project/bizCheckPointManager/detailByIds?ids=' + ids,
    method: 'get'
  })
}
// 新增业务-检查点管理
export function addCheckPointManager(data) {
  return request({
    url: '/project/bizCheckPointManager/add',
    method: 'post',
    data: data
  })
}

// 修改业务-检查点管理
export function updateCheckPointManager(data) {
  return request({
    url: '/project/bizCheckPointManager/edit',
    method: 'post',
    data: data
  })
}

// 删除业务-检查点管理
export function delCheckPointManager(id) {
  return request({
    url: '/project/bizCheckPointManager/delete',
    method: 'post',
    data: { ids: id }
  })
}
// 删除业务-检查点类目数据 （删除模版中每条数据）
export function delCheckPointItem(id) {
  return request({
    url: '/project/bizCheckPointItem/delete',
    method: 'post',
    data: { ids: id }
  })
}