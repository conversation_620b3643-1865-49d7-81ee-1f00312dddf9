<template>
  <el-dialog
    width="500px"
    v-bind="$attrs"
    :close-on-click-modal="false"
    v-on="$listeners"
    @open="handleOpen"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="formData"
    >
      <el-form-item :rules="rules" prop="content">
        <span v-if="mode === 'view'">
          {{ defaultValue }}
        </span>

        <el-input
          v-else
          v-model.trim="formData.content"
          :autosize="{
            minRows: 5,
          }"
          :maxlength="500"
          show-word-limit
          type="textarea"
          placeholder="请输入"
        />
      </el-form-item>
    </el-form>

    <div slot="footer">
      <el-button
        v-if="mode === 'edit'"
        type="primary"
        :loading="submitLoading"
        @click="handleSubmit"
      >
        提交
      </el-button>

      <el-button @click="$emit('update:visible', false)">
        取消
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ConfirmModal',
  inheritAttrs: false,
  props: {
    rules: {
      type: [Array, Object],
      default: () => ({ required: false })
    },

    mode: {
      type: String,
      validator(val) {
        return ['edit', 'view'].includes(val)
      },

      default: 'edit'
    },

    defaultValue: {
      type: String,
      default: ''
    },

    submitLoading: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      isLoading: false,
      formData: {
        content: ''
      }
    }
  },

  methods: {
    reset() {
      this.$refs.formRef.resetFields()
    },

    handleOpen() {
      this.formData.content = this.defaultValue
    },

    handleClosed() {
      this.reset()
    },

    handleSubmit() {
      this.$refs.formRef.validateField('content', (error) => {
        if (error) return
        this.$emit('submit', this.formData.content)
      })
    }
  }
}
</script>
