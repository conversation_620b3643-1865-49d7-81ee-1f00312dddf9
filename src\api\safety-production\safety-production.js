
/*
 * @Author: du<PERSON><PERSON> <EMAIL>
 * @Date: 2024-07-01 08:59:03
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-07-23 11:44:47
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud}  from '@/framework/utils/request'

// 查询列表
export function getResumptionPlanList(query) {
  return request({
    url: cloud.dqbasic + '/bizResumptionPlan/page',
    method: 'get',
    params: query
  })
}

// 查询履职计划详细
export function getDetail(id) {
  return request({
    url: cloud.dqbasic + '/bizResumptionPlan/detail?id=' + id,
    method: 'get'
  })
}

// 履职计划新增
export function add(data) {
  return request({
    url: cloud.dqbasic + '/bizResumptionPlan/add',
    method: 'post',
    data: data
  })
}

// 履职计划修改
export function edit(data) {
  return request({
    url: cloud.dqbasic + '/bizResumptionPlan/edit',
    method: 'post',
    data: data
  })
}

// 履职计划列表删除
export function del(id) {
  return request({
    url: cloud.dqbasic + '/bizResumptionPlan/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 计划周期刷新判断
export function judgeTimeCollect(data) {
  return request({
    url: cloud.dqbasic + '/bizInspectionTask/judgeTimeCollect',
    method: 'post',
    data: data
  })
}


// 查询履职任务列表
export function getResumptionTaskList(query) {
  return request({
    url: cloud.dqbasic + '/bizResumptionTask/page',
    method: 'get',
    params: query
  })
}

// 查询履职任务详细
export function getTaskDetail(id) {
  return request({
    url: cloud.dqbasic + '/bizResumptionTask/detail?id=' + id,
    method: 'get'
  })
}

// 履职任务执行
export function taskEdit(data) {
  return request({
    url: cloud.dqbasic + '/bizResumptionTask/edit',
    method: 'post',
    data: data
  })
}




