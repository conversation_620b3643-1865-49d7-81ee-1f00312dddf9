/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-07-15 16:38:27
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-07-15 16:47:46
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud} from '@/framework/utils/request'

// 查询三同时列表
export function listThreeSimultaneity(query) {
  return request({
    url: cloud.dqbasic + '/threeSimultaneity/page',
    method: 'get',
    params: query
  })
}

// 查询三同时详细
export function getThreeSimultaneity(id) {
  return request({
    url: cloud.dqbasic + '/threeSimultaneity/detail?id=' + id,
    method: 'get'
  })
}

// 新增三同时
export function addThreeSimultaneity(data) {
  return request({
    url: cloud.dqbasic + '/threeSimultaneity/add',
    method: 'post',
    data: data
  })
}

// 修改三同时
export function updateThreeSimultaneity(data) {
  return request({
    url: cloud.dqbasic + '/threeSimultaneity/edit',
    method: 'post',
    data: data
  })
}

// 删除三同时
export function delThreeSimultaneity(id) {
  return request({
    url: cloud.dqbasic + '/threeSimultaneity/delete',
    method: 'post',
    data: { ids: id }
  })
}
