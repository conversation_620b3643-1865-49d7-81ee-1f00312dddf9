import { default as request, cloud } from '@/framework/utils/request'

export default ({

  // 获取会议列表
  fetchList(params) {
    return request({
      url: `${cloud.dqbasic}/meeting/page`,
      method: 'get',
      params
    })
  },

  // 添加会议
  addOrUpdateMeeting(data) {
    return request({
      url: `${cloud.dqbasic}/meeting/addOrUpdate`,
      method: 'post',
      data
    })
  },

  // id获取会议数据
  getMeetingById(id) {
    return request({
      url: `${cloud.dqbasic}/meeting/detail`,
      method: 'get',
      params: {
        id
      }
    })
  },

  // 删除会议
  deleteMeeting(id) {
    return request({
      url: `${cloud.dqbasic}/meeting/delete`,
      method: 'post',
      data: id
    })
  },

  // 回复通知
  reply(data) {
    return request({
      url: `${cloud.dqbasic}/meeting/reply`,
      method: 'post',
      data
    })
  },

  // 获取会议主题列表
  fetchListMeetingTopic(params) {
    return request({
      url: `${cloud.dqbasic}/meetingTopic/page`,
      method: 'get',
      params
    })
  },

  // 添加会议主题
  addMeetingTopic(data) {
    return request({
      url: `${cloud.dqbasic}/meetingTopic/add`,
      method: 'post',
      data
    })
  },

  // 会议类型获取会议主题数据
  getMeetingTopicByType(type) {
    return request({
      url: `${cloud.dqbasic}/meetingTopic/list`,
      method: 'get',
      params: {
        meetingType: type,
        status: 1
      }
    })
  },

  // id获取会议主题数据
  getMeetingTopicById(id) {
    return request({
      url: `${cloud.dqbasic}/meetingTopic/detail`,
      method: 'get',
      params: {
        id
      }
    })
  },

  // 修改会议主题
  updateMeetingTopic(data) {
    return request({
      url: `${cloud.dqbasic}/meetingTopic/edit`,
      method: 'post',
      data
    })
  },

  // 删除会议主题
  deleteMeetingTopic(id) {
    return request({
      url: `${cloud.dqbasic}/meetingTopic/delete`,
      method: 'post',
      data: id
    })
  },

  // 获取组织树
  getOrgTree() {
    return request({
      url: `${cloud.dqbasic}/hrOrganization/currentTree`,
      method: 'get'
    })
  },

  // 获取党员
  getPartyMember(id) {
    return request({
      url: `${cloud.dqbasic}/partyMember/query`,
      method: 'get',
      params: {
        orgIds: id
      }
    })
  },

  // 下发
  issued(ids) {
    return request({
      url: `${cloud.dqbasic}/meeting/issued`,
      method: 'post',
      data: ids
    })
  },

  // 立即下发
  immediately(data) {
    return request({
      url: `${cloud.dqbasic}/meeting/issued/immediately`,
      method: 'post',
      data
    })
  },

  // 新增或更新会议纪要
  addOrUpdateMeetingRecord(data) {
    return request({
      url: `${cloud.dqbasic}/meetingRecord/addOrUpdate`,
      method: 'post',
      data
    })
  },

  // 批量新增或更新会议纪要
  addOrUpdateBatchMeetingRecord(data) {
    return request({
      url: `${cloud.dqbasic}/meetingRecord/addOrUpdateBatch`,
      method: 'post',
      data
    })
  },

  // 批量提交会议纪要
  submitBatchMeetingRecord(data) {
    return request({
      url: `${cloud.dqbasic}/meetingRecord/submitBatch`,
      method: 'post',
      data
    })
  },

  // 获取会议纪要
  getMeetingRecord(id, type) {
    return request({
      url: `${cloud.dqbasic}/meetingRecord/detail`,
      method: 'get',
      params: {
        meetingId: id,
        type
      }
    })
  },

  // 获取会议主题纪要
  getMeetingRecordDetailTopic(id, type) {
    return request({
      url: `${cloud.dqbasic}/meetingRecord/detailTopic`,
      method: 'get',
      params: {
        meetingId: id,
        type
      }
    })
  },

  // 更新文件id
  updateFileId(data) {
    return request({
      url: `${cloud.dqbasic}/meeting/update/fileId`,
      method: 'post',
      data
    })
  },

  // 获取文件信息
  getFileData(id) {
    return request({
      url: `${cloud.dqbasic}/sysFileInfo/getFileInfoListByFileIds`,
      method: 'get',
      params: {
        fileIds: id
      }
    })
  },

  // 获取三会一课列表
  getMeetingLessonList(params) {
    return request({
      url: `${cloud.dqbasic}/meetingLesson/page`,
      method: 'get',
      params
    })
  },

  // 获取组织生活会列表
  getOrganizingLifeList(params) {
    return request({
      url: `${cloud.dqbasic}/organizingLife/page`,
      method: 'get',
      params
    })
  }

})
