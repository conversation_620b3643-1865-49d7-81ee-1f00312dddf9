<template>
  <el-dialog
    :title="title"
    width="40%"
    :visible.sync="dialogVisible"
    :before-close="handleClose"
  >
    <div v-loading="loading">
      <!-- 试题 -->
      <el-row>
        <el-col :span="3" class="label">
          试题
        </el-col>

        <el-col :span="19">
          {{ form.content }}
        </el-col>
      </el-row>

      <!-- 单选、多选选项 -->
      <div v-if="form.quesType == 0 || form.quesType == 1">
        <el-row v-if="form.optionA">
          <el-col :span="3" class="label">
            选项A
          </el-col>

          <el-col :span="19">
            {{ form.optionA }}
          </el-col>
        </el-row>

        <el-row v-if="form.optionB">
          <el-col :span="3" class="label">
            选项B
          </el-col>

          <el-col :span="19">
            {{ form.optionB }}
          </el-col>
        </el-row>

        <el-row v-if="form.optionC">
          <el-col :span="3" class="label">
            选项C
          </el-col>

          <el-col :span="19">
            {{ form.optionC }}
          </el-col>
        </el-row>

        <el-row v-if="form.optionD">
          <el-col :span="3" class="label">
            选项D
          </el-col>

          <el-col :span="19">
            {{ form.optionD }}
          </el-col>
        </el-row>

        <el-row v-if="form.optionE">
          <el-col :span="3" class="label">
            选项E
          </el-col>

          <el-col :span="19">
            {{ form.optionE }}
          </el-col>
        </el-row>

        <el-row v-if="form.optionF">
          <el-col :span="3" class="label">
            选项F
          </el-col>

          <el-col :span="19">
            {{ form.optionF }}
          </el-col>
        </el-row>
      </div>

      <!-- 判断选项 -->
      <div v-if="form.quesType == 2">
        <el-row>
          <el-col :span="3" class="label">
            选项A
          </el-col>

          <el-col :span="19">
            正确
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="3" class="label">
            选项B
          </el-col>

          <el-col :span="19">
            错误
          </el-col>
        </el-row>
      </div>

      <!-- 正确答案 -->
      <el-row>
        <el-col :span="3" class="label">
          正确答案
        </el-col>

        <el-col v-if="form.quesType == 0" :span="19">
          <el-radio-group v-model="form.monoRight" disabled>
            <el-radio
              v-if="form.optionA"
              label="0"
            >
              A
            </el-radio>

            <el-radio
              v-if="form.optionB"
              label="1"
            >
              B
            </el-radio>

            <el-radio
              v-if="form.optionC"
              label="2"
            >
              C
            </el-radio>

            <el-radio
              v-if="form.optionD"
              label="3"
            >
              D
            </el-radio>

            <el-radio
              v-if="form.optionE"
              label="4"
            >
              E
            </el-radio>

            <el-radio
              v-if="form.optionF"
              label="5"
            >
              F
            </el-radio>
          </el-radio-group>
        </el-col>

        <el-col v-if="form.quesType == 1" :span="19">
          <el-checkbox-group v-model="form.multiRight" disabled>
            <el-checkbox
              v-if="form.optionA"
              label="0"
            >
              A
            </el-checkbox>

            <el-checkbox
              v-if="form.optionB"
              label="1"
            >
              B
            </el-checkbox>

            <el-checkbox
              v-if="form.optionC"
              label="2"
            >
              C
            </el-checkbox>

            <el-checkbox
              v-if="form.optionD"
              label="3"
            >
              D
            </el-checkbox>

            <el-checkbox
              v-if="form.optionE"
              label="4"
            >
              E
            </el-checkbox>

            <el-checkbox
              v-if="form.optionF"
              label="5"
            >
              F
            </el-checkbox>
          </el-checkbox-group>
        </el-col>

        <el-col v-if="form.quesType == 2" :span="19">
          <el-radio-group v-model="form.judgeRight" disabled>
            <el-radio label="0">
              正确
            </el-radio>

            <el-radio label="1">
              错误
            </el-radio>
          </el-radio-group>
        </el-col>
      </el-row>

      <!-- 我的答案 -->
      <el-row v-if="showUserAnswer">
        <el-col :span="3" class="label">
          我的答案
        </el-col>

        <el-col v-if="form.quesType == 0" :span="19">
          <el-radio-group v-model="row.userAnswer" disabled>
            <el-radio
              v-if="form.optionA"
              label="0"
            >
              A
            </el-radio>

            <el-radio
              v-if="form.optionB"
              label="1"
            >
              B
            </el-radio>

            <el-radio
              v-if="form.optionC"
              label="2"
            >
              C
            </el-radio>

            <el-radio
              v-if="form.optionD"
              label="3"
            >
              D
            </el-radio>

            <el-radio
              v-if="form.optionE"
              label="4"
            >
              E
            </el-radio>

            <el-radio
              v-if="form.optionF"
              label="5"
            >
              F
            </el-radio>
          </el-radio-group>
        </el-col>

        <el-col v-if="form.quesType == 1" :span="19">
          <el-checkbox-group v-model="row.userAnswer" disabled>
            <el-checkbox
              v-if="form.optionA"
              label="0"
            >
              A
            </el-checkbox>

            <el-checkbox
              v-if="form.optionB"
              label="1"
            >
              B
            </el-checkbox>

            <el-checkbox
              v-if="form.optionC"
              label="2"
            >
              C
            </el-checkbox>

            <el-checkbox
              v-if="form.optionD"
              label="3"
            >
              D
            </el-checkbox>

            <el-checkbox
              v-if="form.optionE"
              label="4"
            >
              E
            </el-checkbox>

            <el-checkbox
              v-if="form.optionF"
              label="5"
            >
              F
            </el-checkbox>
          </el-checkbox-group>
        </el-col>

        <el-col v-if="form.quesType == 2" :span="19">
          <el-radio-group v-model="row.userAnswer" disabled>
            <el-radio label="0">
              正确
            </el-radio>

            <el-radio label="1">
              错误
            </el-radio>
          </el-radio-group>
        </el-col>
      </el-row>

      <!-- 解析 -->
      <el-row>
        <el-col :span="3" class="label">
          解析
        </el-col>

        <el-col :span="19">
          {{ form.remark }}
        </el-col>
      </el-row>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="handleClose">
        关 闭
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  getTrainingQuestionDetail
} from '@/api/exam-manage/question-bank'

export default {
  props: {
    /**
     * 是否显示用户答案
     */
    showUserAnswer: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      // 弹框
      dialogVisible: false,

      // 标题
      title:'试题解析',

      // 加载中
      loading: false,

      // 试题详情
      row:{},
      form: {}
    }
  },

  methods: {
    // 初始化
    init(id, row) {
      if (this.showUserAnswer) {
        this.row = row
        if (this.row.quesType == 1) {
          this.row.userAnswer = !this.row.userAnswer ? [] : this.row.userAnswer.split(',')
        }
      }

      this.getQuestionDetail(id)
      this.dialogVisible = true
    },

    // 获取试题详情
    getQuestionDetail(id) {
      this.loading = true
      getTrainingQuestionDetail({ id }).then((res) => {
        this.loading = false
        this.form = res.data
        if (this.form.quesType == 1) {
          this.form.multiRight = this.form.multiRight.split(',')
        }
      }).catch(() => {
        this.loading = false
      })
    },

    // 关闭弹框
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.el-row{
  margin: 20px 0;

  .label{
    color: #333;
    font-weight: 700;
  }
}
</style>
