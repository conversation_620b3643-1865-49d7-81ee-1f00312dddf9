import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 系统配置-接口权限配置-左侧应用菜单树
  getAppMenuList(params) {
    return request({
      url: `${cloud.permission}/app/getAppMenuList`,
      method: 'get',
      params
    })
  },
  // 系统配置-接口权限配置-右侧获取菜单或按钮下关联的资源
  getApiResource(params) {
    return request({
      url: `${cloud.permission}/resource/getApiResource`,
      method: 'get',
      params
    })
  },
  // 5.3.4.3接口绑定-关联接口-查询应用下所有接口
  getApiByAppServiceList(params) {
    return request({
      url: `${cloud.permission}/resource/getApiByAppServiceList`,
      method: 'get',
      params
    })
  },
  //   关联接口绑定
  relateApi(data) {
    return request({
      url: `${cloud.permission}/allocate/relateApi`,
      method: 'POST',
      data
    })
  },
  //   解除接口绑定
  unrelateApi(data) {
    return request({
      url: `${cloud.permission}/allocate/unrelateApi`,
      method: 'POST',
      data
    })
  },
  // 获取服务名称列表
  apiAppList(params) {
    return request({
      url: `${cloud.permission}/resource/getApiAppList`,
      method: 'get',
      params
    })
  }
})
