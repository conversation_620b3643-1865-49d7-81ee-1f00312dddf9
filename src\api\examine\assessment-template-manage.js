/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-08-16 15:50:26
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-08-16 16:09:34
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud}  from '@/framework/utils/request'

// 查询考核模板管理列表
export function listAssessmentTemplateManage(query) {
  return request({
    url: cloud.dqbasic +'/assessmentTemplateManage/page',
    method: 'get',
    params: query
  })
}

// 查询考核模板管理详细
export function getAssessmentTemplateManage(id) {
  return request({
    url: cloud.dqbasic +'/assessmentTemplateManage/detail?id=' + id,
    method: 'get'
  })
}

// 新增考核模板管理
export function addAssessmentTemplateManage(data) {
  return request({
    url: cloud.dqbasic +'/assessmentTemplateManage/add',
    method: 'post',
    data: data
  })
}

// 修改考核模板管理
export function updateAssessmentTemplateManage(data) {
  return request({
    url: cloud.dqbasic +'/assessmentTemplateManage/edit',
    method: 'post',
    data: data
  })
}

// 删除考核模板管理
export function delAssessmentTemplateManage(id) {
  return request({
    url: cloud.dqbasic +'/assessmentTemplateManage/delete',
    method: 'post',
    data: { ids: id }
  })
}
// 启用禁用考核模板管理
export function updateEnable(data) {
  return request({
    url:cloud.dqbasic + '/assessmentTemplateManage/updateEnable',
    method: 'post',
    data: data
  })
}
