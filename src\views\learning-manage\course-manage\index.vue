<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="100px"
          @submit.native.prevent
        >
          <el-form-item label="课程名称" prop="videoName">
            <el-input
              v-model.trim="queryParams.videoName"
              class="filter-item limit"
              style="width: 200px; margin-right: 10px;"
              maxlength="30"
              clearable
              placeholder="请输入课程名称"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item label="课程类型" prop="resourceType">
            <el-select v-model="queryParams.resourceType" placeholder="请选择">
              <el-option
                v-for="item in courseOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择">
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="创建时间" prop="department">
            <el-date-picker
              v-model="daterange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>

          <el-form-item label="课程栏目" prop="department">
            <el-select v-model="queryParams.courseGrouping" placeholder="请选择" clearable>
              <el-option
                v-for="item in groupOptions"
                :key="item.dictCode"
                :label="item.dictName"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
        </el-form>

        <div class="flex-1" />

        <div class="fr">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            搜索
          </el-button>

          <el-button icon="el-icon-refresh" @click="handleReset">
            重置
          </el-button>
        </div>
      </div>

      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleAdd"
          >
            新增
          </el-button>

          <div class="flex-1" />
        </div>

        <el-table
          ref="table"
          v-loading="loading"
          style="width: 100%;"
          border
          highlight-current-row
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            type="index"
            label="序号"
            width="70"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            key="videoName"
            label="课程名称"
            show-overflow-tooltip
            align="center"
            prop="videoName"
            min-width="200"
          >
            <template #default="scope">
              <span class="view-text" @click="handleDetail(scope.row)">
                {{ scope.row.videoName }}
              </span>
            </template>
          </el-table-column>

          <el-table-column
            key="resourceType"
            label="课程类型"
            show-overflow-tooltip
            align="center"
            prop="resourceType"
            min-width="120"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.resourceType == '0'">
                视频
              </span>

              <span v-else-if="scope.row.resourceType == '1'">
                图文
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="课程栏目"
            show-overflow-tooltip
            align="center"
            prop="courseGroupingName"
            min-width="180"
          />

          <el-table-column
            key="status"
            label="状态"
            show-overflow-tooltip
            align="center"
            prop="status"
            min-width="120"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.status == 1">
                启用
              </span>

              <span v-else-if="scope.row.status == 2">
                禁用
              </span>
            </template>
          </el-table-column>

          <el-table-column
            key="createUserName"
            label="创建人"
            show-overflow-tooltip
            align="center"
            prop="createUserName"
            min-width="180"
          />

          <el-table-column
            key="orgName"
            label="创建部门"
            show-overflow-tooltip
            align="center"
            prop="orgName"
            min-width="180"
          />

          <el-table-column
            key="createTime"
            label="创建时间"
            show-overflow-tooltip
            align="center"
            prop="createTime"
            min-width="180"
            :formatter="dateFormat"
          />

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            min-width="330"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                :loading="scope.row.isDetailLoading"
                @click="handleDetail(scope.row)"
              >
                查看
              </el-button>

              <el-button
                v-if="scope.row.status == 2 "
                type="text"
                :loading="scope.row.isOperateLoading"
                @click="handleEnable(scope.row)"
              >
                启用
              </el-button>

              <el-button
                v-if="scope.row.status == 1 "
                type="text"
                :loading="scope.row.isOperateLoading"
                @click="handleDisable(scope.row)"
              >
                禁用
              </el-button>


              <el-button
                v-if="scope.row.status == 2 "
                type="text"
                :loading="scope.row.isDetailLoading"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>


              <el-button
                v-if="scope.row.status == 2 "
                type="text"
                :loading="scope.row.isDetailLoading"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>

              <el-button
                type="text"
                :loading="scope.row.isDetailLoading"
                @click="handleAudit(scope.row)"
              >
                数据分析
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <CourseDialog
      ref="courseDialog"
      :key="courseDialogKey"
      :title="dialogTitle"
      :type="type"
      :visible.sync="dialogVisible"
      @refresh="handleRefresh"
      @closed="handleCourseDialogClosed"
    />

    <DataAnalysis
      ref="analysisDialog"
      :course-form="courseForm"
      :analysis-visible="analysisVisible"
      @updateAnalysisVisible="updateAnalysisVisible"
    />
  </div>
</template>

<script>
import { getTrainingVideoList, getTrainingVideoDetail, deleteTrainingVideoDetail, updateStatus } from '@/api/training-examination/training-course'
import DataAnalysis from './components/data-analysis'
import CourseDialog from './components/course-modal'
import { getFileDetail } from '@/api/common'
import moment from 'moment'

export default {
  name: 'CourseManage',
  components: { CourseDialog, DataAnalysis },
  data() {
    return {
      // 查询参数
      courseDialogKey: Math.random(),
      queryParams:{
        videoName:'',
        resourceType:'2',
        status:0,
        beginTime:'',
        endTime:'',
        pageNo:1,
        pageSize:10
      },

      // 数据分析表单
      courseForm: {},
      // 控制弹框展示信息-新增or查看or编辑
      type:'',
      // 新增、查看页面弹框标题
      dialogTitle:'',
      // 新增、查看弹框是否显示
      dialogVisible:false,
      // 课程下拉选项
      courseOptions:[{
        value: '2',
        label: '全部'
      }, {
        value: '1',
        label: '图文'
      }, {
        value: '0',
        label: '视频'
      }],

      // 数据分析弹框
      analysisVisible:false,
      // 状态下拉选项
      statusOptions:[{
        value: 0,
        label: '全部'
      }, {
        value: 1,
        label: '启用'
      }, {
        value: 2,
        label: '禁用'
      }],

      groupOptions:[
      ],

      // 查询参数存放变量
      query:{},

      // 查询参数日期范围
      daterange:'',
      // 表格loading
      loading:true,
      // 表格数据源
      tableData:[],
      // 表格总条数
      total:0
    }
  },

  computed: {},
  watch: {},
  created() {
    this.businessDictList({ dictTypeCode: 'courseGrouping' }).then((res) => {
      this.groupOptions = res.data.rows
    })
    this.handleQuery()
  },

  mounted() {

  },

  beforeDestroy() {},
  methods: {
    handleRefresh() {
      this.getList()
    },

    handleCourseDialogClosed() {
      this.courseDialogKey = Math.random()
    },

    /** 查询方法 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    /** 数据分析按钮操作 */
    handleAudit(row) {
      row.isDetailLoading = true
      const query = {}
      query.id = row.id
      getTrainingVideoDetail(query).then((res) => {
        this.courseForm = res.data
        this.analysisVisible = true
        getFileDetail(this.courseForm.videoPath).then((val) => {
          if (val.data != null && val.data != undefined) {
            this.courseForm.videoPath = val.data.fileLink
          }
        }).finally(() => {
          row.isDetailLoading = false
        })
      })
    },

    /** 重置方法 */
    handleReset() {
      this.queryParams = {
        videoName:'',
        resourceType:'2',
        status:0,
        beginTime:'',
        endTime:'',
        pageNo:1,
        pageSize:10
      }
      this.daterange = ''
      this.getList()
    },

    /** 新增方法 */
    handleAdd() {
      this.type = 'add'
      this.dialogTitle = '新增'
      this.dialogVisible = true
    },

    /** 格式化课程分组表格列 */
    // formatGroupingName(row, column){
    //   this.
    // },
    /** 查询方法 */
    getList() {
      this.query = JSON.parse(JSON.stringify(this.queryParams))
      if (this.daterange != '' && this.daterange != null) {
        this.query.beginTime = this.daterange[0]
        this.query.endTime = this.daterange[1]
      }
      if (this.queryParams.resourceType == '2') {
        this.$delete(this.query, 'resourceType')
      }
      if (this.queryParams.status == 0) {
        this.$delete(this.query, 'status')
      }
      this.loading = true
      getTrainingVideoList(this.query).then((data) => {
        this.loading = false
        data.data.rows.forEach((item) => {
          item.isDetailLoading = false
          item.isOperateLoading = false
          item.isDeleteLoading = false
        })
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      })
    },


    /** 查看按钮操作 */
    handleDetail(row) {
      const query = {}
      query.id = row.id
      row.isDetailLoading = true
      getTrainingVideoDetail(query).then((res) => {
        this.$refs.courseDialog.courseForm = this.adaptResponseData(res.data)
        this.dialogTitle = '查看'
        this.type = 'check'
        this.dialogVisible = true
      }).finally(() => {
        row.isDetailLoading = false
      })
    },

    adaptResponseData(data) {
      const newData = JSON.parse(JSON.stringify(data))
      newData.questionList = (newData.questionDTOList || [])
        .map((item) => ({
          ...item,
          second: parseFloat(item.second),
          type: item.quesType
        }))
      return newData
    },

    /** 编辑按钮操作 */
    handleEdit(row) {
      const query = {}
      query.id = row.id
      row.isDetailLoading = true
      getTrainingVideoDetail(query).then((res) => {
        this.$refs.courseDialog.courseForm = this.adaptResponseData(res.data)
        this.dialogTitle = '编辑'
        this.type = 'edit'
        this.dialogVisible = true
      }).finally(() => {
        row.isDetailLoading = false
      })
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      this.$dtModal
        .confirm(`是否确认删除课程名称为"${row.videoName}"的数据项？`)
        .then(() => {
          row.isDeleteLoading = true
          row.ids = []
          row.ids.push(row.id)
          return deleteTrainingVideoDetail(row)
        })
        .then((res) => {
          if (res.success == true) {
            this.getList()
            this.$dtModal.msgSuccess('删除成功')
          }
        })
        .catch((e) => {
          console.log(e)
        })
        .finally(() => {
          row.isDeleteLoading = false
        })
    },

    /** 启用按钮操作 */
    handleEnable(row) {
      this.$dtModal
        .confirm(`是否确认启用课程名称为"${row.videoName}"的数据项？`)
        .then(() => {
          row.isOperateLoading = true
          return updateStatus({ ...row, status: 1 })
        })
        .then((res) => {
          this.getList()
          this.$dtModal.msgSuccess('启用成功')
        })
        .catch((e) => {
          console.log(e)
        })
        .finally(() => {
          row.isOperateLoading = false
        })
    },

    /** 禁用按钮操作 */
    handleDisable(row) {
      this.$dtModal
        .confirm(`是否确认禁用课程名称为"${row.videoName}"的数据项？`)
        .then(() => {
          row.isOperateLoading = true
          return updateStatus({ ...row, status: 2 })
        })
        .then((res) => {
          if (res.success == true) {
            this.getList()
            this.$dtModal.msgSuccess('禁用成功')
          }
        })
        .catch((e) => {
          console.log(e)
        })
        .finally(() => {
          row.isOperateLoading = false
        })
    },

    /** 更新新增弹框状态 */
    // updateDialogVisible(val) {
    //   this.dialogVisible = val
    //   if (val) {
    //     this.getList()
    //   }
    // },

    /** 更新数据分析弹框状态 */
    updateAnalysisVisible(val) {
      this.analysisVisible = val
    },

    /** 格式化表格列时间 */
    dateFormat(row, column, cellValue) {
      // 假设 cellValue 是一个毫秒数或标准的 JavaScript Date 对象
      return moment(cellValue).format('YYYY-MM-DD HH:mm:ss') // 使用 Moment.js
    }
  }
}
</script>

<style scoped lang="scss">
.view-text {
  color: var(--primary);
  cursor: pointer;
  font-weight: 500;
}
</style>
