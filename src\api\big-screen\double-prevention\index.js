import request from '@/framework/utils/request'


// 风险事件总数   隐患总数
export function getDoublePreventNum(query) {
  return request({
    url: `/project/doublePrevent/num`,
    method: 'get',
    params: query
  })
}

// 重大隐患-  一般隐患
export function getHiddenDangerNum(query) {
  return request({
    url: `/project/doublePrevent/hiddenDanger/num`,
    method: 'get',
    params: query
  })
}

// 企业风险分级管控清单
export function getRiskLevel(query) {
  return request({
    url: `/project/doublePrevent/risk/level/statistics`,
    method: 'get',
    params: query
  })
}

// 重大隐患督办情况
export function getHiddenDangerStatistics(query) {
  return request({
    url: `/project/doublePrevent/import/hiddenDanger/statistics`,
    method: 'get',
    params: query
  })
}

// 重大隐患临期/超期
export function getComingStatistics(query) {
  return request({
    url: `/project/doublePrevent/import/hiddenDanger/coming/statistics`,
    method: 'get',
    params: query
  })
}

// 一般隐患超期警示
export function getNormalStatistics(query) {
  return request({
    url: `/project/doublePrevent/normal/hiddenDanger/statistics`,
    method: 'get',
    params: query
  })
}

// 地图数据  也是  临期提醒  里面的
export function getMapData(query) {
  return request({
    url: `/project/doublePrevent/map/data`,
    method: 'get',
    params: query
  })
}
