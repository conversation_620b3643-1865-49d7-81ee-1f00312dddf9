/*
 * @Author: 高宇 <EMAIL>
 * @Date: 2024-05-08 15:27:30
 * @LastEditors: 高宇 <EMAIL>
 * @LastEditTime: 2024-05-09 13:55:20
 * @FilePath: \isrmp_vue\src\api\SecurityOrganization\SecurityOrganization.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/framework/utils/request'

// 查询安全组织架构列表(不分页)
export function bizSecurityOrganizationSelectList(data) {
  return request({
    url: '/project/bizSecurityOrganization/selectList',
    method: 'POST',
    data
  })
}
// 查询安全组织架构列表(分页)
export function bizSecurityOrganizationPage(params) {
  return request({
    url: '/project/bizSecurityOrganization/page',
    method: 'get',
    params
  })
}

// 查询安全组织架构详细
export function bizSecurityOrganizationDetail(params) {
  return request({
    url: '/project/bizSecurityOrganization/detail',
    method: 'get',
    params
  })
}

// 新增安全组织架构
export function bizSecurityOrganizationAdd(data) {
  return request({
    url: '/project/bizSecurityOrganization/add',
    method: 'post',
    data: data
  })
}

// 修改安全组织架构
export function bizSecurityOrganizationEdit(data) {
  return request({
    url: '/project/bizSecurityOrganization/edit',
    method: 'post',
    data: data
  })
}

// 删除安全组织架构
export function bizSecurityOrganizationDel(data) {
  return request({
    url: '/project/bizSecurityOrganization/delete',
    method: 'post',
    data
  })
}
