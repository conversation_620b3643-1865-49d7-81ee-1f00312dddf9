/*
 * @Author: zzp
 * @Date: 2024-04-22 15:53:40
 * @LastEditors:
 * @LastEditTime: 2024-04-22 16:42:26
 * @FilePath: \isrmp_vue\src\api\HealthExaminePlan\health-examine-plan.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud} from '@/framework/utils/request'

// 查询实战应急演练列表
export function emergMateriaStoreGetPage(query) {
  return request({
    url: cloud.dqbasic + '/emergMateriaStore/getListPage',
    method: 'get',
    params: query
  })
}

// 查询实战应急演练详细
export function getEmergMateriaStore(id) {
  return request({
    url: cloud.dqbasic + '/emergMateriaStore/detail?id=' + id,
    method: 'get'
  })
}

// 新增实战应急演练数据
export function addEmergMateriaStore(data) {
  return request({
    url: cloud.dqbasic + '/emergMateriaStore/add',
    method: 'post',
    data: data
  })
}

// 修改实战应急演练数据
export function updateEmergMateriaStore(data) {
  return request({
    url: cloud.dqbasic + '/emergMateriaStore/edit',
    method: 'post',
    data: data
  })
}

// 删除职业健康体检计划
export function delEmergMateriaStore(id) {
  return request({
    url: cloud.dqbasic + '/emergMateriaStore/delete',
    method: 'post',
    data: { ids: id }
  })
}
