import { default as request, cloud } from '@/framework/utils/request'

// 待阅列表
export function toReadList(query) {
  return request({
    url: `${cloud.process}/myflow/cs/toReadList`,
    method: 'get',
    params: query
  })
}
// 已阅列表
export function readList(query) {
  return request({
    url: `${cloud.process}/myflow/cs/readList`,
    method: 'get',
    params: query
  })
}
// 待办列表
export function toDoTaskList(query) {
  return request({
    url: `${cloud.process}/myflow/toDoTask/getPage`,
    method: 'get',
    params: query
  })
}// 已办列表
export function doneTaskList(query) {
  return request({
    url: `${cloud.process}/myflow/doneTask/getPage`,
    method: 'get',
    params: query
  })
}// 我的列表
export function myApplyList(query) {
  return request({
    url: `${cloud.process}/myflow/myApply/getPage`,
    method: 'get',
    params: query
  })
}// 发起申请
export function applyList(query) {
  return request({
    url: `${cloud.process}/myflow/applyList`,
    method: 'get',
    params: query
  })
}
