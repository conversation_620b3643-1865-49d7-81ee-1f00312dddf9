import { default as request, cloud } from '@/framework/utils/request'
// 重大危险源类型统计：/hazard/hazardCategoryCount
export function getHarmfulGasScreen(params) {
  return request({
    url: `${cloud.dqbasic}/harmfulGasScreen/data`,
    method: 'get',
    params
  })
}
// harmfulGasScreen/dealWarn 确定已知处理
export function dealWarn(id) {
  return request({
    url: `${cloud.dqbasic}/harmfulGasScreen/dealWarn`,
    method: 'post',
    data: { id }
  })
}
