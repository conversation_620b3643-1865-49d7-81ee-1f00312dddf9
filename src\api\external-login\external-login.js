/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-04-16 15:39:54
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-09-14 10:35:16
 * @FilePath: \isrmp_vue\src\api\ExpenseCategoryManage\expense-category-manage.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import { default as request, cloud } from '@/framework/utils/request'

// 获取工作单位列表
export function getCompanyList(query) {
  return request({
    url: cloud.dqbasic + '/relateCompany/getList',
    method: 'get',
    params: query
  })
}

// 相关方登录
export function loginExternalApi(data) {
  return request({
    url: cloud.auth + '/loginExternalApi',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
