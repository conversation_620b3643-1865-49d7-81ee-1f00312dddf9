import { default as request, cloud } from '@/framework/utils/request'

// 查询列表
export function listType(query) {
  return request({
    url: `${cloud.manage}/sysAppType/list`,
    method: 'get',
    params: query
  })
}
// 新增
export function addType(data) {
  return request({
    url: `${cloud.manage}/sysAppType/add`,
    method: 'post',
    data
  })
}

// 编辑
export function editType(data) {
  return request({
    url: `${cloud.manage}/sysAppType/edit`,
    method: 'post',
    data
  })
}
// 删除
export function delType(data) {
  return request({
    url: `${cloud.manage}/sysAppType/delete`,
    method: 'post',
    data
  })
}

// 详情
export function detailType(query) {
  return request({
    url: `${cloud.manage}/sysAppType/detail`,
    method: 'get',
    params: query
  })
}
