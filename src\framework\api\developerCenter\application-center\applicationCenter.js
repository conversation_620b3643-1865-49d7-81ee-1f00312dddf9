import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 查询低代码应用列表
  getLowCodeAppList(params) {
    return request({
      url: `${cloud.devcenter}/sysApp/getLowCodeAppList`,
      method: 'get',
      params
    })
  },
  // 新建应用
  sysAppAdd(data) {
    return request({
      url: `${cloud.devcenter}/sysApp/add`,
      method: 'post',
      data
    })
  },
  // 修改应用
  sysAppEdit(data) {
    return request({
      url: `${cloud.devcenter}/sysApp/edit`,
      method: 'post',
      data
    })
  },
  // 应用详情
  sysAppDetail(params) {
    return request({
      url: `${cloud.devcenter}/sysApp/detail`,
      method: 'get',
      params
    })
  },
  // 应用发布状态的修改
  updateStatus(data) {
    return request({
      url: `${cloud.devcenter}/sysApp/updateStatus`,
      method: 'post',
      data
    })
  },
  // 应用删除
  sysAppDelete(data) {
    return request({
      url: `${cloud.devcenter}/sysApp/delete`,
      method: 'post',
      data
    })
  },
  // 删除应用数据
  deleteData(data) {
    return request({
      url: `${cloud.devcenter}/sysApp/deleteData`,
      method: 'post',
      data
    })
  },
  // 查询维护人
  getManagingOrgUserList(params) {
    return request({
      url: `${cloud.permission}/managerGroup/getManagingOrgUserListCommon`,
      method: 'get',
      params
    })
  }
})
