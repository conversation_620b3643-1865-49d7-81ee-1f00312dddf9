/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-11 11:22:59
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-08-21 23:19:40
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'

// 查询车辆异常行为报警列表
export function listCarAlarm(query) {
  return request({
    url: '/project/carAlarm/page',
    method: 'get',
    params: query
  })
}

// 查询车辆异常行为报警详细
export function getCarAlarm(id) {
  return request({
    url: '/project/carAlarm/detail?id=' + id,
    method: 'get'
  })
}

// 车辆异常行为报警--消警
export function antiAlarm(data) {
  return request({
    url: '/project/carAlarm/antiAlarm',
    method: 'post',
    data: data
  })
}
