/*
 * @Author: zzp
 * @Date: 2024-04-22 15:53:40
 * @LastEditors:
 * @LastEditTime: 2024-04-22 16:42:26
 * @FilePath: \isrmp_vue\src\api\HealthExaminePlan\health-examine-plan.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud} from '@/framework/utils/request'

// 查询实战应急演练列表
export function emergShelterGetPage(query) {
  return request({
    url: cloud.dqbasic + '/emergShelter/getListPage',
    method: 'get',
    params: query
  })
}

// 查询实战应急演练详细
export function getEmergShelter(id) {
  return request({
    url: cloud.dqbasic + '/emergShelter/detail?id=' + id,
    method: 'get'
  })
}

// 新增实战应急演练数据
export function addEmergShelter(data) {
  return request({
    url: cloud.dqbasic + '/emergShelter/add',
    method: 'post',
    data: data
  })
}

// 修改实战应急演练数据
export function updateEmergShelter(data) {
  return request({
    url: cloud.dqbasic + '/emergShelter/edit',
    method: 'post',
    data: data
  })
}

// 删除职业健康体检计划
export function delEmergShelter(id) {
  return request({
    url: cloud.dqbasic + '/emergShelter/delete',
    method: 'post',
    data: { ids: id }
  })
}
