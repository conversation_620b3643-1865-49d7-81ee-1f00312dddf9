<!--
  * @Author: gaoyu <EMAIL>
  * @Date: 2025-04-07 09:19:11
  * @LastEditors: gaoyu <EMAIL>
  * @LastEditTime: 2025-04-10 19:55:40
  * @Description: 小铃铛消息抽屉列表
  * Copyright (c) 2025-present HBIS Digital Technology Co.,Ltd. All rights reserved.
-->
<template>
  <div>
    <el-drawer
      class="message-drawer"
      :visible.sync="drawer"
      :show-close="false"
      :with-header="false"
      :modal="false"
      size="25%"
    >
      <el-row class="row-body" :class="noticeM">
        <div class="message-drawer-header">
          <el-tabs v-model="activeName" @tab-click="handleTabsClick">
            <el-tab-pane :label="tabLabel" name="first" />

            <el-tab-pane :label="tabLabel2" name="second" />
          </el-tabs>

          <div class="flex-1" />

          <el-button
            class="all-read"
            type="text"
            @click="handleAllRead()"
          >
            清空
          </el-button>
        </div>

        <div class="message-drawer-body">
          <div
            v-if="bacdDisabled"
            style="
                margin-top: 30px;
                color: #ccc;
                font-size: 80px;
                text-align: center;
              "
          >
            <el-image
              style="width: 112px; height: 122px;"
              :src="require('@/framework/assets/<EMAIL>')"
            />

            <p style=" margin: 0;padding: 0; font-size: 14px;">
              暂无新消息
            </p>
          </div>

          <div v-else>
            <div
              v-for="(item, index) in dataList"
              :key="index"
              @click="getDetail(item)"
            >
              <div class="message-list-item">
                <div class="bell_main">
                  <div class="item-title" style="color: #666;" :title="item.messageTitle">
                    {{ item.messageTitle }}
                  </div>

                  <div class="item-time">
                    {{ item.scheduledSendTime }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-row>

      <el-row :class="foot">
        <div class="message-drawer-footer">
          <el-link :underline="false" @click="toMyMessageList()">
            {{
              bacdDisabled ? "查看历史消息" : "查看更多"
            }}
          </el-link>
        </div>
      </el-row>
    </el-drawer>

    <MessageDialog ref="messageDialogRef" />
  </div>
</template>

<script>
import {
  getUnReadList,
  getAllReadList,
  yesOrNotUnReadMessage,
  getMyPlatNotice,
  getReadDetail
} from '@/framework/api/message'
import { dealRouter } from '@/framework/utils/menu'
import messageDialog from './components/message-detail-dialog.vue'
export default {
  name: 'DtMessageList',
  components: { MessageDialog: messageDialog },
  inject: [
    'reload'
  ],

  props: {},
  data() {
    return {
      drawer: false,
      activeName: 'first',
      participantList: [],
      dataList: [],
      bacdDisabled: false,
      foot: 'foot',
      noticeM: 'noticeM',
      tabLabel: '通知公告（0）',
      tabLabel2: '平台消息（0）',
      dialogFormVisible: false,
      ruleForm: {}
    }
  },

  watch: {
    visible(data) {
      this.dialogVisible = data
    }
  },

  methods: {
    /**
     * @description: 初始化数据
     */
    initData() {
      this.drawer = true
      this.getUnList()
      this.getMyPlatNotice()
      this.activeName = 'first'
    },
    /**
     * @description: 获取通知公告数据
     */

    getUnList() {
      getUnReadList()
        .then((res) => {
          if (res.code === '00000') {
            this.messageNum = res.data.num
            if (this.activeName == 'first') {
              this.dataList = res.data.messageList
            }
            this.tabLabel = `通知公告（${res.data.num}）`
            this.openedDraw()
          }
        })
        .catch((e) => {
          this.tabLabel = '通知公告'
        })
    },

    /**
     * @description: 获取平台消息数据
     */
    getMyPlatNotice() {
      getMyPlatNotice().then((res) => {
        this.messageNum = res.data.num
        if (this.activeName == 'second') {
          this.dataList = res.data.messageList
        }
        this.tabLabel2 = `平台消息（${res.data.num}）`
        this.openedDraw()
      }).catch((e) => {
        this.tabLabel2 = '平台消息'
      })
    },

    /**
     * @description: 打开抽屉
     */
    openedDraw() {
      if (this.dataList.length === 0) {
        this.noticeM = 'noticeNone'
        this.bacdDisabled = true
      } else {
        this.bacdDisabled = false
        this.noticeM = 'noticeM'
        this.foot = 'footer'
      }
      this.$forceUpdate()
    },

    /**
     * @description: 点击tabs
     * @param {*} tab
     * @param {*} event
     */
    handleTabsClick(tab, event) {
      this.dataList = []
      if (tab.name == 'second') {
        this.getMyPlatNotice()
      } else {
        this.getUnList()
      }
    },

    /**
     * @description: 查看历史消息
     */
    async toMyMessageList() {
      this.drawer = false
      const path = '/notice/MessageList'
      if (this.$route.path == path) {
        this.$store.dispatch('tagsView/delCachedView', this.$route).then(() => {
          this.reload()
        })
      } else {
        this.$store.dispatch('settings/setAppCode', 'systemConfig')
        await dealRouter('/notice/MessageList')
        this.$router.push({ path: '/notice/MessageList' })
      }
    },

    /**
     * @description: 查看详情
     * @param {*} item
     */
    getDetail(item) {
      // 包教师傅签字通知
      const { messageTypeCode, messageParams } = item
      if (messageTypeCode === 'masterSign') {
        const { userId, trainId } = JSON.parse(messageParams)
        this.$router.push('/ArchiveTeacherSign?userId=' + userId + '&trainId=' + trainId)

        // 发送请求，将消息改为已读状态
        const params = {
          messageId: item.messageId
        }
        getReadDetail(params).then((res) => {})
      } 
      // 其他通知
      else {
        this.$refs.messageDialogRef.dialogFormVisible = true
        this.$refs.messageDialogRef.open1(item)
      }
    },

    /**
     * @description: 获取未读数据
     */
    initUnReadMessage() {
      yesOrNotUnReadMessage()
        .then((res) => {
          this.$store.dispatch('websocket/setIsUnReadMessage', res.data)
        })
    },

    /**
     * @description: 全部已读
     */
    handleAllRead() {
      getAllReadList().then((res) => {
        if (res.code === '00000') {
          this.tabLabel = '通知公告（0）'
          this.tabLabel2 = '平台消息（0）'
          this.bacdDisabled = true
          this.getUnList()
          this.getMyPlatNotice()
          this.$forceUpdate()
          this.$store.dispatch('websocket/setIsUnReadMessage', false)
        }
      })
    },

    changeList() {
      if (this.activeName == 'first') {
        this.getUnList()
      } else {
        this.getMyPlatNotice()
      }
    }
  }
}
</script>

  <style lang="scss" scoped>
  @import "~@/framework/styles/variables.scss";

  .message-drawer {
    ::v-deep .el-drawer__body {
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .row-body {
        // height: 90%;
        display: flex;
        flex: 1;
        flex-direction: column;
        overflow-y: auto;

        .message-drawer-header {
          display: flex;
          height: 56px;
          padding: 0 16px;
          line-height: 56px;
          border-bottom: 1px solid #dcdfe6;

          ::v-deep .el-tabs {
            .el-tabs__header {
              margin: 0;
            }

            .el-tabs__nav-wrap::after {
              display: none;
            }

            .el-tabs__active-bar {
              display: none;
            }

            .el-tabs__item {
              // padding: 0 10px;
            }
          }
        }

        .message-drawer-body {
          flex: 1;
          overflow-y: auto;

          .message-list-item {
            position: relative;
            display: block;
            height: 70px;
            padding: 12px 20px;
            border-bottom: 1px solid #e8e8e8;
            cursor: pointer;
            // overflow-y: auto;
            .bell {
              float: left;
              width: 50px;
              height: 50px;
              color: #fff;
              background: #dd4b39;

              & > i {
                margin: 10px;
                font-size: 30px;
              }
            }

            .bell_main {
              margin-left: 10px;
              // height: 24px;
              .item-title {
                display: -webkit-box;
                width: 70%;
                height: 24px;
                overflow: hidden;
                color: #131314;
                font-weight: 600;
                font-size: 14px;
                line-height: 24px;
                text-overflow: ellipsis;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;//超出的文本隐藏//溢出用省略号//显示1行，控制显示的行数
              }

              .item-time {
                color: #8f9299;
                font-size: 12px;
                line-height: 20px;
              }
            }
          }
        }

      }

      .message-drawer-footer {
        height: 56px;
        padding: 0 16px;
        line-height: 56px;
        text-align: center;
        border-top: 1px solid #dcdfe6;
      }
    }

  }

  .message-detail {
    .message-body {
      display: flex;
      flex-direction: column;
      min-height: 490px;

      .title {
        // margin-top: 24px;
        margin-bottom: 8px;
        color: #131314;
        font-weight: 500;
        font-size: 18px;
        font-family: "HarmonyOS Sans SC-Medium", "HarmonyOS Sans SC";
        line-height: 24px;
        text-align: center;
      }

      .des {
        display: flex;
        margin-bottom: 24px;
        padding-bottom: 4px;
        color: #606266;
        font-size: 12px;
        line-height: 24px;
        border-bottom: 1px solid #d8d8d8;

        .level{
        }
      }
    }
  }

  .all-read {
    color: #606266;
  }
  </style>
