<template>
  <el-dialog
    :title="dialogTitle"
    v-bind="$attrs"
    width="700px"
    top="10vh"
    v-on="$listeners"
    @open="handleOpen"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      v-loading="isLoading"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="实际培训时间" prop="trainingTime">
        <template v-if="mode === 'view'">
          <span>{{ formatTrainingTime }}</span>
        </template>

        <el-date-picker
          v-else
          v-model="trainingTime"
          type="datetimerange"
          range-separator="~"
          start-placeholder="选择日期和时间"
          end-placeholder="选择日期和时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="实际培训人数">
        <el-input v-model="formData.totalStuff" disabled placeholder="根据培训人员名单，系统自动生成" />
      </el-form-item>

      <el-form-item label="课程文件" prop="videoId">
        <FileUploadEcho
          style="width: 100%;"
          :show-preview="true"
          :file-id.sync="formData.videoId"
          :file-limit="5"
          :file-size="0"
          :is-show-tip="true"
          :is-train="true"
          :disabled="mode === 'view'"
          :file-type="[
            'mp4',
            'mov',
            'png',
            'jpg',
            'jpeg',
            'gif',
            'doc',
            'pdf'
          ]"
        />
      </el-form-item>

      <el-form-item label="培训相关文件" prop="relatedFile">
        <FileUploadEcho
          style="width: 100%;"
          :show-preview="true"
          :file-id.sync="formData.trainingFileId"
          :file-limit="5"
          :file-size="10"
          :is-show-tip="true"
          :is-train="true"
          :disabled="mode === 'view'"
          :file-type="[
            'jpg',
            'pdf'
          ]"
        />
      </el-form-item>

      <el-form-item label="培训人员名单">
        <div v-if="mode === 'edit'" class="table-actions">
          <el-button type="primary" @click="handleDownloadTemplate">
            下载模板
          </el-button>

          <el-button type="primary" :loading="isImportLoading" @click="handleImport">
            导入
          </el-button>

          <input
            ref="importInputRef"
            style="display: none"
            type="file"
            @change="handleFilePicked"
          >
        </div>

        <el-table
          v-loading="isPreviewLoading"
          :data="formData.remembers"
          border
          height="200px"
        >
          <el-table-column prop="userName" label="姓名" />

          <el-table-column prop="tel" label="手机号" />

          <el-table-column prop="orgName" label="部门" />
        </el-table>
      </el-form-item>
    </el-form>

    <div slot="footer">
      <el-button @click="handleCancel">
        {{ mode === 'view' ? '关闭' : '取消' }}
      </el-button>

      <el-button
        v-if="mode === 'edit'"
        type="primary"
        :loading="isSubmitLoading"
        @click="handleSubmit"
      >
        提交
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  downloadPersonnelTemplate,
  getTrainingRecordById,
  importPersonnel,
  getPersonnelInfoByIds,
  updateTrainingRecord
} from '@/api/learning-manage/training-plan'
import { getFileDetail } from '@/api/common/index'
import FileUploadEcho from '@/components/file-upload-echo/file-upload-echo.vue'
import dayjs from 'dayjs'

export default {
  name: 'StatisticModal',
  components: { FileUploadEcho },
  props: {
    trainingId: {
      type: String,
      required: true
    },

    mode: {
      type: String,
      validator: (val) => ['edit', 'view'].includes(val),
      default: 'edit'
    }
  },

  data() {
    const trainingTimeValidator = (rule, value, callback) => {
      if (this.formData.trainBegin && this.formData.trainEnd) {
        callback()
      } else {
        callback(new Error('请选择培训时间'))
      }
    }

    return {
      isImportLoading: false,
      isPreviewLoading: false,
      isLoading: false,
      isSubmitLoading: false,

      formData: {
        realBeginTime: '',
        realEndTime: '',
        // 课程文件
        videoId: '',
        // 培训相关文件
        trainingFileId: '',
        realUserIdList: [],
        userIdList: [],
        totalStuff: 0
      },

      formRules: {
        trainTime: [
          { required: true, validator: trainingTimeValidator, trigger: 'blur' }
        ],

        videoId: [
          { required: true, message: '请上传课程文件', trigger: 'change' }
        ],

        relatedFile: [
          { required: false }
        ]
      }
    }
  },

  computed: {
    trainingTime: {
      get() {
        if (this.formData.realBeginTime && this.formData.realEndTime) {
          return [this.formData.realBeginTime, this.formData.realEndTime]
        }
        return []
      },

      set(value) {
        if (value && value.length === 2) {
          this.formData.realBeginTime = value[0]
          this.formData.realEndTime = value[1]
        } else {
          this.formData.realBeginTime = ''
          this.formData.realEndTime = ''
        }
      }
    },

    formatTrainingTime() {
      const { realEndTime, realBeginTime } = this.formData
      if (!realEndTime || !realBeginTime) return '--'
      const start = dayjs(realBeginTime).format('YYYY-MM-DD HH:mm:ss')
      const end = dayjs(realEndTime).format('YYYY-MM-DD HH:mm:ss')

      return `${start} ~ ${end}`
    },

    dialogTitle() {
      return this.mode === 'view' ? '查看培训记录' : '上传培训记录'
    }
  },

  methods: {
    handleOpen() {
      this.getTrainingDetail()
    },

    handleClosed() {
      this.reset()
    },

    reset() {
      this.formData = {
        realBegin: '',
        realEnd: '',
        trainingCount: 0,
        courseFileList: '',
        relatedFileList: '',
        traineeList: [],
        videoPath: ''
      }
      this.isLoading = false
    },

    handleCancel() {
      this.$emit('update:visible', false)
    },

    handleSubmit() {
      //线下培训上传记录时提交校验实际培训人数不得小于0
      if (this.formData.totalStuff <= 0) {
        this.$message.error('实际培训人数不得小于0')
        return
      }
      this.isSubmitLoading = true
      this.$refs.formRef.validate()
        .then(() => {
          return updateTrainingRecord(this.formData)
        })
        .then(() => {
          this.$message.success('提交成功')
          this.$emit('update:visible', false)
          this.$parent.getList()
        })
        .catch((err) => {
          console.log(err, typeof err, err instanceof Error)
          if (err instanceof Error) {
            this.$message.error('提交失败')
          }
        })
        .finally(() => {
          this.isSubmitLoading = false
        })
    },

    handleDownloadTemplate() {
      downloadPersonnelTemplate()
        .then((res) => {
          const fileName = this.getFilenameFromHeaders(res) || '培训人员导入模板.xlsx'
          const link = document.createElement('a')
          link.href = window.URL.createObjectURL(res.data)
          link.download = fileName
          link.click()
          window.URL.revokeObjectURL(link.href)
        })
    },

    getFilenameFromHeaders(res, defaultName) {
      const disposition = res.headers['content-disposition']
      if (disposition) {
        const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
        const matches = filenameRegex.exec(disposition)
        if (matches != null && matches[1]) {
          return decodeURIComponent(matches[1].replace(/['"]/g, ''))
        }
      }
    },

    handleImport() {
      this.$refs.importInputRef.click()
    },

    handleFilePicked(event) {
      const file = event.target.files[0]
      if (!file) return
      const formData = new FormData()
      formData.append('file', file)

      this.isImportLoading = true
      importPersonnel(formData)
        .then((res) => {
          const list = res.data || []
          this.formData.remembers = list
          this.formData.realUserIdList = list.map((item) => item.id)
          this.formData.totalStuff = list.length
          return this.getPersonnelPreview(this.formData.realUserIdList)
        })
        .then((res) => {
          this.formData.remembers = res
        })
        .finally(() => {
          this.$refs.importInputRef.value = ''
          this.isImportLoading = false
        })
    },

    getPersonnelPreview(ids) {
      this.isPreviewLoading = true
      return getPersonnelInfoByIds(`${ids.join(',')}`)
        .then((res) => {
          return res.data.map((item) => ({
            id: item.userId,
            userName: item.userName,
            tel: item.userPhone,
            orgName: item.userDpt
          }))
        })
        .catch((err) => {
          console.error('获取人员信息失败:', err)
          this.$message.error('获取人员信息失败')
        })
        .finally(() => {
          this.isPreviewLoading = false
        })
    },

    getFileName(id) {
      getFileDetail(id)
        .then((res) => {
          console.log('文件信息', res.data)
        })
    },

    // 获取培训详情
    async getTrainingDetail() {
      try {
        this.isLoading = true
        const res = await getTrainingRecordById(this.trainingId)
        this.formData = res.data

        this.$nextTick().then(() => {
          this.$refs.formRef.clearValidate()
        })
      } catch (error) {
        console.error('获取培训详情失败:', error)
        this.$message.error('获取培训详情失败')
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.table-actions {
  margin-bottom: 10px;
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}
</style>
