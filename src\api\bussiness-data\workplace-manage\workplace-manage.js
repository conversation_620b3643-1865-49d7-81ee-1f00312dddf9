/*
 * @Author: 高宇 <EMAIL>
 * @Date: 2024-05-08 15:27:30
 * @LastEditors: 高宇 <EMAIL>
 * @LastEditTime: 2024-05-16 16:52:57
 * @FilePath: \isrmp_vue\src\api\SecurityOrganization\SecurityOrganization.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/framework/utils/request'

// 分页查询作业场所管理所有数据
export function bizWorkPlacePage(params) {
  return request({
    url: '/project/bizWorkPlace/page',
    method: 'get',
    params
  })
}

// 查询作业场所列表
export function bizWorkPlaceSelect(data) {
  return request({
    url: '/project/bizWorkPlace/selectList',
    method: 'POST',
    data
  })
}

// 新增作业场所管理数据
export function bizWorkPlaceAdd(data) {
  return request({
    url: '/project/bizWorkPlace/add',
    method: 'post',
    data: data
  })
}

// 修改作业场所管理数据
export function bizWorkPlaceEdit(data) {
  return request({
    url: '/project/bizWorkPlace/edit',
    method: 'post',
    data: data
  })
}

// 删除作业场所管理数据
export function delBizWorkPlace(id) {
  return request({
    url: '/project/bizWorkPlace/delete',
    method: 'post',
    data: { placeIds: id }
  })
}

// 获取作业场所管理单条数据详情
export function bizWorkPlaceDetail(params) {
  return request({
    url: '/project/bizWorkPlace/detail',
    method: 'get',
    params
  })
}

// 作业场所启用-停用操作
export function updateEnable(data) {
  return request({
    url: '/project/bizWorkPlace/updateEnable',
    method: 'POST',
    data
  })
}

// 查询岗位管理列表
export function selectList(data) {
  return request({
    url: '/project/bizPostManage/selectList',
    method: 'POST',
    data
  })
}


