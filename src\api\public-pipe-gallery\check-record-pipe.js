/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-08-07 15:51:56
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-08-07 15:52:31
 * @Description: 巡检记录
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */

import request, {cloud}  from '@/framework/utils/request'

// 查询巡检记录列表
export function listCheckRecordPipe(query) {
  return request({
    url: cloud.dqbasic +'/checkRecordPipe/page',
    method: 'get',
    params: query
  })
}

// 查询巡检记录详细
export function getCheckRecordPipe(id) {
  return request({
    url: cloud.dqbasic +'/checkRecordPipe/detail?id=' + id,
    method: 'get'
  })
}

// 新增巡检记录
export function addCheckRecordPipe(data) {
  return request({
    url: cloud.dqbasic +'/checkRecordPipe/add',
    method: 'post',
    data: data
  })
}

// 修改巡检记录
export function updateCheckRecordPipe(data) {
  return request({
    url: cloud.dqbasic +'/checkRecordPipe/edit',
    method: 'post',
    data: data
  })
}

// 删除巡检记录
export function delCheckRecordPipe(id) {
  return request({
    url: cloud.dqbasic +'/checkRecordPipe/delete',
    method: 'post',
    data: { ids: id }
  })
}
