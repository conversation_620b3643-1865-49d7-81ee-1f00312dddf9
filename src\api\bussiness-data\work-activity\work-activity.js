/*
 * @Author: 高宇 <EMAIL>
 * @Date: 2024-05-10 14:22:39
 * @LastEditors: 高宇 <EMAIL>
 * @LastEditTime: 2024-05-10 14:23:06
 * @FilePath: \isrmp_vue\src\api\work-activity\work-activity.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/framework/utils/request'

// 查询作业活动管理列表
export function bizWorkActivityPage(query) {
  return request({
    url: '/project/bizWorkActivity/page',
    method: 'get',
    params: query
  })
}

// 查询作业活动管理详细
export function bizWorkActivityDetail(params) {
  return request({
    url: '/project/bizWorkActivity/detail',
    method: 'get',
    params
  })
}

// 新增作业活动管理
export function bizWorkActivityAdd(data) {
  return request({
    url: '/project/bizWorkActivity/add',
    method: 'post',
    data: data
  })
}

// 修改作业活动管理
export function bizWorkActivityEdit(data) {
  return request({
    url: '/project/bizWorkActivity/edit',
    method: 'post',
    data: data
  })
}

// 删除作业活动管理
export function bizWorkActivityDel(activityId) {
  return request({
    url: '/project/bizWorkActivity/delete',
    method: 'post',
    data: { activityIds: activityId }
  })
}
