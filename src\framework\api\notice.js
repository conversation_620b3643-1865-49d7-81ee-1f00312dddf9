/*
 * @Author: gaoyu <EMAIL>
 * @Date: 2025-03-26 09:28:07
 * @LastEditors: gaoyu <EMAIL>
 * @LastEditTime: 2025-04-21 17:08:20
 * @Description: 
 * Copyright (c) 2025-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import { default as request, cloud } from '@/framework/utils/request'

const getNoticeList = (params) => request({ url: `${cloud.notice}/sysMessageCenter/getNoticePage/V2`, method: 'get', params })
const getUserTree = (params) => request({ url: `${cloud.usercenter}/sysUser/getUserSelectTree/v2`, method: 'get', params })
const addNotice = (params) => request({ url: `${cloud.notice}/sysMessageCenter/sendNotice`, method: 'post', data: params })
const deleteNotice = (params) => request({ url: `${cloud.notice}/sysNotice/delete`, method: 'post', data: params })
const getNoticeDetail = (params) => request({ url: `${cloud.notice}/sysNotice/detail`, method: 'get', params })
const getDictList = (params) => request({ url: `${cloud.dqbasic}/dict/page`, method: 'get', params })
const editNotice = (params) => request({ url: `${cloud.notice}/sysNotice/edit`, method: 'post', data: params })
const setTime = (params) => request({ url: `${cloud.dqbasic}/sysMessageCenter/setTime`, method: 'post', data: params })

export {
  getNoticeList,
  getUserTree,
  addNotice,
  deleteNotice,
  getNoticeDetail,
  getDictList,
  editNotice,
  setTime
}
// 查询日志详情
export function noticeDetail(messageId) {
  return request({
    url: `${cloud.notice}/sysMessageCenter/detail?messageId=${messageId}`,
    method: 'get'
  })
}
