<template>
  <div class="">
    <el-dialog
      top="10vh"
      width="70%"
      v-bind="$attrs"
      :close-on-click-modal="false"
      v-on="$listeners"
      @closed="handleClosed"
    >
      <el-form
        ref="ruleForm"
        inline
        :model="courseForm"
        :rules="rules"
        :label-width="labelWidth"
        :style="{'--label-width': labelWidth}"
        label-position="right"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="课程名称" prop="videoName">
              <el-input
                v-if="type != 'check'"
                v-model="courseForm.videoName"
                placeholder="请输入课程名称"
                maxlength="30"
                show-word-limit
                class="limit"
              />

              <span v-else>
                {{ courseForm.videoName }}
              </span>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="课程类型" required prop="resourceType">
              <el-select
                v-if="type != 'check'"
                v-model="courseForm.resourceType"
                placeholder="请选择"
                @change="handleTrainingTypeChange"
              >
                <el-option label="视频" value="0" />

                <el-option label="图文" value="1" />
              </el-select>

              <span v-else>
                {{ courseForm.resourceType=='0'?'视频':'图文' }}
              </span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="课程栏目" required prop="courseGrouping">
              <el-select
                v-if="type != 'check'"
                v-model="courseForm.courseGrouping"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in groupOptions"
                  :key="item.dictCode"
                  :label="item.dictName"
                  :value="item.dictCode"
                />
              </el-select>

              <span v-else>
                {{ getLabelByKey(courseForm.courseGrouping, groupOptions) }}
              </span>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item
              label="课程描述"
              prop="remark"
            >
              <el-input
                v-if="type !== 'check'"
                v-model.trim="courseForm.remark"
                maxlength="200"
                show-word-limit
              />

              <span v-else>
                {{ courseForm.remark }}
              </span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item required prop="classHour" label="课时">
              <span v-if="type === 'check'">
                {{ courseForm.classHour }}
              </span>

              <el-input-number
                v-else
                v-model.number="courseForm.classHour"
                :controls="false"
                :precision="1"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="14">
            <el-form-item label="课程文件" prop="videoPath">
              <AliyunUpload
                v-if="courseForm.resourceType === '0'"
                ref="videoUploadRef"
                v-model="courseForm.videoPath"
                :disabled="type === 'check'"
                :player-config="playerConfig"
                @upload-start="handleUploadStart"
                @upload-succeed="handleUploadSucceed"
                @duration-loaded="handleDurationLoaded"
              />

              <FileUploadEcho
                v-else
                style="width: 100%;"
                :disabled="type == 'check'"
                :show-preview="true"
                :file-id.sync="courseForm.videoPath"
                :file-limit="1"
                :file-size="0"
                :is-show-tip="true"
                :file-type="[
                  'pdf'
                ]"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!--            课程类型：视频    -->
        <el-row v-if="courseForm.resourceType === '0'">
          <el-form-item label="学习试题">
            <el-table
              border
              :data="courseForm.questionList"
              :header-cell-style="{ backgroundColor: '#f2f2f2' }"
            >
              <el-table-column
                label="答题时间"
                prop="second"
                :formatter="questionTimeFormatter"
                align="center"
                width="100"
              />

              <el-table-column
                label="试题"
                prop="content"
                align="center"
              />

              <el-table-column
                label="题型"
                prop="type"
                :formatter="questionTypeFormatter"
                align="center"
                width="90"
              />

              <el-table-column
                v-if="type !== 'check'"
                label="操作"
                align="center"
                width="160"
              >
                <template #default="scope">
                  <el-button type="text" @click="handleEditQuestionInList(scope)">
                    选择试题
                  </el-button>

                  <el-button type="text" @click="handleDeleteQuestion(scope.$index)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-row>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button
          v-if="type != 'check'"
          type="primary"
          :loading="isLoading"
          @click="handleSaveData(false)"
        >
          保存
        </el-button>

        <el-button
          v-if="type != 'check'"
          type="primary"
          :loading="isLoading"
          @click="handleSaveData(true)"
        >
          保存并启用
        </el-button>

        <el-button
          :disabled="isLoading"
          @click="handleCancel"
        >
          取 消
        </el-button>
      </span>

      <QuestionPickerModal ref="questionPickerModalRef" />
    </el-dialog>
  </div>
</template>

<script>
import FileUploadEcho from '@/components/file-upload-echo/file-upload-echo'
import {
  addTrainingVideo,
  editTrainingVideo
} from '@/api/training-examination/training-course'
import AliyunUpload from '@/components/aliyun-upload'
import QuestionPickerModal from './question-picker-modal'
import QuestionPickerButtonComponent from './player-components/QuestionPickerButtonComponent'
import './player-components/questionPicker.scss'
import { questionTypeFormatter, questionTimeFormatter } from '@/utils/question'

export default {
  name: 'CourseModal',
  components: {
    FileUploadEcho,
    AliyunUpload,
    QuestionPickerModal
  },

  props: {
    /** 判断弹框信息为新增、编辑和查看 */
    type: {
      type: String,
      required: true,
      default: ''
    }
  },

  data() {
    const validateVideoPath = (rule, value, callback) => {
      console.log('validate uploading', this.videoUploading)
      if (this.videoUploading) {
        return callback('请等待视频上传完毕')
      } else if (!value) {
        return callback('请上传课程文件')
      } else {
        return callback()
      }
    }

    return {
      labelWidth: '100px',
      videoUploading: false,
      isLoading: false,
      // 课程分组下拉选
      groupOptions: [],
      // 视频时长
      videoDuration: 0,
      courseForm: {
        id: '',
        videoName: '',
        resourceType: '0', // 课程默认类型:视频
        courseGrouping: '',
        videoPath: '',
        // bannerPath: '',
        remark: '',
        classHour: '',
        questionList: []
      },

      fileType: ['jpg', 'png'],
      rules: {
        videoPath: [
          { required: true, validator: validateVideoPath, trigger: 'change' }
        ],

        // bannerPath: [
        //   { required: true, message: '请选择课程封面', trigger: 'change' }
        // ],

        // remark: [
        //   { required: true, message: '请输入课程描述', trigger: 'blur' }
        // ],

        classHour: [
          { required: true, message: '请填写课时', trigger: 'blur' }
        ],

        courseGrouping: [
          { required: true, message: '请选择课程栏目', trigger: 'change' }
        ],

        resourceType: [
          { required: true, message: '请选择课程类型', trigger: 'change' }
        ],

        videoName: [
          { required: true, message: '请填写课程名称', trigger: 'blur' }
        ]
      },

      fileList: []
    }
  },

  computed: {
    playerConfig() {
      const playerConfig = { components:[] }
      if (this.type !== 'check') {
        playerConfig.components.push(
          {
            name: 'QuestionPickerButtonComponent',
            type: QuestionPickerButtonComponent,
            args: [{
              onPickQuestion: this.handlePickQuestion,
              pauseOnAddQuestion: true
            }]
          }
        )
      }

      return playerConfig
    }
  },

  watch: {
    // 'courseForm.videoPath': {
    //   handler(val, oldVal) {
    //     if (shouldResetQuestionList(val, oldVal)) {
    //       this.courseForm.questionList = []
    //     }
    //   },

    //   immediate: true
    // }
  },

  created() {
    this.businessDictList({ dictTypeCode: 'courseGrouping' }).then((res) => {
      this.groupOptions = res.data.rows
    })
  },

  methods: {
    handleCancel() {
      this.$emit('update:visible', false)
    },

    handleDurationLoaded(duration) {
      this.videoDuration = duration
      console.log('duration', duration)
    },

    // shouldResetQuestionList(newVideo, oldVideo) {
    //   if (this.type === 'check') return false
    //   // 首次上传视频
    //   if (this.type === 'add' && !oldVideo && newVideo) return false
    //   // 上传新视频
    //   if (this.type === 'edit' && !this.courseForm.questionList.length) return false
    //   if (oldVideo !== newVideo) return true
    //   return false
    // },

    handlePickQuestion(second) {
      console.log('this 指向', this)
      // 1. 检查当前时间点是否已存在试题
      const existingQuestionAtTime = this.courseForm.questionList.find(
        (q) => Math.floor(q.second) === Math.floor(second)
      )

      // 2. 如果已存在试题，则提示用户并终止操作
      if (existingQuestionAtTime) {
        this.$message.warning(
          `时间点 ${this.questionTimeFormatter(second)} 处已存在试题 "${existingQuestionAtTime.content}"，请先删除或选择其他时间点。`
        )
        return // 阻止后续的试题选择和添加流程
      }

      this.$refs.questionPickerModalRef.pick()
        .then((question) => {
          this.courseForm.questionList.push({
            questionId: question.id,
            second,
            content: question.content,
            type: question.quesType
          })
        })
        .catch(() => {
          console.log('操作取消')
        })
    },

    handleEditQuestionInList(scope) {
      const indexToReplace = scope.$index
      const originalQuestionInList = this.courseForm.questionList[indexToReplace]

      this.$refs.questionPickerModalRef.pick()
        .then((newlyPickedQuestion) => {
          const updatedQuestionForList = {
            questionId: newlyPickedQuestion.id,
            second: originalQuestionInList.second,
            content: newlyPickedQuestion.content,
            type: newlyPickedQuestion.quesType
          }

          this.courseForm.questionList.splice(indexToReplace, 1, updatedQuestionForList)
        })
        .catch((e) => {
          console.log('替换试题操作已取消', e)
        })
    },


    handleDeleteQuestion(index) {
      this.courseForm.questionList.splice(index, 1)
    },

    handleClosed() {
      this.videoUploading = false
      if (this.$refs.videoUploadRef) {
        this.$refs.videoUploadRef.reset()
      }
      this.courseForm = {
        id: '',
        videoName: '',
        resourceType: '0',
        courseGrouping: '',
        videoPath: '',
        bannerPath: '',
        remark: '',
        classHour: '',
        questionList: []
      }
      this.$refs.ruleForm.resetFields()
    },

    getOutOfBoundsQuestions() {
      return this.courseForm.questionList.filter((q) => q.second > this.videoDuration)
    },

    /** 保存按钮操作 */
    handleSaveData(isCourseEnabled) {
      const invalidQuestions = this.getOutOfBoundsQuestions()
      if (invalidQuestions.length) {
        const content = invalidQuestions.map((q) => this.questionTimeFormatter(q.second)).join(', ')
        this.$message.error(`试题时间点不能大于视频时长: ${content}`)
        return
      }

      const api = this.type === 'add'
        ? addTrainingVideo
        : editTrainingVideo
      const messageText = this.type === 'add'
        ? '新增'
        : '修改'

      this.$refs.ruleForm.validate((valid) => {
        if (!valid) return false

        this.isLoading = true
        this.courseForm.status = isCourseEnabled ? 1 : 2
        api(this.courseForm).then((res) => {
          if (res.success == true) {
            this.$message({
              message: `${messageText}成功`,
              type: 'success'
            })
            this.$refs.ruleForm.resetFields()
            this.$emit('refresh')
            this.$emit('update:visible', false)
          }
        }).finally(() => {
          this.isLoading = false
        })
      })
    },

    /** 根据key显示对应label */
    getLabelByKey(key, list) {
      const item = list.find((v) => v.dictCode == key)
      if (item) {
        return item.dictName
      } else {
        return ''
      }
    },

    handleUploadStart() {
      // this.$message('开始上传')
      this.videoUploading = true
    },

    handleUploadSucceed() {
      this.$message.success('上传成功')
      this.videoUploading = false
    },

    handleTrainingTypeChange() {
      this.courseForm.questionList = []
      this.courseForm.videoPath = ''
    },

    questionTypeFormatter,
    questionTimeFormatter
  }
}
</script>

<style lang="scss" scoped>
.el-form-item {
  width: 100%;
  ::v-deep .el-form-item__content {
    width: calc(100% - var(--label-width));
    > :is(.el-input, .el-select ) {
      width: 100%;
      .el-input__inner {
        text-align: left;
      }
    }
  }
}
</style>
