/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-14 14:51:15
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-05-18 10:19:33
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud}  from '@/framework/utils/request'

// 查询人员体检计划列表
export function listHealthPlanUser(query) {
  return request({
    url: cloud.dqbasic + '/healthPlanUser/page',
    method: 'get',
    params: query
  })
}

// 查询人员体检计划详细
export function getHealthPlanUser(id) {
  return request({
    url: cloud.dqbasic + '/healthPlanUser/detail?id=' + id,
    method: 'get'
  })
}

// 新增人员体检计划
export function addHealthPlanUser(data) {
  return request({
    url: cloud.dqbasic + '/healthPlanUser/add',
    method: 'post',
    data: data
  })
}

// 修改人员体检计划
export function updateHealthPlanUser(data) {
  return request({
    url: cloud.dqbasic + '/healthPlanUser/edit',
    method: 'post',
    data: data
  })
}

// 删除人员体检计划
export function delHealthPlanUser(id) {
  return request({
    url: cloud.dqbasic + '/healthPlanUser/delete',
    method: 'post',
    data: { ids: id }
  })
}
