import { default as request, cloud } from '@/framework/utils/request'

export default ({
  fetchPage(params) {
    return request({
      url: `${cloud.dqbasic}/sysConfig/page`,
      method: 'get',
      params
    })
  },
  add(data) {
    return request({
      url: `${cloud.dqbasic}/sysConfig/add`,
      method: 'post',
      data
    })
  },
  detail(id) {
    return request({
      url: `${cloud.dqbasic}/sysConfig/detail`,
      method: 'get',
      params: {
        configId: id
      }
    })
  },
  delete(data) {
    return request({
      url: `${cloud.dqbasic}/sysConfig/delete`,
      method: 'post',
      data
    })
  },
  edit(data) {
    return request({
      url: `${cloud.dqbasic}/sysConfig/edit`,
      method: 'post',
      data
    })
  }

})
