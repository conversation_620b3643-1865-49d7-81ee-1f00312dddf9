<template>
  <div>
    <el-dialog
      title="考试结果"
      :visible.sync="dialogVisible"
      width="80%"
      :before-close="handleClose"
      top="10vh"
    >
      <el-form
        ref="queryForm"
        :model="queryParams"
        label-width="70px"
        inline
        @submit.native.prevent
      >
        <el-form-item label="姓名" prop="userName">
          <el-input
            v-model.trim="queryParams.userName"
            maxlength="30"
            placeholder="请输入"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="考试状态" prop="examStatus">
          <el-select
            v-model="queryParams.examStatus"
            placeholder="请选择"
            clearable
            style="width: 100%;"
          >
            <el-option label="未考" value="0" />

            <el-option label="已考" value="1" />
          </el-select>
        </el-form-item>

        <el-form-item label="部门" prop="orgId">
          <DeptSelect v-model="queryParams.orgId" placeholder="请选择" />
        </el-form-item>

        <div class="fr">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            搜索
          </el-button>

          <el-button icon="el-icon-refresh" @click="handleReset">
            重置
          </el-button>

          <el-button type="primary" @click="exportOut">
            导出
          </el-button>
        </div>
      </el-form>

      <el-table
        v-loading="loading"
        style="width: 100%;"
        border
        highlight-current-row
        :header-cell-style="{ backgroundColor: '#f2f2f2' }"
        :data="tableData"
      >
        <template slot="empty">
          <p>{{ $store.getters.dataText }}</p>
        </template>

        <el-table-column
          type="index"
          label="序号"
          width="70"
          :index="
            (index) =>
              (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
          "
        />

        <el-table-column
          label="姓名"
          show-overflow-tooltip
          align="center"
          prop="userName"
        />

        <el-table-column
          label="所在部门"
          show-overflow-tooltip
          align="center"
          prop="orgName"
        />

        <el-table-column
          label="考试状态"
          show-overflow-tooltip
          align="center"
          prop="examCount"
          min-width="60"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.examCount > 0">
              已考
            </span>

            <span v-else>
              未考
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="开始答题时间"
          show-overflow-tooltip
          align="center"
          prop="startTime"
          min-width="120"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.startTime == null">
              --
            </span>

            <span v-else>
              {{ scope.row.startTime }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="结束答题时间"
          show-overflow-tooltip
          align="center"
          prop="endTime"
          min-width="120"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.endTime == null">
              --
            </span>

            <span v-else>
              {{ scope.row.endTime }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="时长"
          align="center"
          prop="examTime"
          min-width="60"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.examTime == null">
              --
            </span>

            <span v-else>
              {{ getFormattedUseTime(scope.row.examTime) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="分数"
          align="center"
          prop="highestScore"
          min-width="60"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.highestScore == null">
              --
            </span>

            <span v-else>
              {{ scope.row.highestScore }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          width="100"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="handleAnswerDetail(scope.row)">
              答题详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <dt-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNo"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-dialog>

    <!-- 答题详情弹框 -->
    <OnlineAnswerDialog ref="onlineAnswerRef" />
  </div>
</template>

<script>
import { getFormattedUseTime } from '@/utils/paper'
import {
  getExamOnlineResult,
  exportExamOnlineResult
} from '@/api/exam-manage/organize-exam'
import DeptSelect from '@/components/dept-select/dept-select.vue'
import OnlineAnswerDialog from './online-answer-dialog.vue'


export default {
  components: { DeptSelect, OnlineAnswerDialog },
  data() {
    return {
      // 弹框
      dialogVisible: false,

      row:{},

      // 表格
      total:0,
      tableData:[],
      loading:false,
      queryParams:{
        userIds:'', // 考试人
        examId:'', // 考试id
        userName:'', // 姓名
        examStatus:'', // 考试状态
        orgId:'', // 部门
        pageNo:1,
        pageSize:10
      }
    }
  },

  methods: {
    getFormattedUseTime,

    // 初始化
    init(row) {
      this.row = row
      this.queryParams.userIds = row.userIds
      this.queryParams.examId = row.examId
      this.handleQuery()
      this.dialogVisible = true
    },

    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 重置
    handleReset() {
      this.queryParams = {
        userIds: this.row.userIds, // 考试人
        examId: this.row.examId, // 考试id
        userName:'', // 姓名
        examStatus:'', // 考试状态
        orgId:'', // 部门
        pageNo:1,
        pageSize:10
      }
      this.getList()
    },

    // 获取列表
    getList() {
      const query = JSON.parse(JSON.stringify(this.queryParams))
      this.loading = true
      getExamOnlineResult(query).then((data) => {
        this.loading = false
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      }).catch(() => {
        this.loading = false
      })
    },

    // 导出
    exportOut() {
      const data = JSON.parse(JSON.stringify(this.queryParams))
      exportExamOnlineResult(data).then((res) => {
        const blob = new Blob([res.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const objectUrl = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = objectUrl // 指定下载链接
        a.download = '组织考试结果（线上）' // 指定下载文件名
        a.click()
        URL.revokeObjectURL(a.href) // 释放URL对象
      })
    },

    // 答题详情
    handleAnswerDetail(row) {
      this.$refs.onlineAnswerRef.init(row)
    },

    // 关闭弹框
    handleClose() {
      this.queryParams = {
        userIds:'', // 考试人
        examId:'', // 考试id
        userName:'', // 姓名
        examStatus:'', // 考试状态
        orgId:'', // 部门
        pageNo:1,
        pageSize:10
      }
      this.dialogVisible = false
    }
  }
}
</script>
