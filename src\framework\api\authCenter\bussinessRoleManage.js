import { default as request, cloud } from '@/framework/utils/request'

// 角色分页列表接口
export function rolePage(params) {
  return request({
    url: `${cloud.permission}/businessRole/rolePage`,
    method: 'get',
    params
  })
}
// 查询所有第三方应用列表
export function getCurrAdminGroupApps(params) {
  return request({
    url: `${cloud.permission}/businessRole/getCurrAdminGroupApps`,
    method: 'get',
    params
  })
}
// 业务角色详情/编辑展示接口
export function userDetail(params) {
  return request({
    url: `${cloud.permission}/businessRole/detail`,
    method: 'get',
    params
  })
}
// 查询角色已选中功能权限列表
export function getRoleFunctionChooseTree(params) {
  return request({
    url: `${cloud.permission}/businessRole/getRoleFunctionChooseTree`,
    method: 'get',
    params
  })
}
// 已绑定该角色用户列表查询
export function getRoleUserPage(params) {
  return request({
    url: `${cloud.permission}/businessRole/getRoleUserPage`,
    method: 'get',
    params
  })
}
// 在全量组织树中搜索组织名称，返回平级列表
export function searchOrgName(params) {
  return request({
    url: `${cloud.permission}/managerGroup/searchOrgName`,
    method: 'get',
    params: {
      orgName: params.name
    }
  })
}
// 在全量组织树中搜索用户名称，返回平级列表
export function searchUserName(params) {
  return request({
    url: `${cloud.permission}/businessRole/searchUserNameForRole`,
    method: 'get',
    params: {
      nameOrAccount: params.name,
      roleId: params.roleId
    }
  })
}
// 删除/批量删除用户
export function batchDeleteUsers(data) {
  return request({
    url: `${cloud.permission}/businessRole/batchDeleteUsers`,
    method: 'post',
    data
  })
}
// 新增业务角色
export function addRole(data) {
  return request({
    url: `${cloud.permission}/businessRole/add`,
    method: 'post',
    data
  })
}
// 修改业务角色
export function updateRole(data) {
  return request({
    url: `${cloud.permission}/businessRole/edit`,
    method: 'post',
    data
  })
}
// 业务角色启用禁用
export function updteStatus(data) {
  return request({
    url: `${cloud.permission}/businessRole/updteStatus`,
    method: 'post',
    data
  })
}
//   删除业务角色
export function deleteRole(data) {
  return request({
    url: `${cloud.permission}/businessRole/deleteRole`,
    method: 'post',
    data
  })
}
// 查询角色全部功能权限列表
export function getParentGroupFunction(params) {
  return request({
    url: `${cloud.permission}/businessRole/getRoleFunctionTree`,
    method: 'get',
    params
  })
}
// 授权用户保存
export function grantRoleUsers(data) {
  return request({
    url: `${cloud.permission}/businessRole/grantRoleUsers`,
    method: 'post',
    data
  })
}

// 查询当前角色可选的数据范围
export function getParentGroupDataScope(params) {
  return request({
    url: `${cloud.permission}/businessRole/getRoleDataScopeChildren`,
    method: 'get',
    params
  })
}

// 获取当前用户的角色功能范围和数据权限
export function getRoleAllAuthScope(data) {
  return request({
    url: `${cloud.permission}/businessRole/getRoleAllAuthScope`,
    method: 'post',
    data
  })
}
// 下载三方角色导入模板
export function downloadTemplate(params) {
  return request({
    url: `${cloud.permission}/businessRole/downloadTemplate`,
    method: 'get',
    params: {
      enclosure: '三方角色导入模板.xls'
    },
    responseType: 'arraybuffer'
  })
}
// 三方角色导入
export function uploadRoles(data) {
  return request({
    url: `${cloud.permission}/businessRole/uploadRoles`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 查询
export function getManagingOrgUserList(params) {
  return request({
    url: `${cloud.permission}/businessRole/getRoleOrgUserList`,
    method: 'get',
    params
  })
}
// export default ({
//   // 查询
//   getManagingOrgUserList(params) {
//     return request({
//       url: cloud.permission + '/businessRole/getRoleOrgUserList',
//       method: 'get',
//       params
//     })
//   }
// })
