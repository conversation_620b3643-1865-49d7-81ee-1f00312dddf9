import { default as request, cloud } from '@/framework/utils/request'

export function fetchList(query) {
  return request({
    url: `${cloud.dqbasic}/vue-element-admin/article/list`,
    method: 'get',
    params: query
  })
}

export function fetchArticle(id) {
  return request({
    url: `${cloud.dqbasic}/vue-element-admin/article/detail`,
    method: 'get',
    params: { id }
  })
}

export function fetchPv(pv) {
  return request({
    url: `${cloud.dqbasic}/vue-element-admin/article/pv`,
    method: 'get',
    params: { pv }
  })
}

export function createArticle(data) {
  return request({
    url: `${cloud.dqbasic}/vue-element-admin/article/create`,
    method: 'post',
    data
  })
}

export function updateArticle(data) {
  return request({
    url: `${cloud.dqbasic}/vue-element-admin/article/update`,
    method: 'post',
    data
  })
}
