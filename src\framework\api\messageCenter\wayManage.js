import { default as request, cloud } from '@/framework/utils/request'

// 查询渠道列表
export function sysMessageChannelPage(query) {
  return request({
    url: `${cloud.notice}/sysMessageChannel/page/V2`,
    method: 'get',
    params: query
  })
}

// 查询渠道详细
export function sysMessageChannelDetail(channelId) {
  return request({
    url: `${cloud.notice}/sysMessageChannel/detail?channelId=${channelId}`,
    method: 'get'
  })
}

// 新增渠道
export function addSysMessageChannel(data) {
  return request({
    url: `${cloud.notice}/sysMessageChannel/add`,
    method: 'post',
    data
  })
}

// 修改渠道
export function updateSysMessageChannel(data) {
  return request({
    url: `${cloud.notice}/sysMessageChannel/edit`,
    method: 'post',
    data
  })
}

// 删除渠道
export function sysMessageChannelDel(channelId) {
  return request({
    url: `${cloud.notice}/sysMessageChannel/delete`,
    method: 'post',
    data: { channelId }
  })
}

// 修改渠道状态
export function updateStatus(data) {
  return request({
    url: `${cloud.notice}/sysMessageChannel/updateStatus`,
    method: 'post',
    data
  })
}

// 获取渠道类型
export function sysMessageChannelType(query) {
  return request({
    url: '/sysMessageChannelType/page',
    method: 'get',
    params: query
  })
}

// 获取sdk
export function getSdk(query) {
  return request({
    url: `${cloud.notice}/sysMessageChannelParameter/getSdk`,
    method: 'get',
    params: query
  })
}

// 获取渠道参数
export function getParameter(query) {
  return request({
    url: `${cloud.notice}/sysMessageChannelParameter/getParameter`,
    method: 'get',
    params: query
  })
}
