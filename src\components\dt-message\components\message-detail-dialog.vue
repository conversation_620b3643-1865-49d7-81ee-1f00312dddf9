<!--
  * @Author: gaoyu <EMAIL>
  * @Date: 2025-04-07 09:20:23
  * @LastEditors: gaoyu <EMAIL>
  * @LastEditTime: 2025-04-10 19:55:35
  * @Description:消息详情弹窗
  * Copyright (c) 2025-present HBIS Digital Technology Co.,Ltd. All rights reserved.
-->
<template>
  <dtDialog
    ref="dialogPopupRef"
    class="message-detail"
    :title="'通知详情'"
    :visible.sync="dialogFormVisible"
    :footer-slot="true"
    :footer-hidden="true"
    :screen-hidden="true"
    :modal-append-to-body="true"
    :append-to-body="true"
  >
    <div slot="content">
      <div class="message-body">
        <div class="title">
          {{ ruleForm.messageTitle }}
        </div>

        <div class="des">
          <div class="left-box">
            <div class="level">
              {{ ruleForm.priorityLevel=='1'?'高级': ruleForm.priorityLevel=='2'?'中级':ruleForm.priorityLevel=='3'?'低级':'' }}
            </div>

            <div>
              发送人员：{{ ruleForm.sender }}
            </div>
          </div>

          <div class="date">
            {{ ruleForm.scheduledSendTime }}
          </div>
        </div>

        <div class="content">
          {{ ruleForm.messageContent }}
        </div>
      </div>
    </div>
  </dtDialog>
</template>

<script>
import {
  getReadDetail,
  yesOrNotUnReadMessage
} from '@/framework/api/message'
import meetingApi from '@/framework/api/meeting'
import store from '@/store'
import { rule } from 'postcss'
export default {
  name: 'MessageDetailDialog',
  data() {
    return {
      dialogFormVisible: false,
      ruleForm: {},
      messageId: this.$messageDialog.messageId
    }
  },

  watch: {
    dialogFormVisible: {
      handler(newVal, oldVa) {
        if (newVal) {
          if (this.messageId) {
            this.open2()
          }
        }
      }
    }
  },

  methods: {
    /**
     * @description: 打开详情弹窗
     * @param {*} item
     */
    open1(item) {
      const params = {}
      if (item) {
        if (item.messageType === 'meeting') {
          meetingApi.getMeetingById(item.messageContent).then((res) => {
            meetingApi
              .getMeetingTopicByType(res.data.meetingType)
              .then((resMt) => {
                this.meetingTopicList = resMt.data
                if (
                  res.data.participants.length === this.participantList.length
                ) {
                  this.checkedAll = true
                }
                this.meetingModel = res.data
              })
          })
        } else {
          params.messageId = item.messageId
        }
      } else {
        params.messageId = this.messageId
      }
      getReadDetail(params).then((res) => {
        if (res.code === '00000') {
          this.$parent.initUnReadMessage()
          this.$parent.changeList()
          setTimeout(() => {
            this.ruleForm = res.data
            this.$forceUpdate()
          }, 50)
        }
      })
    },

    /**
     * @description: 获取详情，打开详情弹窗
     */
    open2() {
      this.dialogFormVisible = true
      getReadDetail({
        messageId: this.messageId
      }).then((res) => {
        if (res.code === '00000') {
          this.initUnReadMessage()
          setTimeout(() => {
            this.ruleForm = res.data
            this.$forceUpdate()
          }, 50)
        }
      })
    },

    /**
     * @description: 获取未读数据
     */
    initUnReadMessage() {
      yesOrNotUnReadMessage()
        .then((res) => {
          store.dispatch('websocket/setIsUnReadMessage', res.data)
        })
    }
  }

}
</script>

  <style lang="scss" scoped>
  @import "~@/framework/styles/variables.scss";

  .message-detail {
    .message-body {
      display: flex;
      flex-direction: column;
      min-height: 490px;

      .title {
        margin-bottom: 8px;
        color: #131314;
        font-weight: 500;
        font-size: 18px;
        font-family: "HarmonyOS Sans SC-Medium", "HarmonyOS Sans SC";
        line-height: 24px;
        text-align: center;
      }

      .des {
        display: flex;
        justify-content: space-between;
        margin-bottom: 24px;
        padding-bottom: 4px;
        color: #606266;
        font-size: 12px;
        line-height: 24px;
        border-bottom: 1px solid #d8d8d8;

        .left-box {
          display: flex;

          .level {
            margin-right: 16px;
          }
        }
      }
    }

    ::v-deep .el-dialog__body {
      padding-top: 0;

    }

  }
</style>
