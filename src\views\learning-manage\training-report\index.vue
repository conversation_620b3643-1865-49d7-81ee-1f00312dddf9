<template>
  <div class="app-container">
    <div v-if="!showDetail">
      <div class="mainbox">
        <div class="filter-container">
          <el-form
            ref="queryForm"
            inline
            :model="queryParams"
            label-width="100px"
            @submit.native.prevent
          >
            <el-form-item label="培训名称">
              <el-input v-model="queryParams.trainName" />
            </el-form-item>

            <el-form-item label="培训方式">
              <el-select v-model="queryParams.online">
                <el-option
                  v-for="item in trainingMethodOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="培训类型">
              <el-select v-model="queryParams.trainType" clearable>
                <el-option
                  v-for="item in trainingTypeOptions"
                  :key="item.id"
                  :value="item.dictCode"
                  :label="item.dictName"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="培训时间">
              <el-date-picker
                v-model="trainingTime"
                type="datetimerange"
                start-placeholder="请选择"
                end-placeholder="请选择"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
              />
            </el-form-item>

            <el-form-item label="创建部门">
              <DeptSelect v-model="queryParams.orgId" placeholder="请选择创建部门" />
            </el-form-item>

            <div class="fr">
              <el-button type="primary" icon="el-icon-search" @click="handleQuery">
                搜索
              </el-button>

              <el-button icon="el-icon-refresh" @click="handleReset">
                重置
              </el-button>
            </div>
          </el-form>
        </div>

        <div class="table-container table-fullscreen">
          <div class="table-opt-container">
            <!-- <el-button
              type="primary"
              icon="el-icon-plus"
              @click="handleAdd"
            >
              新增
            </el-button> -->

            <div class="flex-1" />
          </div>

          <el-table
            ref="table"
            v-loading="isLoading"
            style="width: 100%;"
            border
            :header-cell-style="{ backgroundColor: '#f2f2f2' }"
            :data="tableData"
          >
            <!-- <template slot="empty">
              <p>{{ $store.getters.dataText }}</p>
            </template> -->

            <el-table-column
              type="index"
              label="序号"
              width="70"
              fixed="left"
              :index="
                (index) =>
                  (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
              "
            />

            <el-table-column
              key="trainName"
              label="培训名称"
              show-overflow-tooltip
              prop="trainName"
              width="130"
            >
            </el-table-column>

            <el-table-column
              key="online"
              label="培训方式"
              prop="online"
              :formatter="trainingMethodFormatter"
              show-overflow-tooltip
              width="90"
            />

            <el-table-column
              label="培训类型"
              prop="trainTypeName"
              show-overflow-tooltip
              width="140"
            />

            <el-table-column
              label="人员范围"
              prop="personnelScope"
              show-overflow-tooltip
              :formatter="personnelScopeFormatter"
              width="100"
            />

            <el-table-column
              label="培训人数"
              prop="planNumber"
              show-overflow-tooltip
              width="110"
            />

            <el-table-column
              label="培训时间"
              show-overflow-tooltip
              width="210"
              :formatter="trainingTimeFormatter"
            />

            <el-table-column
              label="实际培训人数"
              prop="totalStuff"
              show-overflow-tooltip
              width="110"
            />

            <el-table-column
              label="实际培训时间"
              show-overflow-tooltip
              width="210"
              :formatter="realTimeFormatter"
            />

            <el-table-column
              label="创建部门"
              prop="orgName"
              show-overflow-tooltip
              width="150"
            />

            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
              width="200"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  @click="handleViewDetail(scope.row)"
                >
                  查看报告
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <dt-pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNo"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </div>

    <!-- 详情视图 -->
    <TrainingReportForm 
      v-else
      :currentId="currentId" 
      @back="handleBackFromDetail"
    />
    
  </div>
</template>

<script>
import { getTrainRecordPage, getTrainingPlanById, getTrainingPlanEvaluation } from '@/api/learning-manage/training-report.js'
import {
  TRAINING_METHOD_OPTIONS,
  CONSTANTS
} from '@/constants/training-plan'
import dayjs from 'dayjs'
import DeptSelect from '@/components/dept-select/dept-select.vue'
import TrainingReportForm from './training-report-form.vue'

export default {
  name: 'TrainingReport',
  components: { DeptSelect, TrainingReportForm  },
  data() {
    return {
      // 常量对象
      CONSTANTS,

      queryParams: {
        trainName: '',
        online: '',
        trainType: '',
        searchBeginTime: '',
        searchEndTime: '',
        status: '',
        orgId: '',
        pageNo: 1,
        pageSize: 10
      },

      // 培训方式
      trainingMethodOptions: TRAINING_METHOD_OPTIONS,

      total: 0,
      isLoading: false,
      tableData: [],
      trainingTypeOptions: [],

      showDetail: false,
      currentId: null
    }
  },

  computed: {
    trainingTime: {
      set(value) {
        if (value && value.length === 2) {
          this.queryParams.searchBeginTime = value[0]
          this.queryParams.searchEndTime = value[1]
        } else {
          this.queryParams.searchBeginTime = ''
          this.queryParams.searchEndTime = ''
        }
      },

      get() {
        if (this.queryParams.searchBeginTime && this.queryParams.searchEndTime) {
          return [this.queryParams.searchBeginTime, this.queryParams.searchEndTime]
        } else {
          return []
        }
      }
    }
  },

  created() {
    this.handleQuery()
    this.getDicts()
  },

  mounted() {

  },

  methods: {
    trainingTimeFormatter(row) {
      return `${row.trainBegin}~${row.trainEnd}`
    },
    realTimeFormatter(row) {
      return `${row.realBeginTime}~${row.realEndTime}`
    },
    trainingMethodFormatter(row, column, cellValue) {
      const item = this.trainingMethodOptions.find((item) => item.value === cellValue)
      return item ? item.label : cellValue
    },
    personnelScopeFormatter(row, column, cellValue) {
      return cellValue === CONSTANTS.PERSONNEL_SCOPE_EXTERNAL ? '相关方人员' : '公司人员'
    },

    getDicts() {
      this.businessDictList({ dictTypeCode: 'trainType' }).then((res) => {
        this.trainingTypeOptions = res.data.rows
      })
    },

    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    handleReset() {
      this.queryParams = {
        trainName: '',
        online: '',
        trainType: '',
        searchBeginTime: '',
        searchEndTime: '',
        status: '',
        orgId: '',
        pageNo: 1,
        pageSize: 10
      }
      this.getList()
    },

    getList() {
      this.isLoading = true
      getTrainRecordPage(this.queryParams).then((data) => {
        this.isLoading = false
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      })
    },

    handleViewDetail(row){
      // this.$router.push(`/trainingReportForm?id=${row.id}`)

      this.currentId = row.id
      this.showDetail = true
    },

    handleBackFromDetail(isrRefresh) {
      // 从详情返回列表
      this.showDetail = false
      this.currentId = null
      // 可选：返回时刷新列表数据
      if (isrRefresh)  this.getList()
    }

  }
}
</script>

<style lang="scss" scoped>

</style>
