import { default as request, cloud } from '@/framework/utils/request'

export default ({
  fetchPage(params) {
    return request({
      url: `${cloud.dqbasic}/databaseInfo/page`,
      method: 'get',
      params
    })
  },
  add(data) {
    return request({
      url: `${cloud.dqbasic}/databaseInfo/add`,
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: `${cloud.dqbasic}/databaseInfo/delete`,
      method: 'post',
      data
    })
  }

})
