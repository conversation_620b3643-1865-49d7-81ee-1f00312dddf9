/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-07-02 15:55:26
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-07-10 14:34:03
 * @FilePath: \isrmp_vue\src\api\related-management\company.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import { default as request, cloud } from '@/framework/utils/request'

// 查询相关方-公司列表
export function listCompany(query) {
  return request({
    url: cloud.dqbasic + '/relateCompany/page',
    method: 'get',
    params: query
  })
}

// 查询相关方-公司详细
export function getCompany(id) {
  return request({
    url: cloud.dqbasic + '/relateCompany/detail?id=' + id,
    method: 'get'
  })
}

// 新增相关方-公司
export function addCompany(data) {
  return request({
    url: cloud.dqbasic + '/relateCompany/add',
    method: 'post',
    data: data
  })
}

// 修改相关方-公司
export function updateCompany(data) {
  return request({
    url: cloud.dqbasic + '/relateCompany/edit',
    method: 'post',
    data: data
  })
}

// 删除相关方-公司
export function delCompany(id) {
  return request({
    url: cloud.dqbasic + '/relateCompany/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 获取相关方状态
export function getCompanyStatus() {
  return request({
    url: cloud.dqbasic + '/relateStatus',
    method: 'get'
  })
}

// 修改相关方状态
export function updateCompanyStatus(data) {
  return request({
    url: cloud.dqbasic + '/relateCompany/updateStatus',
    method: 'post',
    data: data
  })
}

/** 获取企业名称列表 */
export function getCompanyList() {
  return request({
    url: cloud.dqbasic + '/relateCompany/list',
    method: 'get'
  })
}

// 下载模板
export function downTemplate() {
  return request({
    url: cloud.dqbasic + '/relateCompany/exportTemplate',
    method: 'get',
    responseType: 'arraybuffer'
  })
}

// 导入
export function importTeamExcel(data) {
  return request({
    url: cloud.dqbasic + '/relateCompany/import',
    method: 'post',
    data
  })
}

// 导出
export function exportExcel(query) {
  return request({
    url: cloud.dqbasic + '/relateCompany/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}
