/*
 * @Author: miteng <EMAIL>
 * @Date: 2024-05-15 14:18:34
 * @LastEditors: miteng <EMAIL>
 * @LastEditTime: 2024-05-20 09:05:46
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'

// 查询业务--检查点模板列表
export function listCheckPointTemplate(query) {
  return request({
    url: '/project/bizCheckPointTemplate/page',
    method: 'get',
    params: query
  })
}

// 查询业务-检查点模板详细 id
export function getCheckPointTemplate(id) {
  return request({
    url: '/project/bizCheckPointTemplate/detail?id=' + id,
    method: 'get'
  })
}
// 查询业务-获取业务-检查点管理导入模板多选的详情 ids
export function getCheckPointTemplates(ids) {
  return request({
    url: '/project/bizCheckPointTemplate/detailByIds?ids=' + ids,
    method: 'get'
  })
}
// 新增业务-检查点模板
export function addCheckPointTemplate(data) {
  return request({
    url: '/project/bizCheckPointTemplate/add',
    method: 'post',
    data: data
  })
}

// 修改业务-检查点模板
export function updateCheckPointTemplate(data) {
  return request({
    url: '/project/bizCheckPointTemplate/edit',
    method: 'post',
    data: data
  })
}

// 删除业务-检查点模板
export function delCheckPointTemplate(id) {
  return request({
    url: '/project/bizCheckPointTemplate/delete',
    method: 'post',
    data: { ids: id }
  })
}
// 删除业务-检查点类目数据 （删除模版中每条数据）
export function delCheckPointItem(id) {
  return request({
    url: '/project/bizCheckPointItem/delete',
    method: 'post',
    data: { ids: id }
  })
}