<!--
  * @Author: wangzexin <EMAIL>
  * @Date: 2024-11-13 16:18:19
  * @LastEditors: gaoyu <EMAIL>
  * @LastEditTime: 2025-05-06 10:57:31
  * @Description:
  * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
-->
<template>
  <UEPlayer />
</template>

<script>
import { appLoad, apiRegister, apiSend } from '@/components/ue-player'
import { EventBus } from '@/utils/event-bus'
export default {
  name: 'UEContainer',
  data() {
    return {
      loaded: false
    }
  },

  watch: {
    '$route.path': {
      handler(newVal) {
        if (this.loaded) {
          setTimeout(() => {
            this.$emit('loaded')
          }, 200)
        }
      },

      immediate: false
    }

  },

  mounted() {
    // const url = window.UEIP // UE的服务器地址
    const url = `${location.hostname}:82`
    appLoad(url, () => {
      this.loaded = true
      this.$emit('loaded')

      // 监听UE发送过来的消息
      apiRegister('UEToHtml', (info) => {
        EventBus.$emit('getMessige', info)
      })
    })
  },

  methods: {
    sendToUE(params) {
      if (!this.loaded) return
      // 发送消息给UE
      apiSend('HtmlToUE', params, (res) => {
      })
    }
  }
}
</script>
