/*
 * @Author: wang<PERSON>xin <EMAIL>
 * @Date: 2024-07-09 14:23:10
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-15 10:09:25
 * @Description: 考试题库
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'
// 分页查询考试题库信息
export function getQuestionInfoList(query) {
  return request({
    url: '/project/trainingQuestion/page',
    method: 'get',
    params: query
  })
}
  
// 新增考试题库信息
export function addQuestionInfo(data) {
  return request({
    url: '/project/trainingQuestion/add',
    method: 'post',
    data: data
  })
}
// 删除考试题库信息
export function deleteQuestionInfo(data) {
  return request({
    url: '/project/trainingQuestion/delete',
    method: 'post',
    data: data
  })
}
// 获取题库信息单条数据详情
export function getQuestionInfoDetail(query) {
  return request({
    url: '/project/trainingQuestion/detail',
    method: 'get',
    params: query
  })
}
// 获取题库信息单条数据详情
export function exportTemplate(query) {
  return request({
    url: '/project/trainingQuestion/exportTemplate',
    method: 'get',
    params: query,
    responseType: 'arraybuffer'
  })
}
// 修改考试题库信息
export function editQuestionInfo(data) {
  return request({
    url: '/project/trainingQuestion/edit',
    method: 'post',
    data: data
  })
}

// 启用-停用操作
export function updateStatus(data) {
  return request({
    url: '/project/trainingQuestion/updateStatus',
    method: 'post',
    data: data
  })
}
// 导出数据
export function exportTable(query) {
  return request({
    url: '/project/trainingQuestion/export',
    method: 'get',
    params: query,
    responseType: 'arraybuffer'
  })
}
// 导入数据
export function importData(data) {
  return request({
    url: '/project/trainingQuestion/import',
    method: 'post',
    data: data
  })
}