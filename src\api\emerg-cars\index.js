/*
 * @Author: du<PERSON><PERSON> <EMAIL>
 * @Date: 2024-07-26 13:54:46
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-07-26 13:57:55
 * @FilePath: \isrmp_vue\src\api\emerg-cars\index.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import { default as request, cloud } from '@/framework/utils/request'
/** 应急车辆列表 */
export function getCarsList(params) {
  return request({
    url: cloud.dqbasic + '/emergVehicles/page',
    method: 'get',
    params
  })
}

/** 应急车辆详情 */
export function getIssue(id) {
  return request({
    url: cloud.dqbasic + '/emergVehicles/detail?id=' + id,
    method: 'get'
  })
}

// 应急车辆新增
export function add(data) {
  return request({
    url: cloud.dqbasic + '/emergVehicles/add',
    method: 'post',
    data: data
  })
}

// 应急车辆修改
export function edit(data) {
  return request({
    url: cloud.dqbasic + '/emergVehicles/edit',
    method: 'post',
    data: data
  })
}

// 应急车辆删除
export function del(id) {
  return request({
    url: cloud.dqbasic + '/emergVehicles/delete',
    method: 'post',
    data: { ids: id }
  })
}
