import { default as request, cloud } from '@/framework/utils/request'

// 低代码路由
export function getLowCodeAppMenuTree(query) {
  return request({
    url: `${cloud.devcenter}/sysApp/getLowCodeAppMenuTree`,
    method: 'get',
    params: query
  })
}

// 查询指定应用的左侧菜单树
export function getMenuListByAppCode(query) {
  return request({
    url: `${cloud.devcenter}/sysAcMenu/getMenuListByAppCode`,
    method: 'get',
    params: query
  })
}

// 新增表单
export function customFormInfoAdd(data) {
  return request({
    url: `${cloud.devcenter}/customFormInfo/add`,
    method: 'post',
    data
  })
}

// 拖拽排序
export function updateMenuOrder(data) {
  return request({
    url: `${cloud.devcenter}/sysAcMenu/updateMenuOrder`,
    method: 'post',
    data
  })
}

