/*
 * @Author: zzp
 * @Date: 2024-04-22 15:53:40
 * @LastEditors:
 * @LastEditTime: 2024-04-22 16:42:26
 * @FilePath: \isrmp_vue\src\api\HealthExaminePlan\health-examine-plan.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud} from '@/framework/utils/request'

// 查询职业健康体检计划列表
export function listHealthExaminePlan(query) {
  return request({
    url: cloud.dqbasic + '/healthExaminePlan/page',
    method: 'get',
    params: query
  })
}

// 查询职业健康体检计划详细
export function getHealthExaminePlan(id) {
  return request({
    url: cloud.dqbasic + '/healthExaminePlan/detail?id=' + id,
    method: 'get'
  })
}

// 发布职业健康体检计划
export function publishHealthExaminePlan(data) {
  return request({
    url: cloud.dqbasic + '/healthExaminePlan/publishAdd',
    method: 'post',
    data: data
  })
}

// 新增职业健康体检计划 -- 编辑
export function saveHealthExaminePlan(data) {
  return request({
    url: cloud.dqbasic + '/healthExaminePlan/draftSaveAndEdit',
    method: 'post',
    data: data
  })
}

// 修改职业健康体检计划
export function updateHealthExaminePlan(data) {
  return request({
    url: cloud.dqbasic + '/healthExaminePlan/draftSaveAndEdit',
    method: 'post',
    data: data
  })
}

// 删除职业健康体检计划
export function delHealthExaminePlan(id) {
  return request({
    url: cloud.dqbasic + '/healthExaminePlan/delete',
    method: 'post',
    data: { ids: id }
  })
}
