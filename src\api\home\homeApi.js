/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-07-05 11:07:05
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-29 10:23:32
 * @FilePath: \isrmp_vue\src\api\home\homeApi.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud}  from '@/framework/utils/request'

/** 隐患整改 */
export function dangerAnalyze() {
  return request({
    url: cloud.dqbasic + '/dangerAnalyze',
    method: 'get'
  })
}

/** 每月隐患统计 */
export function hiddenDangerAnalyze() {
  return request({
    url: cloud.dqbasic + '/hiddenDangerAnalyze',
    method: 'get'
  })
}

/** 部门任务执行情况 */
export function deptTask() {
  return request({
    url: cloud.dqbasic + '/deptTask',
    method: 'get'
  })
}

/** 每月任务执行情况 */
export function monthTask() {
  return request({
    url: cloud.dqbasic + '/monthTask',
    method: 'get'
  })
}

/** 随手拍 */
export function snapshotTodo() {
  return request({
    url: cloud.dqbasic + '/snapshotTodo',
    method: 'get'
  })
}

/** 部门隐患情况 */
export function deptHiddenDangerAnalyze() {
  return request({
    url: cloud.dqbasic + '/deptHiddenDangerAnalyze',
    method: 'get'
  })
}

/** 组织架构 */
export function saftyOrg() {
  return request({
    url: cloud.dqbasic + '/saftyOrg',
    method: 'get'
  })
}
/** 数据展示统计 */
export function dataDisplayStatistics() {
  return request({
    url: cloud.dqbasic + '/dataDisplayStatistics',
    method: 'get'
  })
}
/** 首页巡检任务饼图统计 */
export function inspectionTaskStatistics() {
  return request({
    url: cloud.dqbasic + '/inspectionTaskStatistics',
    method: 'get'
  })
}
/** 首页隐患饼图统计 */
export function hiddenDangerStatistics() {
  return request({
    url: cloud.dqbasic + '/hiddenDangerStatistics',
    method: 'get'
  })
}
/** 首页隐患类型饼图统计 */
export function hiddenDangerTypeStatistics() {
  return request({
    url: cloud.dqbasic + '/hiddenDangerTypeStatistics',
    method: 'get'
  })
}
/** 特殊作业饼图统计 */
export function specialWorkStatistics() {
  return request({
    url: cloud.dqbasic + '/specialWorkStatistics',
    method: 'get'
  })
}
/** 特殊作业饼图统计 */
export function inspectionTaskListStatistics(userId) {
  return request({
    url: cloud.dqbasic + '/inspectionTaskListStatistics?userId='+userId,
    method: 'get'
  })
}