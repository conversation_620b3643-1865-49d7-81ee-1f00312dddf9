import request, {cloud}  from '@/framework/utils/request'

// 查询设备管理列表
export function listDeviceManager(query) {
  return request({
    url: cloud.dqbasic + '/deviceManager/page',
    method: 'get',
    params: query
  })
}

// 查询设备管理详细
export function getDeviceManager(id) {
  return request({
    url: cloud.dqbasic + '/deviceManager/detail?id=' + id,
    method: 'get'
  })
}

// 新增设备管理
export function addDeviceManager(data) {
  return request({
    url: cloud.dqbasic + '/deviceManager/add',
    method: 'post',
    data: data
  })
}

// 修改设备管理
export function updateDeviceManager(data) {
  return request({
    url: cloud.dqbasic + '/deviceManager/edit',
    method: 'post',
    data: data
  })
}

// 删除设备管理
export function delDeviceManager(id) {
  return request({
    url: cloud.dqbasic + '/deviceManager/delete',
    method: 'post',
    data: { ids: id }
  })
}
