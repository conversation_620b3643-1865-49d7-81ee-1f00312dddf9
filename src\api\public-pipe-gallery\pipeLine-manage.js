/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-08-09 09:05:51
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-08-09 11:12:11
 * @Description: 管道管理
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */

import request, {cloud}  from '@/framework/utils/request'

// 查询管道管理列表
export function listPipeLineManage(query) {
  return request({
    url: cloud.dqbasic +'/pipeLineManage/page',
    method: 'get',
    params: query
  })
}

// 查询管道管理详细
export function getPipeLineManage(id) {
  return request({
    url: cloud.dqbasic +'/pipeLineManage/detail?id=' + id,
    method: 'get'
  })
}

// 新增管道管理
export function addPipeLineManage(data) {
  return request({
    url: cloud.dqbasic +'/pipeLineManage/add',
    method: 'post',
    data: data
  })
}

// 修改管道管理
export function updatePipeLineManage(data) {
  return request({
    url: cloud.dqbasic +'/pipeLineManage/edit',
    method: 'post',
    data: data
  })
}

// 删除管道管理
export function delPipeLineManage(id) {
  return request({
    url: cloud.dqbasic +'/pipeLineManage/delete',
    method: 'post',
    data: { ids: id }
  })
}
