import request from '@/framework/utils/request'

// 分页查询所在部位管理所有数据
export function bizLocationPage(query) {
  return request({
    url: '/project/bizLocation/page',
    method: 'get',
    params: query
  })
}

// 查询所在部位列表
export function bizLocationSelectList(data) {
  return request({
    url: '/project/bizLocation/selectList',
    method: 'POST',
    data
  })
}

// 查询所在部位管理详细
export function bizLocationDetail(params) {
  return request({
    url: '/project/bizLocation/detail',
    method: 'get',
    params
  })
}

// 新增所在部位管理
export function bizLocationAdd(data) {
  return request({
    url: '/project/bizLocation/add',
    method: 'post',
    data
  })
}

// 修改所在部位管理
export function bizLocationEdit(data) {
  return request({
    url: '/project/bizLocation/edit',
    method: 'post',
    data
  })
}

// 删除所在部位管理
export function delLocation(locationId) {
  return request({
    url: '/project/bizLocation/delete',
    method: 'post',
    data: { locationIds: locationId }
  })
}

// 作业场所启用-停用操作
export function updateLocationEnable(data) {
  return request({
    url: '/project/bizLocation/updateEnable',
    method: 'post',
    data
  })
}
