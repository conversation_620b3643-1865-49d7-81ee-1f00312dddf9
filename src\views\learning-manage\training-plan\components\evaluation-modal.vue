<template>
  <el-dialog
    title="培训评价"
    v-bind="$attrs"
    width="400px"
    top="10vh"
    v-on="$listeners"
    @open="handleOpen"
    @closed="handleClosed"
  >
    <div class="training-name">
      培训名称：{{ trainingName }}
    </div>

    <el-descriptions
      v-loading="isLoading"
      :column="1"
      :colon="false"
      border
    >
      <el-descriptions-item
        v-for="item in descriptionItemList"
        :key="item.prop"
        :label="item.label"
      >
        {{ item.formatter ? item.formatter(evaluation[item.prop]) : evaluation[item.prop] }}
      </el-descriptions-item>
    </el-descriptions>

    <div slot="footer">
      <el-button @click="handleCancel">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getTrainingPlanEvaluation } from '@/api/learning-manage/training-plan'
import { isDefined } from '@/utils'

export default {
  name: 'StatisticModal',
  props: {
    trainingId: {
      type: String,
      required: true
    },

    trainingName: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      isLoading: false,

      descriptionItemList: [
        { prop: 'totalStuff', label: '实际参加人数' },
        { prop: 'evaluatTotal', label: '评价人数' },
        { prop: 'verySatisfiedTotal', label: '很满意 (人)' },
        { prop: 'satisfiedTotal', label: '满意 (人)' },
        { prop: 'acceptableTotal', label: '尚可 (人)' },
        { prop: 'noSatisfiedTotal', label: '不满意 (人)' },
        { prop: 'veryDissatisfiedTotal', label: '很不满意 (人)' },
        { prop: 'satisfactionLevel', label: '整体满意率' }
      ],

      evaluation: {}
    }
  },

  methods: {
    handleOpen() {
      this.getData()
    },

    handleClosed() {
      this.reset()
    },

    reset() {
      this.evaluation = {}
      this.isLoading = false
    },

    handleCancel() {
      this.$emit('update:visible', false)
    },

    adaptData(data) {
      for (const key of Object.keys(data)) {
        if (!isDefined(data[key])) {
          data[key] = '-'
        } else if (key === 'satisfactionLevel') {
          data[key] = `${data[key]}%`
        }
      }

      return data
    },

    async getData() {
      this.isLoading = true
      const currentTrainingId = this.trainingId
      try {
        const response = await getTrainingPlanEvaluation(this.trainingId)
        if (currentTrainingId !== this.trainingId) return
        this.evaluation = this.adaptData(response.data)
      } catch (err) {
        this.$message.error('获取评价数据失败')
        console.error('获取数据失败:', err)
      } finally {
        if (currentTrainingId === this.trainingId) {
          this.isLoading = false
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.training-name {
  margin-bottom: 10px;
}
::v-deep .el-descriptions__body .el-descriptions__table  .el-descriptions-item__label {
  width: 120px;
  color: #131414;
  text-align: right;
}
</style>
