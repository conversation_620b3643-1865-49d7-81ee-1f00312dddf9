/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-07-15 08:31:36
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 17:10:35
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import {getRequest, postRequest} from '@/framework/utils/request'

// 查询车辆出入园管理列表
export function listAccessControl(query) {
  return getRequest('/project/carAccessControl/page', query)
}

// 查询车辆出入园管理详细
export function getAccessControl(id) {
  return getRequest('/project/carAccessControl/detail?id=' + id)
}

// 新增车辆出入园管理
export function addAccessControl(data) {
  return postRequest('/project/carAccessControl/add', data)
}

// 修改车辆出入园管理
export function updateAccessControl(data) {
  return postRequest('/project/carAccessControl/edit', data)
}

// 删除车辆出入园管理
export function delAccessControl(id) {
  return postRequest('/project/carAccessControl/delete', { ids: id })
}
