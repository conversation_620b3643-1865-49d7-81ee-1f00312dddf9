<template>
  <div :class="[type === 'train' ? 'external-login-bg-train' : 'external-login-bg-exam']">
    <div class="external-login">
      <div class="external-login-box-wrapper">
        <div class="external-login-box">
          <div class="external-login-box-title">
            <span>{{ formTitle }}</span>
          </div>
          <div class="external-login-box-line"></div>
          <div class="external-login-form-wrapper">
            <el-form label-width="124px" :rules="rules" ref="externalLoginForm" :model="form">
              <el-form-item label="姓名：" prop="name">
                <el-input v-model="form.name" placeholder="请输入您的姓名"></el-input>
              </el-form-item>
              <el-form-item label="手机号：" prop="phone">
                <el-input v-model="form.phone" placeholder="请输入您的手机号"></el-input>
              </el-form-item>
              <el-form-item label="工作单位：" prop="company">
                <el-select v-model="form.company" placeholder="请选择您的工作单位" style="width: 100%;"
                  filterable
                  :loading="companyLoading"
                  clearable
                  @clear="loadCompanyList(null)"
                >
                  <el-option
                    v-for="item in companyList"
                    :key="item.id"
                    :label="item.companyName  "
                    :value="item.id">
                  </el-option>
                </el-select>
                <!-- <el-input v-model="form.company" placeholder="请选择"></el-input> -->
              </el-form-item>
            </el-form>  
          </div>
          
          <div class="external-login-btn-wrapper">
            <el-button class="external-login-btn" type="primary" :loading="loginLoading" @click="beforeLogin">
              {{ loginButtonText }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { resetRouter } from '@/router'
import { getCompanyList } from '@/api/external-login/external-login';
import { getLeftMenu } from '@/framework/api/menu'
import themepicker from '@/framework/utils/mixins/themepicker'
import { dealRouter } from '@/framework/utils/menu'
export default {
    mixins: [themepicker],
    name: 'ExternalLogin',
    data() {
      return {
        type: '',
        formTitle: '',
        loginButtonText: '',
        companyList: [],
        loginLoading: false,
        companyLoading: false,
        form: {
          name: '',
          phone: '',
          company: '',
          tenantId: '000000'
        },
        rules: {
          name: [
            { required: true, message: '请输入姓名', trigger: 'blur' },
          ],
          phone: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
          ],
          company: [
            { required: true, message: '请选择工作单位', trigger: 'blur' },
          ],
        }
      }
    },
    mounted() {
      let {type} = this.$route.query
      if (!type) {
        type = 'train'
      }
      this.type = type
      if (type === 'train') {
        this.formTitle = '相关方培训'
        this.loginButtonText = '进入培训'
      } else if (type === 'exam') {
        this.formTitle = '相关方考试'
        this.loginButtonText = '进入考试'
      } else {
        this.$message.error('参数错误')
      }
      this.loadCompanyList(null);
    },
    methods: {
      // 获取当前主题信息
      async getCurrentThemeInfo() {
        const themeArr = await this.$store.dispatch('settings/getCurrentThemeInfo')
        themeArr.forEach((res) => {
          if (res.key === 'theme') {
            this.initTheme(res.value)
          }
        })
      },

      // 走登录接口前的校验
      beforeLogin() {
        this.$refs.externalLoginForm.validate((valid) => {
          if (valid) {
            this.login()
          }
        })
      },
      login() {
        this.loginLoading = true
        this.$store.dispatch('user/externalLogin', this.form).then(async () => { // 走登录接口
          this.getCurrentThemeInfo()
          // 查询角色的所有菜单 有大屏优先进大屏; 没有大屏 进入第一个菜单
          const resp = await getLeftMenu()
          // 安全生产菜单
          const safetyMenus = resp.data.all.find((v) => v.appCode == 'safe')
          // 获取第一个不是大屏的菜单
          const target = this.findLeafMenu(safetyMenus.children.filter((v) => v.menuCode != 'BigScreenHome'))
          if (target) {
            resetRouter()
            const { roles } = await this.$store.dispatch('user/getInfo')
            await dealRouter(target.path, roles)
            this.$router.push({ path: target.path })
          }

        }).finally(() => {
          this.loginLoading = false
        })
      },

      // 获取公司列表
      loadCompanyList(orgName) {
        const query = {}
        if (orgName) {
          query.orgName = orgName
        }
        this.companyLoading = true;
        getCompanyList(query).then(res => {
          if (res.code === '00000') {
            this.companyList = res.data;
          } else {
            this.$message.error(res.msg);
          }
        }).finally(() => {
          this.companyLoading = false;
        })
      },

      // 递归查找最后一级菜单
      findLeafMenu(data) {
        for (let i = 0; i < data.length; i++) {
          if (data[i].menuType == 'C') {
            return data[i]
          } else if (data[i].children && data[i].children.length > 0) {
            const result = this.findLeafMenu(data[i].children)
            if (result) {
              return result
            }
          }
        }
      },
    }
  }
</script>

<style lang="scss" scoped>
.external-login-bg-train{
  height: 100%;
  background-repeat: no-repeat;
  background-image: url('~@/assets/external-login/bg-train.png');
  background-size: cover;
}

.external-login-bg-exam{
  height: 100%;
  background-repeat: no-repeat;
  background-image: url('~@/assets/external-login/bg-exam.png');
  background-size: cover;
}
.external-login {
  height: 100%;
  background-color: rgba(33,57,139,0.32);
}
.external-login-box-wrapper{
  width: 482px;
  margin: 0px auto; 
  padding-top: 19vh;
}

.external-login-box {
  width: 100%;
  height: 460px;
  position: relative;

  background: linear-gradient( 180deg, rgba(255,255,255,0.86) 0%, #FFFFFF 100%);
  box-shadow: 0px 2px 24px 0px rgba(22,87,183,0.13), 0px 8px 24px 0px rgba(22,86,183,0.22);
  border-radius: 10px;

  opacity: 0.92;
  border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 1 1;
}
.external-login-box-title{
  text-align: center;
  padding: 24px 24px 21px 24px;
  font-family: SourceHanSansCN, SourceHanSansCN;
  font-weight: bold;
  font-size: 18px;
  color: #3461FF;
  line-height: 18px;
  font-style: normal;
}
.external-login-box-line{
  width: 482px;
  height: 1px;
  background: #D1E0EC;
}
.external-login-form-wrapper{
  margin-right: 33px;
  margin-top: 48px;
  ::v-deep{
    .el-form-item{
      margin-bottom: 24px;
    }
    .el-form-item__label{
      padding: 0;
    }
  }
}
.external-login-btn-wrapper{
  display: flex;
  justify-content: center;
  position: absolute;
  bottom: 54px;
  width: 100%;
}
.external-login-btn{
  width: 358px;
  height: 46px;
}
</style>