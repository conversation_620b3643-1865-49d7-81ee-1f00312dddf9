
/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-02 15:19:01
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-16 09:27:50
 * @Description: 培训课程
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'
// 分页查询培训课程信息
export function getPlanInfoList(query) {
  return request({
    url: '/project/trainingBaseInfo/page',
    method: 'get',
    params: query
  })
}
  
// 新增培训课程信息
export function addPlanInfo(data) {
  return request({
    url: '/project/trainingBaseInfo/add',
    method: 'post',
    data: data
  })
}
// 修改培训课程信息
export function editPlanInfo(data) {
  return request({
    url: '/project/trainingBaseInfo/edit',
    method: 'post',
    data: data
  })
}
// 启用与禁用
export function updateStatus(data) {
  return request({
    url: '/project/trainingBaseInfo/updateStatus',
    method: 'post',
    data: data
  })
}
// 根据培训id查询培训签到情况
export function selectCheckCount(query) {
  return request({
    url: '/project/trainingBaseInfo/selectCheckCount',
    method: 'get',
    params: query

  })
}

// 删除培训课程信息
export function deletePlanInfo(data) {
  return request({
    url: '/project/trainingBaseInfo/delete',
    method: 'post',
    data: data

  })
}

// 获取培训基础信息单条数据详情
export function getPlanInfoDetail(query) {
  return request({
    url: '/project/trainingBaseInfo/detail',
    method: 'get',
    params: query

  })
}


// 获取培训基础信息单条数据详情
export function selectCourseProcess(query) {
  return request({
    url: '/project/trainingBaseInfo/selectCourseProcess',
    method: 'get',
    params: query

  })
}

// 学员详情 根据培训id查询每个学员的进度
export function selectRememberLearnProcess(query) {
  return request({
    url: '/project/trainingBaseInfo/selectRememberLearnProcess',
    method: 'get',
    params: query

  })
}

