import { default as request, cloud } from '@/framework/utils/request'

// 业务关联-查询本低代码应用下的表单(下拉框)
export function getFormListByAppId(query) {
  return request({
    url: `${cloud.devcenter}/components/getFormListByAppId`,
    method: 'get',
    params: query
  })
}

// 查询表单的所有字段(下拉框)
export function listByFormId(query) {
  return request({
    url: `${cloud.devcenter}/customFormColumnInfo/listByFormId`,
    method: 'get',
    params: query
  })
}

// 分页查询表单数据列表(点击组件)
export function customFormDataGetList(query) {
  return request({
    url: `${cloud.devcenter}/customFormData/getList/${query.formId}`,
    method: 'get',
    params: query
  })
}

export function orgSelects(query) {
  return request({
    url: `${cloud.devcenter}/components/orgSelects`,
    method: 'get',
    params: query
  })
}
// 根据ids查询组织详情
export function getOrgTreeByOrgIds(query) {
  return request({
    url: `${cloud.devcenter}/components/getOrgTreeByOrgIds`,
    method: 'get',
    params: query
  })
}

