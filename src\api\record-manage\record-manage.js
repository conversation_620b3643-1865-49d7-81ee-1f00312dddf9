/*
 * @Author: du<PERSON>yan <EMAIL>
 * @Date: 2024-07-02 15:55:26
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-08-30 17:15:51
 * @FilePath: \isrmp_vue\src\api\record-manage\record-manage.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import { default as request, cloud } from '@/framework/utils/request'

// 查询文件列表
export function listDir(query) {
  return request({
    url: cloud.dqbasic + '/archives/page',
    method: 'get',
    params: query
  })
}

// 查询文件详细
export function getDirDetail(id) {
  return request({
    url: cloud.dqbasic + '/archives/detail?id=' + id,
    method: 'get'
  })
}

// 新增文件或文件夹
export function addArchives(data) {
  return request({
    url: cloud.dqbasic + '/archives/add',
    method: 'post',
    data: data
  })
}

// 修改文件或文件夹
export function updateArchives(data) {
  return request({
    url: cloud.dqbasic + '/archives/edit',
    method: 'post',
    data: data
  })
}

// 删除文件或文件夹
export function delArchives(id) {
  return request({
    url: cloud.dqbasic + '/archives/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 文件目录列表
export function getDirList(query) {
  return request({
    url: cloud.dqbasic + '/archives/dir',
    method: 'get',
    params: query
  })
}


/** 获取回收站列表 */
export function getRecycleList() {
  return request({
    url: cloud.dqbasic + '/archives/recycle',
    method: 'get'
  })
}
// 回收站删除文件或文件夹
export function delRecycle(id) {
  return request({
    url: cloud.dqbasic + '/archives/recycle/delete',
    method: 'post',
    data: { id: id }
  })
}

// 查询修订列表
export function archiveReviseList(query) {
  return request({
    url: cloud.dqbasic + '/archiveRevise/page',
    method: 'get',
    params: query
  })
}

// 查询文件详细
export function getReviseDetail(id) {
  return request({
    url: cloud.dqbasic + '/archiveRevise/detail?id=' + id,
    method: 'get'
  })
}

// 新增文件或文件夹
export function addRevise(data) {
  return request({
    url: cloud.dqbasic + '/archiveRevise/add',
    method: 'post',
    data: data
  })
}

