import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 查询分类管理分页列表
  getPage(params) {
    return request({
      url: `${cloud.devcenter}/sysAppType/page`,
      method: 'get',
      params
    })
  },
  // 新建应用分类
  sysAppTypeAdd(data) {
    return request({
      url: `${cloud.devcenter}/sysAppType/add`,
      method: 'post',
      data
    })
  },
  // 分类详情
  sysAppTypeDetail(params) {
    return request({
      url: `${cloud.devcenter}/sysAppType/detail`,
      method: 'get',
      params
    })
  },
  //  修改应用分类
  sysAppTypeEdit(data) {
    return request({
      url: `${cloud.devcenter}/sysAppType/edit`,
      method: 'post',
      data
    })
  },
  // 删除应用分类
  deleteType(data) {
    return request({
      url: `${cloud.devcenter}/sysAppType/delete`,
      method: 'post',
      data
    })
  },
  // 查询分类管理不分页列表
  sysAppTypeList(params) {
    return request({
      url: `${cloud.devcenter}/sysAppType/list`,
      method: 'get',
      params
    })
  }
})
