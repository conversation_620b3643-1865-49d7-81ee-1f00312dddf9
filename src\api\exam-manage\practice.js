import request, { cloud } from '@/framework/utils/request'

// 练习查询
export function getTrainingExercisePage(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExercise/page`,
    method: 'get',
    params: query
  })
}

// 练习详情
export function getTrainingExerciseDetail(query) {
  return request({
    url: `${cloud.dqbasic}/trainingExercise/detail`,
    method: 'get',
    params: query
  })
}

// 练习新增
export function trainingExerciseAdd(data) {
  return request({
    url: `${cloud.dqbasic}/trainingExercise/add`,
    method: 'post',
    data
  })
}

// 练习修改
export function trainingExerciseEdit(data) {
  return request({
    url: `${cloud.dqbasic}/trainingExercise/edit`,
    method: 'post',
    data
  })
}

// 练习删除
export function trainingExerciseDelete(data) {
  return request({
    url: `${cloud.dqbasic}/trainingExercise/delete`,
    method: 'post',
    data
  })
}

// 练习启用、停用
export function updateStatus(data) {
  return request({
    url: `${cloud.dqbasic}/trainingExercise/updateStatus`,
    method: 'post',
    data
  })
}

