import { default as request, cloud } from '@/framework/utils/request'
import { kdf } from 'crypto-js'
// 分页查询定制化表单所有数据
export function customFormInfoPage(query) {
  return request({
    url: `${cloud.unifiedreport}/customFormInfo/page`,
    method: 'get',
    params: query
  })
}
// 表单详情
export function customFormInfoDetail(formId) {
  return request({
    url: `${cloud.devcenter}/customFormInfo/detail?formId=${formId}`,
    method: 'get'
  })
}
// 保存表单页面设计
export function savePageDesign(data) {
  return request({
    url: `${cloud.devcenter}/customFormInfo/savePageDesign`,
    method: 'post',
    data
  })
}
// // 获取字典表
// export function getDictList(params) {
//   return request({
//     url: `${cloud.dqbasic}/dict/list`,
//     method: 'get',
//     params
//   })
// }
// 判断从表名重复
export function existTableCode(query) {
  return request({
    url: `${cloud.devcenter}/customFormInfo/existTableCode`,
    method: 'get',
    params: query
  })
}
// --------------------------------------------------------------
// 开发者设计表单 配置项中
// 表单列信息列表
export function columnList(query) {
  return request({
    url: `${cloud.devcenter}/customFormInfo/column/list`,
    method: 'get',
    params: query
  })
}
// 列设置/customFormInfo/listShowSetting
export function listShowSetting(query) {
  return request({
    url: `${cloud.devcenter}/customFormInfo/listShowSetting`,
    method: 'post',
    data: query
  })
}
// 表单搜索框设置
export function listQuerySetting(query) {
  return request({
    url: `${cloud.devcenter}/customFormInfo/listQuerySetting`,
    method: 'post',
    data: query
  })
}
// listSortSetting
export function listSortSetting(query) {
  return request({
    url: `${cloud.devcenter}/customFormInfo/listSortSetting`,
    method: 'post',
    data: query
  })
}
//-------------------------------------

// // 表单按钮列表
// export function buttonList(query) {
//   return request({
//     url: `${cloud.devcenter}/customFormInfo/button/list`,
//     method: 'get',
//     params: query
//   })
// }
// // 排序设置
// export function datasortSet(query) {
//   return request({
//     url: `${cloud.devcenter}/customFormInfo/datasort-set`,
//     method: 'post',
//     data: query
//   })
// }
//----------------------------------------
// 查询表单页面配置json，查询结果用于页面渲染 table
// customFormInfo/queryPageDesign/{formId}
export function queryPageDesign(query) {
  return request({
    url: `${cloud.devcenter}/customFormInfo/queryPageDesign/${query.formId}`,
    method: 'get',
    params: query
  })
}
// 排序等设置后的jsons结构数据
export function queryListDesign(query) {
  return request({
    url: `${cloud.devcenter}/customFormInfo/queryListDesign/${query.formId}`,
    method: 'get',
    params: query
  })
}

// ------用户访问表单数据------------------------------------------------
// 查询定制化表单数据列表 -- 开发中心进入
export function getDesignerList(query) {
  return request({
    url: `${cloud.devcenter}/designerFormData/getList/${query.formId}`,
    method: 'get',
    params: query
  })
}
// 查询定制化表单数据列表 -- 门户应用系统进入
export function getList(query) {
  return request({
    url: `${cloud.devcenter}/customFormData/getList/${query.formId}`,
    method: 'get',
    params: query
  })
}

// 新增定制化表单数据
export function customFormDataAdd(data) {
  return request({
    url: `${cloud.devcenter}/customFormData/add/${data.formId}`,
    method: 'post',
    data
  })
}
// 修改定制化表单数据
export function customFormDataEdit(data) {
  return request({
    url: `${cloud.devcenter}/customFormData/edit/${data.formId}`,
    method: 'post',
    data
  })
}
// 删除定制化表单数据
export function customFormDataDelete(data) {
  return request({
    url: `${cloud.devcenter}/customFormData/delete/${data.formId}`,
    method: 'post',
    data
  })
}

// 表单数据详情 主表
export function customFormDataDetail(query) {
  return request({
    url: `${cloud.devcenter}/customFormData/detail/${query.formId}`,
    method: 'get',
    params: query
  })
}
// 表单数据详情 从表列表
export function getDetailTableList(query) {
  return request({
    url: `${cloud.devcenter}/customFormData/getDetailTableList/${query.formId}`,
    method: 'get',
    params: query
  })
}

// 表单数据-导入
export function importUploadFile(data, formId) {
  return request({
    url: `${cloud.devcenter}/customFormData/import/${formId}`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 表单数据-下载导入模板
export function downloadTemplate(params) {
  return request({
    url: `${cloud.devcenter}/customFormData/import/template/${params.formId}`,
    method: 'get',
    responseType: 'arraybuffer'
  })
}
// 表单数据-导出
export function exportFormData(data, formId) {
  return request({
    url: `${cloud.devcenter}/customFormData/export/${formId}`,
    method: 'post',
    // params,
    data,
    responseType: 'arraybuffer'
    // headers: {
    //   'Content-Type': 'application/x-www-form-urlencoded'
    // }
  })
}
// 表单数据-导出表单数据校验
export function exportDataValidation(data, formId) {
  return request({
    url: `${cloud.devcenter}/customFormData/export/validation/${formId}`,
    method: 'post',
    data
  })
}
// 表单数据-导出全部
// export function exportAllMaterial(params) {
//   return request({
//     url: `${cloud.devcenter}/customFormData/exportAll`,
//     method: 'post',
//     params,
//     responseType: 'arraybuffer'
//   })
// }
// 导入失败下载数据 customFormData/downloadFailData
// export function downloadFailData(data) {
//   return request({
//     url: `${cloud.usercenter}sysUser/downloadFailData`,
//     method: 'post',
//     data
//   })
// }

/// /------数据模型--------------
// 数据模型-列信息
export function dataModelColumnList(query) {
  return request({
    url: `${cloud.devcenter}/customFormColumnInfo/dataModel/columnList`,
    method: 'get',
    params: query
  })
}
// 数据模型-保存索引
export function dataModelSaveIndex(data) {
  return request({
    url: `${cloud.devcenter}/customFormColumnInfo/dataModel/saveIndex`,
    method: 'post',
    data
  })
}


/// ----------自定义按钮 执行动作
export function customButtonOperate(data) {
  return request({
    url: `${cloud.devcenter}/customFormData/customButtonOperate/${data.formId}`,
    method: 'post',
    data
  })
}
// 查询流程标题信息
export function getTitleInfo(query) {
  return request({
    url: `${cloud.process}/flow/flowLowCode/getTitleInfo`,
    method: 'get',
    params: query
  })
}
// 查询流程操作记录列表
export function taskList(query) {
  return request({
    url: `${cloud.process}/flow/flowLowCode/taskList`,
    method: 'get',
    params: query
  })
}
// 查询流程操作记录流程视图
export function taskViewList(query) {
  return request({
    url: `${cloud.process}/flow/flowLowCode/taskViewList`,
    method: 'get',
    params: query
  })
}
// 查询流程图记录
export function flowLowCodeImage(query) {
  return request({
    url: `${cloud.process}/flow/flowLowCode/image`,
    method: 'get',
    params: query
  })
}
// 查询流程图节点详情
export function getActivityDetail(query) {
  return request({
    url: `${cloud.process}/flow/instance/getActivityDetail`,
    method: 'get',
    params: query
  })
}
