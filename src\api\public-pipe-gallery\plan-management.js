/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-08-07 13:58:07
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-08-07 15:04:41
 * @Description: 巡检计划管理
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud}  from '@/framework/utils/request'

// 查询巡检计划管理列表
export function listPlanManagement(query) {
  return request({
    url: cloud.dqbasic +'/inspectionPlanManagement/page',
    method: 'get',
    params: query
  })
}

// 查询巡检计划管理详细
export function getPlanManagement(id) {
  return request({
    url: cloud.dqbasic +'/inspectionPlanManagement/detail?id=' + id,
    method: 'get'
  })
}

// 新增巡检计划管理
export function addPlanManagement(data) {
  return request({
    url: cloud.dqbasic +'/inspectionPlanManagement/add',
    method: 'post',
    data: data
  })
}

// 修改巡检计划管理
export function updatePlanManagement(data) {
  return request({
    url: cloud.dqbasic +'/inspectionPlanManagement/edit',
    method: 'post',
    data: data
  })
}

// 删除巡检计划管理
export function delPlanManagement(id) {
  return request({
    url: cloud.dqbasic +'/inspectionPlanManagement/delete',
    method: 'post',
    data: { ids: id }
  })
}
