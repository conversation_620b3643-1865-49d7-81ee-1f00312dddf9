/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-26 11:44:01
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-26 16:09:35
 * @Description: 职业健康监护档案
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'

// 分页查询职业健康监护档案所有数据
export function getHealthRecords(query) {
  return request({
    url: '/project/healthRecords/page',
    method: 'get',
    params: query
  })
}
// 修改职业健康监护档案数据
export function editHealthRecords(data) {
  return request({
    url: '/project/healthRecords/edit',
    method: 'post',
    data: data
  })
}
// 获取职业健康监护档案单条数据详情
export function getHealthRecordDeatil(query) {
  return request({
    url: '/project/healthRecords/detail',
    method: 'get',
    params: query
  })
}
// 删除职业健康监护档案数据
export function deleteHealthRecords(data) {
  return request({
    url: '/project/healthRecords/delete',
    method: 'post',
    data: data
  })
}
// 新增职业健康监护档案数据
export function addHealthRecords(data) {
  return request({
    url: '/project/healthRecords/add',
    method: 'post',
    data: data
  })
}