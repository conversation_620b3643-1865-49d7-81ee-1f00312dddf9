import request from '@/framework/utils/request'

export default ({
  fetchList(params) {
    return request({
      url: '/hrOrganization/tree',
      method: 'get',
      params
    })
  },
  add(data) {
    return request({
      url: '/hrOrganization/add',
      method: 'POST',
      data
    })
  },
  edit(data) {
    return request({
      url: '/hrOrganization/edit',
      method: 'POST',
      data
    })
  },
  detail(id) {
    return request({
      url: '/hrOrganization/detail',
      method: 'get',
      params: {
        orgId: id
      }
    })
  },
  delete(data) {
    return request({
      url: '/hrOrganization/delete',
      method: 'post',
      data
    })
  },
  /**
   * 添加党组织
   * @param {*} data 组织信息
   * @returns res
   */
  saveOrg(data) {
    return request({
      url: '/hrOrganization/saveOrg',
      method: 'POST',
      data
    })
  },
  currentTree(params) {
    return request({
      url: '/hrOrganization/currentTree',
      method: 'get',
      params
    })
  },
  info(id) {
    return request({
      url: '/hrOrganization/info',
      method: 'get',
      params: {
        orgId: id
      }
    })
  },
  detailInfo(id) {
    return request({
      url: '/hrOrganization/detailInfo',
      method: 'get',
      params: {
        orgId: id
      }
    })
  },
  deleteOrg(data) {
    return request({
      url: '/hrOrganization/deleteOrg',
      method: 'post',
      data
    })
  },
  moveTree(data) {
    return request({
      url: '/hrOrganization/moveTree',
      method: 'post',
      data
    })
  }

})
