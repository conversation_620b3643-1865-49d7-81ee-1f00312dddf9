<template>
  <div class="upload-file">
    <el-upload
      ref="upload" multiple :action="uploadFileUrl"
      :before-upload="handleBeforeUpload" :file-list="fileList"
      :limit="fileLimit" :on-error="handleUploadError" :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false" :headers="headers" :data="paramsData"
      :disabled="uploadDisabled" class="upload-file-uploader"
    >
      <!-- 上传按钮 -->
      <el-button :disabled="uploadDisabled" size="mini" type="primary">
        选取文件
      </el-button>
      <!-- 上传提示 -->
      <div v-if="showTip" slot="tip" class="el-upload__tip">
        请上传
        <template v-if="fileSize">
          大小不超过 <b>{{ fileSize }}MB</b>
        </template>

        <template v-if="fileType">
          格式为 <b>{{ fileType.join("/") }}</b>
        </template>
        的文件
      </div>
    </el-upload>
  
    <!-- 文件列表 -->
    <transition-group
      v-if="showFileList" class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear"
      tag="ul"
    >
      <li v-for="(file, index) in fileList" :key="file.url" class="el-upload-list__item ele-upload-list__item-content">
        <el-link :href="`${file.url}`" :underline="false" target="_blank">
          <span class="el-icon-document">
            {{ getFileName(file.name) }}
          </span>
        </el-link>

        <div v-if="!uploadDisabled" class="ele-upload-list__item-content-action">
          <el-link :underline="false" type="danger" @click="handleDelete(index)">
            删除
          </el-link>
        </div>
      </li> 
    </transition-group>
  </div>
</template>
  
<script>
import { getToken } from '@/framework/utils/auth' // get token from cookie
import { cloud } from '@/framework/utils/request'
import emitter from 'element-ui/src/mixins/emitter'
  
export default {
  name: 'DtFileUpload',
  mixins: [emitter],
  inject: {
    elForm: {
      default: ''
    }
  },

  props: {
    /** 
     * 值
     * value: [String, Object, Array]
     */
    value:{
      type:[String, Object, Array],
      default(){
        return[]
      }
    },

    /** 
      * 数量限制
    */
    fileLimit: {
      type: Number,
      default: 5
    },

    /** 
     * 大小限制(MB)
     */
    fileSize: {
      type: Number,
      default: 5
    },

    /** 
     * 文件类型, 例如['png', 'jpg', 'jpeg']
     */
    fileType: {
      type: Array,
      default: () => ['doc', 'docx', 'xlsx', 'xls', 'ppt', 'txt', 'pdf']
    },

    /** *
     * 是否显示提示
     */
    isShowTip: {
      type: Boolean,
      default: true
    },

    /** *
     * 是否禁用
     */
    disabled: {
      type: Boolean,
      default: false
    },

    /**
     * 路径
     */
    fileUrl:{
      type: String,
      default: '/project/bizFileInfo/put-file'
    },

    /**
     * 是否显示文件列表
     */
    showFileList:{
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      number: 0,
      uploadList: [],
      // baseUrl:  process.env.VUE_APP_BASE_API,
      // uploadFileUrl: process.env.VUE_APP_BASE_API + cloud.file + '/sysFileInfo/uploadfile', // 上传地址，必填,
      paramsData: {
        secretFlag: 'N'
      }, // 上传携带的参数，看需求要不要

      headers: {
        'Authorization': getToken()
      },

      fileList: []
    }
  },

  computed: {
    // baseUrl() {
    //   try {
    //     return this.globalAttr.VUE_APP_BASE_API
    //   } catch (error) {
    //     return 'http://***************:30710'
    //   }
    // },
    uploadFileUrl() {
      try {
        return process.env.VUE_APP_BASE_API + this.fileUrl
      } catch (error) {
        return this.globalAttr.VUE_APP_BASE_API +this.fileUrl
      }
    },

    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize)
    },

    uploadDisabled() {
      return this.disabled || (this.elForm || {}).disabled
    }
  },

  watch: {
    value: {
      handler(val) {
        if (val) {
          let temp = 1
          // 首先将值转为数组
          // const list = Array.isArray(val) ? val : this.value.split(',')
          // 然后将数组转为对象数组
  
          this.fileList = val.map(item => {
            if (typeof item === 'string') {
              item = { name: item, url: item, id: item }
            } else {
              item = { name: item.name, url: item.url, id: item.id }
            }
            item.uid = item.uid || new Date().getTime() + temp++
            return item
          })
          this.dispatch('ElFormItem', 'el.form.change', this.fileList)
        } else {
          this.fileList = []
          return []
        }
      },

      deep: true,
      immediate: true
    }
  },

  methods: {
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        let fileExtension = ''
        if (file.name.lastIndexOf('.') > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1)
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true
          if (fileExtension && fileExtension.indexOf(type) > -1) return true
          return false
        })
        if (!isTypeOk) {
          this.$dtModal.msgError(`文件格式不正确, 请上传${this.fileType.join('/')}格式文件!`)
          return false
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$dtModal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }
      this.$dtModal.loading('正在上传文件，请稍候...')
      this.number++
      return true
    },

    // 文件个数超出
    handleExceed() {
      this.$dtModal.msgError(`上传文件数量不能超过 ${this.fileLimit} 个!`)
    },

    // 上传失败
    handleUploadError(err) {
      this.$dtModal.msgError('上传文件失败，请重试')
      this.$dtModal.closeLoading()
    },

    // 上传成功回调
    handleUploadSuccess(res) {
      this.uploadList.push({ name: res.data.fileOriginName, url: res.data.fileLink, id: res.data.id })
      if (this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList)
        this.uploadList = []
        this.number = 0
        // console.log(' this.fileList', this.fileList)
        // this.$emit('input', this.listToString(this.fileList))
        this.$emit('input', this.fileList)
        this.$emit('getFileList',res.data)
        this.$dtModal.closeLoading()
      }
    },

    // 删除文件
    handleDelete(index) {
      this.fileList.splice(index, 1)
      // this.$emit('input', this.listToString(this.fileList))
      this.$emit('input', this.fileList)
    },

    // 获取文件名称
    getFileName(name) {
      if (name.lastIndexOf('/') > -1) {
        return name.slice(name.lastIndexOf('/') + 1)
      } else {
        return name
      }
    },

    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = ''
      separator = separator || ','
      for (const i in list) {
        strs += list[i].url + separator
      }
      return strs != '' ? strs.substr(0, strs.length - 1) : ''
    }
  }
}
</script>
  
  <style scoped lang="scss">
  .upload-file-uploader {
    margin-bottom: 5px;
  }
  
  .upload-file-list .el-upload-list__item {
    border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
  }
  
  .upload-file-list .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
  }
  
  .ele-upload-list__item-content-action .el-link {
    margin-right: 10px;
  }
  </style>
  