import { default as request, cloud } from '@/framework/utils/request'

// 查询日志列表
export function sysMessageCenterPage(query) {
  return request({
    url: `${cloud.notice}/sysMessageCenter/page/V2`,
    method: 'get',
    params: query
  })
}

// 查询日志详情
export function messageDetail(messageId) {
  return request({
    url: `${cloud.notice}/sysMessageCenter/detail?messageId=${messageId}`,
    method: 'get'
  })
}

// 重发
export function retrySendMessage(data) {
  return request({
    url: `${cloud.notice}/sysMessageCenter/retrySendMessage`,
    method: 'post',
    data
  })
}
