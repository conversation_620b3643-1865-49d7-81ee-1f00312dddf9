<template>
  <div>
    <el-dialog
      title="答题详情"
      :visible.sync="dialogVisible"
      width="60%"
      :before-close="handleClose"
      class="custom_dialog paper"
      top="5vh"
    >
      <div class="total">
        <span>答题结果：总计 {{ questionCount }} 道试题，得分 {{ userScore }} 分</span>

        <div style="display: flex;">
          <span style="margin-right: 6px;">
            筛选错误试题
          </span>

          <el-switch
            v-model="queryParams.userResult"
            :active-value="0"
            :inactive-value="null"
            @change="handleQuery"
          />
        </div>
      </div>

      <el-table
        v-loading="loading"
        style="width: 100%;"
        border
        highlight-current-row
        :header-cell-style="{ backgroundColor: '#f2f2f2' }"
        :data="tableData"
      >
        <template slot="empty">
          <p>{{ $store.getters.dataText }}</p>
        </template>

        <el-table-column
          label="题目"
          show-overflow-tooltip
          align="center"
          prop="content"
          min-width="220"
        />

        <el-table-column
          label="类型"
          show-overflow-tooltip
          align="center"
          prop="quesType"
          min-width="60"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.quesType == 0">
              单选题
            </span>

            <span v-else-if="scope.row.quesType == 1">
              多选题
            </span>

            <span v-else-if="scope.row.quesType == 2">
              判断题
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="结果"
          show-overflow-tooltip
          align="center"
          prop="userResult"
          min-width="60"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.userResult == 1">
              <i class="el-icon-check" style="color:#67C23A;font-weight: 700;" />
            </span>

            <span v-else-if="scope.row.userResult == 0">
              <i class="el-icon-close" style="color: #F56C6C;font-weight: 700;" />
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="得分"
          align="center"
          prop="userResult"
          min-width="60"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.userResult == 1 ? scope.row.score : 0 }}</span>
          </template>
        </el-table-column>


        <el-table-column
          label="解析"
          align="center"
          min-width="90"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="showAnalysis(scope.row)">
              查看解析
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <dt-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNo"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-dialog>

    <!-- 查看解析弹框 -->
    <QuestionAnalysisDialog ref="questionAnalysisRef" :show-user-answer="true" />
  </div>
</template>

<script>
import {
  getPaperQuestionPage
} from '@/api/training-examination/my-practice'
import {
  getExamPaperQuestionPage
} from '@/api/training-examination/my-exam'
import QuestionAnalysisDialog from '@/components/question-analysis/index.vue'

export default {
  components: {
    QuestionAnalysisDialog
  },

  data() {
    return {
      // 弹框
      dialogVisible: false,

      // 用户得分
      questionCount: 0,
      userScore: 0,

      // 表格
      loading: false,
      tableData: [],
      total: 0,
      queryParams:{
        userResult:null, // 0 错误 1 正确
        pageNo:1,
        pageSize:10
      },

      // 类型
      type:''
    }
  },

  methods: {
    // 初始化
    init(row) {
      this.userScore = row.userScore
      if (row.exerciseId) { // 练习试卷
        this.type = 'exercise'
        this.queryParams.exercisePaperId = row.id
      }
      if (row.examPlanId) { // 考试试卷
        this.type = 'exam'
        this.queryParams.examUserPaperId = row.id
      }
      this.queryParams.userResult = null
      this.handleQuery()
      this.dialogVisible = true
    },

    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 获取列表
    async getList() {
      const query = JSON.parse(JSON.stringify(this.queryParams))
      this.loading = true
      let res
      if (this.type == 'exercise') {
        res = await getPaperQuestionPage(query)
      } else {
        res = await getExamPaperQuestionPage(query)
      }
      this.loading = false
      if (!res) return
      this.questionCount = res.message
      this.tableData = res.data.rows
      this.total = res.data.totalRows
    },

    // 查看解析
    showAnalysis(row) {
      this.$refs.questionAnalysisRef.init(row.questionId, JSON.parse(JSON.stringify(row)))
    },

    // 关闭弹框
    handleClose() {
      this.questionCount = 0
      this.userScore = 0
      this.type = ''
      this.queryParams = {
        userResult:null, // 0 错误 1 正确
        pageNo:1,
        pageSize:10
      }
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.custom_dialog {
  ::v-deep .el-dialog__header {
    padding: 16px 24px !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ebeef5;
  }

  ::v-deep .el-dialog__header .el-dialog__headerbtn {
    position: relative;
    top: unset;
    right: unset;
  }

  &.paper{
    ::v-deep .el-dialog__body{
        padding: 20px !important;
    }
  }
}

.total{
    margin-bottom: 10px;
    font-size: 14px;
    color: #333;
    font-weight: 700;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
</style>
