/*
 * @Author: du<PERSON><PERSON> <EMAIL>
 * @Date: 2024-04-22 15:53:40
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-04-22 16:42:26
 * @FilePath: \isrmp_vue\src\api\ExpenseBudgetManage\expense-budget-manage.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import { default as request, cloud } from '@/framework/utils/request'

// 查询费用预算管理列表
export function listExpenseBudgetManage(query) {
  return request({
    url: cloud.dqbasic + '/expenseBudgetManage/page',
    method: 'get',
    params: query
  })
}

// 查询费用预算管理详细
export function getExpenseBudgetManage(id) {
  return request({
    url: cloud.dqbasic + '/expenseBudgetManage/detail?id=' + id,
    method: 'get'
  })
}

// 新增费用预算管理
export function addExpenseBudgetManage(data) {
  return request({
    url: cloud.dqbasic + '/expenseBudgetManage/add',
    method: 'post',
    data: data
  })
}

// 修改费用预算管理
export function updateExpenseBudgetManage(data) {
  return request({
    url: cloud.dqbasic + '/expenseBudgetManage/edit',
    method: 'post',
    data: data
  })
}

// 删除费用预算管理
export function delExpenseBudgetManage(id) {
  return request({
    url: cloud.dqbasic + '/expenseBudgetManage/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 不分页 查询左侧 公司列表
export function getExpenseBudgetList(year) {
  return request({
    url: cloud.dqbasic + '/expenseBudgetManage/selectList',
    method: 'post',
    data:  year
  })
}

export function changeStatus(data) {
  return request({
    url: cloud.dqbasic + '/expenseBudgetManage/updateEnable',
    method: 'post',
    data:  data
  })
}
