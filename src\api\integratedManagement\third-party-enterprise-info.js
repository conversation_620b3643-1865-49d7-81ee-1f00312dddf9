import request, {cloud} from '@/framework/utils/request'

// 查询第三方单位管理列表
export function listThirdPartyEnterpriseInfo(query) {
  return request({
    url: cloud.dqbasic + '/thirdPartyEnterpriseInfo/page',
    method: 'get',
    params: query
  })
}

// 查询第三方单位管理详细
export function getThirdPartyEnterpriseInfo(id) {
  return request({
    url: cloud.dqbasic + '/thirdPartyEnterpriseInfo/detail?id=' + id,
    method: 'get'
  })
}

// 新增第三方单位管理
export function addThirdPartyEnterpriseInfo(data) {
  return request({
    url: cloud.dqbasic + '/thirdPartyEnterpriseInfo/add',
    method: 'post',
    data: data
  })
}

// 修改第三方单位管理
export function updateThirdPartyEnterpriseInfo(data) {
  return request({
    url: cloud.dqbasic + '/thirdPartyEnterpriseInfo/edit',
    method: 'post',
    data: data
  })
}

// 删除第三方单位管理
export function delThirdPartyEnterpriseInfo(id) {
  return request({
    url: cloud.dqbasic + '/thirdPartyEnterpriseInfo/delete',
    method: 'post',
    data: { ids: id }
  })
}
