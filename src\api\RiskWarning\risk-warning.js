import request from "@/framework/utils/request"
import { method } from "lodash"

// 查询风险告知与警示信息列表
export function queryRiskWarningList(params) {
  return request({
    url: "/project/bizRiskExternal/page",
    method: "get",
    params
  })
}

// 查询风险告知与警示信息详情
export function queryRiskWarningDetail(params) {
  return request({
    url: "/project/bizRiskExternal/detail",
    method: "get",
    params
  })
}

// 新增风险告知与警示信息详情
export function addRiskWarningDetail(data) {
  return request({
    url: "/project/bizRiskManager/add",
    method: "post",
    data
  })
}

// 编辑风险告知与警示信息详情
export function editRiskWarningDetail(data) {
  return request({
    url: "/project/bizRiskManager/edit",
    method: "post",
    data
  })
}
