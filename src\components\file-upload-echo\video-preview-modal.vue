<!--
  * @Author: jiadongjin <EMAIL>
  * @Date: 2024-04-09 08:43:13
  * @LastEditors: jiadongjin <EMAIL>
  * @LastEditTime: 2024-05-25 09:02:37
  * @Description: 
  * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
-->
<template>
  <div>
    <!-- 标签modal弹窗 -->
    <DtDialog
      :title="modalTitle"
      :visible.sync="dialogVisible"
      :lock-scroll="false"
      :append-to-body="true"
      comfirm-label="确 定"
      :footer-slot="true"
    >
      <video
        v-if="dialogVisible"
        slot="content"
        :src="detectVideo"
        controls
        muted
        loop
        style="width: 100%; height: 400px; object-fit: contain"
      /> 

      <div slot="footer" class="clearfix operation-box">
        <el-button size="small" @click="dialogVisible = false">
          关 闭
        </el-button>
      </div>
    </DtDialog>
  </div>
</template>

<script>
export default {
  props: {
    /**
     * 视频url
     */
    detectVideo: {
      type: String,
      default: function() {
        return ''
      }
    }
  },

  data() {
    return {
      dialogVisible: false
    }
  },

  computed: {
    modalTitle() {
      return "视频预览"
    }
  },

  created() {},
  methods: {
    show() {
      this.dialogVisible = true
    }
  }
}
</script>