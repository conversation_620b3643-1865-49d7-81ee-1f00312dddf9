<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form :model="queryParams" ref="queryForm" :inline="true" @submit.native.prevent v-show="showSearch"
                 label-width="68px">

          <el-form-item label="姓名" prop="ownerUserName">
            <el-input v-model="queryParams.ownerUserName" placeholder="请输入姓名" clearable size="small"
                      style="width: 240px;" @keyup.enter.native="handleQuery"/>
          </el-form-item>
          <el-form-item label="部门" prop="ownerOrgId">
            <DeptSelect v-model="queryParams.ownerOrgId" placeholder="请选择部门" />
          </el-form-item>
        </el-form>
        <div class="flex-1"></div>
        <div>
          <div class="flex-1"></div>
          <div style="display:flex;">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
          </div>
        </div>
      </div>
      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <div class="flex-1"></div>
          <dt-dialog-column v-model="isShowTable" :columns="showColumns" :table-ref="$refs.table"
                            @queryTable="getList"/>
        </div>
        <el-table v-if="isShowTable" v-loading="loading" ref="table" border highlight-current-row
                  :header-cell-style="{ backgroundColor: '#f2f2f2'}" :data="IntegrationRankingList">
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>
          <el-table-column
            fixed="left"
            type="index"
            label="序号"
            width="70"
            align="center"
            :index="(index)=>(queryParams.pageNo - 1) * queryParams.pageSize + index + 1"
          />
          <el-table-column v-for="(item,index) in showColumns" :label="item.label" v-if="item.show"
                           show-overflow-tooltip :key="item.prop"
                           align="center" :prop="item.prop">
            <template slot-scope="scope">
              <span>{{ scope.row[item.prop] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" :key="Math.random()" fixed="right" align="center"
                           class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="small"
                @click="handleDetail(scope.row)"
              >查看明细
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
    <!-- 查看积分明细弹框 -->
    <el-dialog title="积分明细" :visible.sync="open">
      <div>
        <div class="table-opt-container" style="font-size: 16px;font-weight: bold">
          姓名：{{detailRow.ownerUserName}}  岗位: {{detailRow.jobName}}
        </div>
        <el-table v-loading="loading" ref="dialogTable" border highlight-current-row
                  :header-cell-style="{ backgroundColor: '#f2f2f2'}" :data="IntegrationDetailList">
          <el-table-column label="积分操作项" align="center" prop="type">
            <template slot-scope="scope">
              <span>{{ scope.row.type == 1 ? '自主学习' :  '答题练习'  }}</span>
            </template>
          </el-table-column>
          <el-table-column label="积分" align="center" prop="getScore">
            <template slot-scope="scope">
              <span>{{ '+' + scope.row.getScore }}</span>
            </template>
          </el-table-column>
          <el-table-column label="剩余积分" align="center" prop="unGetScore"></el-table-column>
          <el-table-column label="获取时间" align="center" prop="createTime">
            <template slot-scope="scope">
              <span>{{ scope.row.createTime }}</span>
            </template>
          </el-table-column>
        </el-table>
        <dt-pagination
          v-show="detailTotal>0"
          :total="detailTotal"
          :page.sync="detailParams.pageNo"
          :limit.sync="detailParams.pageSize"
          @pagination="handleDetail"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listIntegrationDetail,
  getIntegrationDetail,
  delIntegrationDetail,
  addIntegrationDetail,
  updateIntegrationDetail, listIntegrationDetailRanking
} from "@/api/training-examination/integration/IntegrationDetail";
import DeptSelect from '@/components/dept-select/dept-select.vue'

export default {
  name: "IntegrationDetail",
  components: {DeptSelect},
  data() {
    return {
      // 遮罩层
      loading: true,
      //显隐表格
      isShowTable: true,
      // 选中数组
      ids: [],
      //列显隐数组
      showColumns: [
        {prop: "ownerUserName", label: "姓名", show: true},
        {prop: "ownerOrgName", label: "部门", show: true},
        {prop: "jobName", label: "岗位", show: true},
        {prop: "getScore", label: "总积分", show: true},
        {prop: "endTime", label: "截止时间", show: true}
      ],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      detailTotal: 0,
      // 积分明细表格数据
      IntegrationRankingList: [],
      IntegrationDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      detailRow: {},
      // 查询参数
      queryParams: {
        ownerUserName: null,
        ownerOrgId: null,
        pageNo: 1,
        pageSize: 10,
      },
      detailParams:  {
        userId: null,
        pageNo: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  computed: {
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询积分明细列表 */
    getList() {
      this.loading = true;
      listIntegrationDetailRanking(this.queryParams).then(({data: response}) => {
        this.IntegrationRankingList = response.rows;
        this.total = response.totalRows;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    handleReset() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleDetail(row) {
      if (row){
        this.detailParams.userId = row.ownerUserId;
        this.detailRow = row
      }
      listIntegrationDetail(this.detailParams).then(response => {
        this.IntegrationDetailList = response.data.rows;
        this.detailTotal = response.data.totalRows;
        this.open = true;
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('integrationDetail/IntegrationDetail/export', {
        ...this.queryParams
      }, `integrationDetail_IntegrationDetail.xlsx`)
    }
  }
};
</script>
