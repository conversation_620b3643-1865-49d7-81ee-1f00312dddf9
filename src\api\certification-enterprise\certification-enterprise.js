/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-07-01 08:59:03
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 17:51:38
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud}  from '@/framework/utils/request'
import {getRequest, postRequest} from '@/framework/utils/request'

// 查询列表
export function getList(query) {
  return getRequest(cloud.dqbasic + '/enterpriseCredentials/page', query)
}

// 查询详细
export function getDetail(id) {
  return getRequest(cloud.dqbasic + '/enterpriseCredentials/detail?id=' + id)
}

// 新增
export function add(data) {
  return postRequest(cloud.dqbasic + '/enterpriseCredentials/add', data)
}

// 修改
export function edit(data) {
  return postRequest(cloud.dqbasic + '/enterpriseCredentials/edit', data)
}

// 删除
export function del(id) {
  return postRequest(cloud.dqbasic + '/enterpriseCredentials/delete', { ids: id })
}

// 下载模板
export function downTemplate() {
  return request({
    url: cloud.dqbasic + '/enterpriseCredentials/exportCredentialsTemplate',
    method: 'get',
    responseType: 'arraybuffer'
  })
}

// 导入
export function importTeamExcel(data) {
  return postRequest(cloud.dqbasic + '/enterpriseCredentials/importExcel', data)
}

// 导出
export function exportExcel(query) {
  return request({
    url: cloud.dqbasic + '/enterpriseCredentials/exportCredentials',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 分页查询相关方-公司所有数据
export function getRelateCompanyList(query) {
  return getRequest(cloud.dqbasic + '/relateCompany/page', query)
}

// 查询公司所有数据
export function getHrOrg(query) {
  return getRequest(cloud.dqbasic + '/enterprisePersionCertificates/getHrOrg')
}