import { default as request, cloud } from '@/framework/utils/request'

export default ({
  //  获取业务接口列表
  getApiPage(params) {
    return request({
      url: `${cloud.process}/api/page`,
      method: 'get',
      params
    })
  },
  // 新增接口
  addApi(data) {
    return request({
      url: `${cloud.process}/api/add`,
      method: 'post',
      data
    })
  },
  // 修改接口
  updateApi(data) {
    return request({
      url: `${cloud.process}/api/update`,
      method: 'post',
      data
    })
  },
  // 删除接口
  deleteApi(data) {
    return request({
      url: `${cloud.process}/api/delete`,
      method: 'post',
      data
    })
  },
  // 获取接口详情
  getApiInfo(params) {
    return request({
      url: `${cloud.process}/api/info`,
      method: 'get',
      params
    })
  },
  // 获取应用名称
  getStatusThirdAppList(params) {
    return request({
      url: `${cloud.manage}/sysApp/getStatusThirdAppList`,
      method: 'get',
      params
    })
  }
})
