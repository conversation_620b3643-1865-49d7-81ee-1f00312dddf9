/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-20 16:43:48
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-05-22 13:57:46
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud}  from '@/framework/utils/request'

// 查询列表
export function getList(query) {
  return request({
    url: cloud.dqbasic + '/hazardManage/page',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getDetail(id) {
  return request({
    url: cloud.dqbasic + '/hazardManage/detail?id=' + id,
    method: 'get'
  })
}

// 新增
export function add(data) {
  return request({
    url: cloud.dqbasic + '/hazardManage/add',
    method: 'post',
    data: data
  })
}

// 修改
export function edit(data) {
  return request({
    url: cloud.dqbasic + '/hazardManage/edit',
    method: 'post',
    data: data
  })
}

// 删除
export function del(id) {
  return request({
    url: cloud.dqbasic + '/hazardManage/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 查询危险源列表
export function getHazardSourceList(data) {
  return request({
    url: cloud.dqbasic + '/hazardManage/selectList',
    method: 'post',
    data
  })
}

// 查询可能导致的事故类型列表
export function getPossibleAccidentList(data) {
  return request({
    url: cloud.dqbasic + '/bizPossibleAccidentType/selectList',
    method: 'post',
    data
  })
}

// 查询安全标志列表
export function getSafetySignsList(data) {
  return request({
    url: cloud.dqbasic + '/bizSafetySigns/selectList',
    method: 'post',
    data
  })
}

// 查询作业活动列表
export function getActivityList(data) {
  return request({
    url: cloud.dqbasic + '/bizWorkActivity/selectList',
    method: 'post',
    data
  })
}

// 查询作业场所列表
export function getWorkPlaceList(data) {
  return request({
    url: cloud.dqbasic + '/bizWorkPlace/selectList',
    method: 'post',
    data
  })
}

// 查询所在部位列表
export function getLocationList(data) {
  return request({
    url: cloud.dqbasic + '/bizLocation/selectList',
    method: 'post',
    data
  })
}

// 查询已绑设备
export function getDeviceBindingInfo(data) {
  return request({
    url: cloud.dqbasic + '/deviceManager/getDeviceBindingInfo',
    method: 'post',
    data
  })
}