/*
 * @Author: mxt
 * @Date: 2024-04-22 15:53:40
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 17:33:35
 * @FilePath: \isrmp_vue\src\api\HealthExaminePlan\health-examine-plan.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud} from '@/framework/utils/request'
import {getRequest, postRequest} from '@/framework/utils/request'

// 查询三维仿真应急演练列表
export function emPraDrillsGetPage(query) {
  return getRequest(cloud.dqbasic + '/emergSimulaDrills/page', query)
}

// 查询三维仿真应急演练详细
export function getEmPraDrill(id) {
  return getRequest(cloud.dqbasic + '/emergSimulaDrills/detail?id=' + id)
}

// 新增三维仿真应急演练数据
export function addEmPraDrill(data) {
  return postRequest(cloud.dqbasic + '/emergSimulaDrills/add', data)
}

// 修改三维仿真应急演练数据
export function updateEmPraDrill(data) {
  return postRequest(cloud.dqbasic + '/emergSimulaDrills/edit', data)
}

// 删除三维仿真应急演练
export function delEmPraDrill(id) {
  return postRequest(cloud.dqbasic + '/emergSimulaDrills/delete', { ids: id })
}
