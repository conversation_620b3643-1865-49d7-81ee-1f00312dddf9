/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-18 15:46:34
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-18 16:07:33
 * @Description: 类别管理api
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'
// 分页查询类别管理所有数据
export function getMaCategoryList(query) {
  return request({
    url: '/project/maCategoryManagement/page',
    method: 'get',
    params: query
  })
}
// 修改类别管理数据
export function editMaCategoryInfo(data) {
  return request({
    url: '/project/maCategoryManagement/edit',
    method: 'post',
    data: data
  })
}
// 获取类别管理单条数据详情
export function getMaCategoryDeatil(query) {
  return request({
    url: '/project/maCategoryManagement/detail',
    method: 'get',
    params: query
  })
}
// 删除类别管理数据
export function deleteMaCategoryInfo(data) {
  return request({
    url: '/project/maCategoryManagement/delete',
    method: 'post',
    data: data
  })
}
// 新增类别管理数据
export function addMaCategoryInfo(data) {
  return request({
    url: '/project/maCategoryManagement/add',
    method: 'post',
    data: data
  })
}