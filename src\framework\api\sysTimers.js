import { default as request, cloud } from '@/framework/utils/request'

export default ({
  fetchPage(params) {
    return request({
      url: `${cloud.dqbasic}/sysTimers/page`,
      method: 'get',
      params
    })
  },
  changeStatus(id, status) {
    const url = status === 1 ? '/sysTimers/stop' : '/sysTimers/start'
    return request({
      url: cloud.dqbasic + url,
      method: 'POST',
      data: {
        timerId: id
      }
    })
  },
  getActionClasses(params) {
    return request({
      url: `${cloud.dqbasic}/sysTimers/getActionClasses`,
      method: 'post',
      params
    })
  },
  detail(id) {
    return request({
      url: `${cloud.dqbasic}/sysTimers/detail`,
      method: 'get',
      params: {
        timerId: id
      }
    })
  },
  add(data) {
    return request({
      url: `${cloud.dqbasic}/sysTimers/add`,
      method: 'post',
      data
    })
  },
  edit(data) {
    return request({
      url: `${cloud.dqbasic}/sysTimers/edit`,
      method: 'post',
      data
    })
  },
  delete(data) {
    return request({
      url: `${cloud.dqbasic}/sysTimers/delete`,
      method: 'post',
      data
    })
  },
  // 执行一次
  runOnce(data) {
    return request({
      url: `${cloud.dqbasic}/sysTimers/runOnce`,
      method: 'post',
      data
    })
  }

})

