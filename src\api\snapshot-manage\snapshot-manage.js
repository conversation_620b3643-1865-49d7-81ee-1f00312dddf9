/*
 * @Author: du<PERSON><PERSON> <EMAIL>
 * @Date: 2024-05-29 10:04:06
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-05-29 13:39:06
 * @FilePath: \isrmp_vue\src\api\snapshot-manage\snapshot-manage.js
 * @Description
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, {cloud} from '@/framework/utils/request'

// 随手拍管理列表
export function snapshotPage(query) {
  return request({
    url: cloud.dqbasic + '/bizSnapshot/page',
    method: 'get',
    params: query
  })
}

// 下发整改
export function snapshotEdit(data) {
  return request({
    url: cloud.dqbasic + '/bizSnapshot/rectification',
    method: 'post',
    data: data
  })
}

// 作废
export function snapshotVoid(data) {
  return request({
    url: cloud.dqbasic + '/bizSnapshot/cancel',
    method: 'post',
    data: data
  })
}

// 删除
export function snapshotDelete(id) {
  return request({
    url: cloud.dqbasic + '/bizSnapshot/delete',
    method: 'post',
    data: { ids: id }
  })
}

/** 详情 */
export function snapshotDetail(query) {
  return request({
    url: cloud.dqbasic + '/bizSnapshot/detail',
    method: 'get',
    params: query
  })
}
