import request, {cloud} from '@/framework/utils/request'

// 查询一企一档列表
export function listenterpriseInfo(query) {
  return request({
    url: cloud.dqbasic + '/enterpriseInfo/page',
    method: 'get',
    params: query
  })
}

// 查询一企一档详细
export function getenterpriseInfo(id) {
  return request({
    url: cloud.dqbasic + '/enterpriseInfo/detail?id=' + id,
    method: 'get'
  })
}

// 新增一企一档
export function addenterpriseInfo(data) {
  return request({
    url: cloud.dqbasic + '/enterpriseInfo/add',
    method: 'post',
    data: data
  })
}

// 修改一企一档
export function updateenterpriseInfo(data) {
  return request({
    url: cloud.dqbasic + '/enterpriseInfo/edit',
    method: 'post',
    data: data
  })
}

// 删除一企一档
export function delenterpriseInfo(id) {
  return request({
    url: cloud.dqbasic + '/enterpriseInfo/delete',
    method: 'post',
    data: { ids: id }
  })
}
