/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-20 16:43:48
 * @LastEditors: gaoyu <EMAIL>
 * @LastEditTime: 2025-04-30 13:27:03
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import request, { cloud } from '@/framework/utils/request'
import { getRequest, postRequest } from '@/framework/utils/request'

// 查询列表
export function getList(query) {
  return getRequest(`${cloud.dqbasic}/bizSwEntrance/page`, query)
}

// 查询详细
export function getDetail(id) {
  return getRequest(`${cloud.dqbasic}/specialWorkCheck/detail?id=${id}`)
}

// 新增
export function add(data) {
  return postRequest(`${cloud.dqbasic}/specialWorkCheck/add`, data)
}

// 修改
export function edit(data) {
  return postRequest(`${cloud.dqbasic}/specialWorkCheck/edit`, data)
}

// 删除
export function del(id) {
  return postRequest(`${cloud.dqbasic}/specialWorkCheck/delete`, { ids: id })
}

// 从管理表新增
export function addByWorkid(params) {
  return getRequest(`${cloud.dqbasic}/specialWorkCheck/addByWorkid`, params)
}

/**
 * @description: 断路作业提交验收(验收通过)
 * @param {*} data
 * @return {*} 断路作业提交验收(验收通过)
 */
export function bizSwOpenCircuitComplete(data) {
  return postRequest(`${cloud.dqbasic}/bizSwOpenCircuit/complete/check`, data)
}
/**
 * @description: 断路作业提交验收(验收不通过)
 * @param {*} data
 * @return {*} 断路作业提交验收(验收不通过)
 */
export function bizSwOpenCircuitReject(data) {
  return postRequest(`${cloud.dqbasic}/bizSwOpenCircuit/reject/check`, data)
}
/**
 * @description: 动土作业提交验收(验收通过)
 * @param {*} data
 * @return {*} 动土作业提交验收(验收通过)
 */
export function bizSwBreakGroundComplete(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBreakGround/complete/check`, data)
}
/**
 * @description: 断路作业提交验收(验收不通过)
 * @param {*} data
 * @return {*} 断路作业提交验收(验收不通过)
 */
export function bizSwBreakGroundReject(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBreakGround/reject/check`, data)
}
/**
 * @description: 临时用电作业提交验收(验收通过)
 * @param {*} data
 * @return {*} 临时用电作业提交验收(验收通过)
 */
export function bizSwTemporaryPowerComplete(data) {
  return postRequest(`${cloud.dqbasic}/bizSwTemporaryPower/complete/check`, data)
}
/**
 * @description: 临时用电作业提交验收(验收不通过)
 * @param {*} data
 * @return {*} 临时用电作业提交验收(验收不通过)
 */
export function bizSwTemporaryPowerReject(data) {
  return postRequest(`${cloud.dqbasic}/bizSwTemporaryPower/reject/check`, data)
}
/**
 * @description: 吊装作业提交验收(验收通过)
 * @param {*} data
 * @return {*} 吊装作业提交验收(验收通过)
 */
export function bizSwLiftingComplete(data) {
  return postRequest(`${cloud.dqbasic}/bizSwLifting/complete/check`, data)
}
/**
 * @description: 吊装作业提交验收(验收不通过)
 * @param {*} data
 * @return {*} 吊装作业提交验收(验收不通过)
 */
export function bizSwLiftingReject(data) {
  return postRequest(`${cloud.dqbasic}/bizSwLifting/reject/check`, data)
}
/**
 * @description: 高处作业提交验收(验收通过)
 * @param {*} data
 * @return {*} 高处作业提交验收(验收通过)
 */
export function swHighPlaceComplete(data) {
  return postRequest(`${cloud.dqbasic}/swHighPlace/complete/check`, data)
}
/**
 * @description: 高处作业提交验收(验收不通过)
 * @param {*} data
 * @return {*} 高处作业提交验收(验收不通过)
 */
export function swHighPlaceReject(data) {
  return postRequest(`${cloud.dqbasic}/swHighPlace/reject/check`, data)
}
/**
 * @description: 盲板抽堵作业提交验收(验收通过)
 * @param {*} data
 * @return {*} 盲板抽堵作业提交验收(验收通过)
 */
export function bizSwBlindBlockedComplete(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBlindBlocked/complete/check`, data)
}
/**
 * @description: 盲板抽堵作业提交验收(验收不通过)
 * @param {*} data
 * @return {*} 盲板抽堵作业提交验收(验收不通过)
 */
export function bizSwBlindBlockedReject(data) {
  return postRequest(`${cloud.dqbasic}/bizSwBlindBlocked/reject/check`, data)
}
/**
 * @description: 受限空间作业提交验收(验收通过)
 * @param {*} data
 * @return {*} 受限空间作业提交验收(验收通过)
 */
export function bizSwConfinedSpaceComplete(data) {
  return postRequest(`${cloud.dqbasic}/bizSwConfinedSpace/complete/check`, data)
}
/**
 * @description: 受限空间作业提交验收(验收不通过)
 * @param {*} data
 * @return {*} 受限空间作业提交验收(验收不通过)
 */
export function bizSwConfinedSpaceReject(data) {
  return postRequest(`${cloud.dqbasic}/bizSwConfinedSpace/reject/check`, data)
}
