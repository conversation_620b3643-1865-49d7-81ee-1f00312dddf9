import { default as request, cloud } from '@/framework/utils/request'

export default ({

  // 获取资源列表
  fetchList(params) {
    return request({
      url: `${cloud.dqbasic}/resource/pageList`,
      method: 'get',
      params
    })
  },

  // 获取资源数据
  getResourceById(id) {
    return request({
      url: `${cloud.dqbasic}/resource/getDetail`,
      method: 'get',
      params: {
        resourceCode: id
      }
    })
  },
  // 获取资源数据
  fileUpload(data) {
    return request({
      url: `${cloud.file}/sysFileInfo/upload`,
      method: 'post',
      data,
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },
  // 获取资源数据
  updateAvatar(fileId) {
    return request({
      url: `${cloud.dqbasic}/sysUser/updateAvatar`,
      method: 'post',
      data: { avatar: fileId }
    })
  }

})
