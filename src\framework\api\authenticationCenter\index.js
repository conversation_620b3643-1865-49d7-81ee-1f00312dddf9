import { default as request, cloud } from '@/framework/utils/request'

// 获取概览数据
export function overviewCount() {
  return request({
    url: `${cloud.auth}/auth/overviewCount`,
    method: 'get'
  })
}

// 获取柱状图的数据
export function statisticalAuth(query) {
  return request({
    url: `${cloud.auth}/auth/statisticalAuth`,
    method: 'get',
    params: query
  })
}

// 获取饼图数据
export function pieData(query) {
  return request({
    url: `${cloud.auth}/auth/statisticalAuth`,
    method: 'get',
    params: query
  })
}
