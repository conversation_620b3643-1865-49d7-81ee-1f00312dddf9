import request from '@/framework/utils/request'

export function addAnnualPlan(data) {
  return request({
    url: '/project/trainingYearPlan/add',
    method: 'POST',
    data
  })
}

export function getAnnualPlanList(data) {
  return request({
    url: '/project/trainingYearPlan/page',
    method: 'POST',
    data
  })
}

export function getAnnualPlanDetailById(id) {
  return request({
    url: '/project/trainingYearPlan/detail',
    method: 'GET',
    params: { id }
  })
}

export function deleteAnnualPlanById(id) {
  return request({
    url: '/project/trainingYearPlan/delete',
    method: 'POST',
    data: {
      id
    }
  })
}

export function getAnnualPlanExecutionDetailList(data) {
  return request({
    url: '/project/trainingRealPlan/page',
    method: 'POST',
    data
  })
}

export function submitPostponeReason(data) {
  return request({
    url: '/project/trainingRealPlan/editDelayDesc',
    method: 'POST',
    data
  })
}

export function updateCompletedStatus(data) {
  return request({
    url: '/project/trainingRealPlan/updateCompleteStatus',
    method: 'POST',
    data
  })
}

export function getCompletedPlanList() {
  return request({
    url: '/project/trainingBaseInfo/getFinishList',
    method: 'GET'
  })
}

export function getRealPlanById(id) {
  return request({
    url: '/project/trainingRealPlan/detail',
    method: 'GET',
    params: { id }
  })
}
