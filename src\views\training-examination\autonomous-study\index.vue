<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container" style="display: flex;flex-direction: column;">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="100px"
          @submit.native.prevent
        >
          <el-form-item label="课程名称" prop="videoName">
            <el-input
              v-model.trim="queryParams.videoName"
              class="filter-item limit"
              style="width: 200px; margin-right: 10px"
              maxlength="30"
              clearable
              placeholder="请输入课程名称"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-form>

        <div style="margin-left: 30px">
          <span style="font-size: 14px;">课程栏目</span>
          <el-button :type="item.id === btnName ? 'primary' : ''" @click="handleBtn(item.id)" v-for="item in btnList" :key="item.id" style="margin-left: 15px;">{{ item.name }}</el-button>
          <div class="fr">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery" >搜索</el-button>
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
          </div>
        </div>
      </div>

      <div class="table-container table-fullscreen">
        <el-table
          ref="table"
          v-loading="loading"
          style="width: 100%"
          border
          highlight-current-row
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            type="index"
            label="序号"
            width="70"
            :index="(index) =>(queryParams.pageNo - 1) * queryParams.pageSize + index + 1"
          />

          <el-table-column
            key="videoName"
            label="课程名称"
            show-overflow-tooltip
            align="center"
            prop="videoName"
            min-width="300"
          />

          <el-table-column
            label="课程栏目"
            show-overflow-tooltip
            align="center"
            prop="courseGroupingName"
            min-width="180"
          />

          <el-table-column
            key="resourceType"
            label="课程类型"
            show-overflow-tooltip
            align="center"
            prop="resourceType"
            min-width="120"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.resourceType == '0'"> 视频 </span>
              <span v-else-if="scope.row.resourceType == '1'"> 图文 </span>
            </template>
          </el-table-column>

          <el-table-column
            key="courseScore"
            label="课程分值"
            show-overflow-tooltip
            align="center"
            prop="courseScore"
            min-width="120"
          />

          <el-table-column
            key="createTime"
            label="创建日期"
            show-overflow-tooltip
            align="center"
            prop="createTime"
            min-width="180"
            :formatter="dateFormat"
          />

          <el-table-column
            key="orgName"
            label="创建部门"
            show-overflow-tooltip
            align="center"
            prop="orgName"
            min-width="180"
          />

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            min-width="150"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="handleStudy(scope.row.id)"
              >
                学习
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {
  getStudyList
} from "@/api/training-examination/autonomous-study";
import moment from "moment";

export default {
  name: "AutonomousStudy",
  components: {},
  data() {
    return {
      queryParams: {
        videoName: '',
        courseGrouping: '',
        pageNo: 1,
        pageSize: 10,
      },
      btnName: 'all',
      btnList: [
        {
          id: 'all',
          name: '全部',
        }
      ],
      loading: true,
      tableData: [],
      total: 0,
    };
  },

  computed: {},
  watch: {},
  created() {
    this.getBtnList();
    this.handleQuery();
  },

  mounted() {},
  methods: {
    handleBtn(id) {
      this.btnName = id;
      this.getList();
    },

    /** 查询方法 */
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },

    /** 跳转到学习 */
    handleStudy(id) {
      this.$router.push({ path: '/trainingExamination/autonomousStudyDetail', query: { id } });
    },

    /** 重置方法 */
    handleReset() {
      this.btnName = 'all';
      this.queryParams = {
        videoName: "",
        courseGrouping: this.btnName,
        pageNo: 1,
        pageSize: 10,
      };
      this.getList();
    },
    getBtnList() {
      this.businessDictList({ dictTypeCode: "courseGrouping" }).then((res) => {
        this.btnList = [...this.btnList, ...res.data.rows.map(item => {
          return {
            id: item.dictCode,
            name: item.dictName,
          };
        })];
      });
    },

    /** 查询方法 */
    getList() {
      this.queryParams.courseGrouping = this.btnName === 'all' ? '' : this.btnName;
      this.loading = true;
      getStudyList(this.queryParams).then((data) => {
        this.loading = false;
        this.tableData = data.data.rows;
        this.total = data.data.totalRows;
      });
    },

    /** 格式化表格列时间 */
    dateFormat(row, column, cellValue) {
      return moment(cellValue).format("YYYY-MM-DD HH:mm:ss");
    },
  },
};
</script>
