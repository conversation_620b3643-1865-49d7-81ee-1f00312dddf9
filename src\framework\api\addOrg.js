import { default as request, cloud } from '@/framework/utils/request'

export default ({
  // 查询当前被授权的管理员组上一级的可授权范围和管理范围
  getParentGroupDataScope(params) {
    return request({
      url: `${cloud.permission}/managerGroup/getParentGroupDataScope`,
      method: 'get',
      params: {
        groupId: params.groupId,
        relType: params.relType,
        orgId: params.orgId,
        orgName: params.orgName
      }
    })
  },
  // 查询当前管理员已经绑定的可授权范围和管理范围
  getCurrentGroupDataScope(groupId, relType) {
    return request({
      url: `${cloud.permission}/managerGroup/getCurrentGroupDataScope`,
      method: 'get',
      params: {
        groupId,
        relType
      }
    })
  }
})
