<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="filter-container">
        <el-form
          ref="queryForm"
          inline
          :model="queryParams"
          label-width="100px"
          @submit.native.prevent
        >
          <el-form-item label="姓名">
            <el-input v-model="queryParams.userName" />
          </el-form-item>

          <el-form-item label="部门">
            <DeptSelect v-model="queryParams.orgId" placeholder="请选择创建部门" />
          </el-form-item>

          <el-form-item label="课程名称">
            <el-input v-model="queryParams.courseName" />
          </el-form-item>

          <el-form-item label="课程栏目">
            <el-select v-model="queryParams.courseGrouping" placeholder="请选择" clearable>
              <el-option
                v-for="item in groupOptions"
                :key="item.dictCode"
                :label="item.dictName"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <div class="fr">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">
              搜索
            </el-button>

            <el-button icon="el-icon-refresh" @click="handleReset">
              重置
            </el-button>
          </div>
        </el-form>
      </div>

      <div class="table-container table-fullscreen">
        <div class="table-opt-container">
          <div class="flex-1" />
        </div>

        <el-table
          ref="table"
          v-loading="isLoading"
          style="width: 100%;"
          border
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >

          <el-table-column
            type="index"
            label="序号"
            width="70"
            fixed="left"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            label="姓名"
            show-overflow-tooltip
            prop="userName"
            width="130"
          >
          </el-table-column>

          <el-table-column
            label="部门"
            prop="orgName"
            show-overflow-tooltip
            width="180"
          />

          <el-table-column
            label="岗位"
            prop="post"
            show-overflow-tooltip
            width="140"
          />

          <el-table-column
            label="课程名称"
            prop="courseName"
            show-overflow-tooltip
            width="0"
          />

          <el-table-column
            label="课程类型"
            prop="resourceType"
            show-overflow-tooltip
            width="110"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.resourceType == '0'">
                视频
              </span>
              <span v-else-if="scope.row.resourceType == '1'">
                图文
              </span>
            </template>
          </el-table-column>

          <el-table-column
            label="课程栏目"
            prop="courseGroupingName"
            show-overflow-tooltip
            width="210"
          />

          <el-table-column
            label="学习时长（分钟）"
            prop="learnTime"
            width="110"
          />

          <el-table-column
            label="学习次数（次）"
            prop="learnNumber"
            width="110"
          />
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    
  </div>
</template>

<script>
import { getCourseRecordPage } from '@/api/learning-manage/training-report.js'
import dayjs from 'dayjs'
import DeptSelect from '@/components/dept-select/dept-select.vue'

export default {
  name: 'StudyRecord',
  components: { DeptSelect },
  data() {
    return {

      queryParams: {
        userName: '',
        courseName: '',
        courseGrouping: '',
        orgId: '',
        pageNo: 1,
        pageSize: 10
      },

      total: 0,
      isLoading: false,
      tableData: [],
      groupOptions: []

    }
  },

  created() {
    this.businessDictList({ dictTypeCode: 'courseGrouping' }).then((res) => {
      this.groupOptions = res.data.rows
    })
    this.handleQuery()
  },

  mounted() {

  },

  methods: {
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    handleReset() {
      this.queryParams = {
        userName: '',
        courseName: '',
        courseGrouping: '',
        orgId: '',
        pageNo: 1,
        pageSize: 10
      }
      this.getList()
    },

    getList() {
      this.isLoading = true
      getCourseRecordPage(this.queryParams).then((data) => {
        this.isLoading = false
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      })
    },
  }
}
</script>

<style lang="scss" scoped>

</style>
