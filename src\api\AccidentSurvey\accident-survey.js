/*
 * @Author: du<PERSON><PERSON> <EMAIL>
 * @Date: 2024-04-22 17:04:45
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-05-09 16:41:26
 * @FilePath: \isrmp_vue\src\api\AccidentSurvey\accident-survey.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import { default as request, cloud } from '@/framework/utils/request'

// 查询业务-事故调查列表
export function listAccidentSurvey(query) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurvey/page',
    method: 'get',
    params: query
  })
}

// 查询业务-事故调查详细
export function getAccidentSurvey(id) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurvey/detail?id=' + id,
    method: 'get'
  })
}

// 新增业务-事故调查
export function addAccidentSurvey(data) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurvey/add',
    method: 'post',
    data: data
  })
}

// 修改业务-事故调查
export function updateAccidentSurvey(data) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurvey/edit',
    method: 'post',
    data: data
  })
}

// 删除业务-事故调查
export function delAccidentSurvey(id) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurvey/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 获取数据库表名
export function getTableName(query) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurvey/getTableName',
    method: 'get',
    params: query
  })
}
