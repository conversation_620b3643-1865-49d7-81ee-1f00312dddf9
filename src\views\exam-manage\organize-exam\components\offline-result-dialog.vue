<template>
  <div>
    <el-dialog
      title="考试结果"
      :visible.sync="dialogVisible"
      width="65%"
      :before-close="handleClose"
      top="10vh"
    >
      <el-form
        ref="queryForm"
        :model="queryParams"
        label-width="50px"
        inline
        @submit.native.prevent
      >
        <el-form-item label="姓名" prop="userName">
          <el-input
            v-model.trim="queryParams.userName"
            maxlength="30"
            placeholder="请输入"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="部门" prop="orgId">
          <DeptSelect v-model="queryParams.orgId" placeholder="请选择" />
        </el-form-item>

        <div class="fr">
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">
            搜索
          </el-button>

          <el-button icon="el-icon-refresh" @click="handleReset">
            重置
          </el-button>

          <el-button type="primary" @click="handleDownloadTemplate">
            下载导入模板
          </el-button>

          <el-button
            type="primary"
            :loading="importLoading"
            @click="handleImport"
          >
            导入
          </el-button>
        </div>
      </el-form>

      <el-table
        v-loading="loading"
        style="width: 100%;"
        border
        highlight-current-row
        :header-cell-style="{ backgroundColor: '#f2f2f2' }"
        :data="tableData"
      >
        <template slot="empty">
          <p>{{ $store.getters.dataText }}</p>
        </template>

        <el-table-column
          type="index"
          label="序号"
          width="70"
          :index="
            (index) =>
              (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
          "
        />

        <el-table-column
          label="姓名"
          show-overflow-tooltip
          align="center"
          prop="userName"
        />

        <el-table-column
          label="手机号"
          show-overflow-tooltip
          align="center"
          prop="userPhone"
        />

        <el-table-column
          label="所在部门"
          show-overflow-tooltip
          align="center"
          prop="orgName"
        />

        <el-table-column
          label="分数"
          align="center"
          prop="userScore"
          min-width="60"
        />
      </el-table>

      <dt-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNo"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-dialog>

    <!-- 导入弹框 -->
    <dtDialog
      title="线下考试结果导入"
      :visible.sync="fileUploadVisible"
      width="620px"
      :is-loading="uploadLoading"
      comfirm-label="提 交"
      @closeDialog="handleCloseImport"
      @comfirmBtn="importDataSave"
    >
      <el-form slot="content">
        <div class="">
          <dt-importFile
            ref="dtImportFileRef"
            :down-load-template="handleImportTemplate"
            :type="'线下考试结果'"
          />
        </div>
      </el-form>
    </dtDialog>
  </div>
</template>

<script>
import {
  trainingExamPlanTemplate,
  uploadOfflineResult,
  selectExamOfflineResult
} from '@/api/exam-manage/organize-exam'
import DeptSelect from '@/components/dept-select/dept-select.vue'


export default {
  components: { DeptSelect },
  data() {
    return {
      // 弹框
      dialogVisible: false,

      // 表单
      row:{},

      // 表格
      total:0,
      tableData:[],
      loading:false,
      queryParams:{
        examPlanId:'',
        userName:'', // 姓名
        orgId:'', // 部门
        pageNo:1,
        pageSize:10
      },

      // 导入
      importLoading: false,
      fileUploadVisible: false,
      uploadLoading: false,
      handleImportTemplate: trainingExamPlanTemplate
    }
  },

  methods: {
    // 初始化
    init(row) {
      this.row = row
      this.queryParams.examPlanId = row.examId
      this.handleQuery()
      this.dialogVisible = true
    },

    // 查询
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    // 重置
    handleReset() {
      this.queryParams = {
        examPlanId:this.row.examId,
        userName:'', // 姓名
        orgId:'', // 部门
        pageNo:1,
        pageSize:10
      }
      this.getList()
    },

    // 获取列表
    getList() {
      const query = JSON.parse(JSON.stringify(this.queryParams))
      this.loading = true
      selectExamOfflineResult(query).then((data) => {
        this.loading = false
        this.tableData = data.data.rows
        this.total = data.data.totalRows
      }).catch(() => {
        this.loading = false
      })
    },

    // 下载导入模板
    handleDownloadTemplate() {
      trainingExamPlanTemplate().then((res) => {
        const blob = new Blob([res.data], {
          type: 'application/vnd.ms-excel'
        })
        const objectUrl = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = objectUrl // 指定下载链接
        a.download = '线下考试结果模板' // 指定下载文件名
        a.click()
        URL.revokeObjectURL(a.href) // 释放URL对象
      })
    },

    // 导入
    handleImport() {
      this.importLoading = true
      this.fileUploadVisible = true
      this.$nextTick().then(() => {
        this.$refs.dtImportFileRef.fileList = []
      })
    },

    // 关闭导入弹窗
    handleCloseImport() {
      this.fileUploadVisible = false
      this.importLoading = false
    },

    // 导入数据
    importDataSave() {
      this.uploadLoading = true
      const file = this.$refs.dtImportFileRef.fileList
      if (file.length == 0) {
        this.$dtMessage({
          title: '失败',
          message: '请选择需要上传的文件',
          type: 'error',
          duration: 2000
        })
        return
      }
      const formData = new FormData()
      // 数据
      formData.append('examPlanId', this.row.examId)
      formData.append('file', file[0].raw)
      formData.append('name', file[0].name)
      uploadOfflineResult(formData)
        .then(async (res) => {
          this.importLoading = false
          this.uploadLoading = false
          this.fileUploadVisible = false
          this.handleQuery()
          this.$emit('update')
          this.$dtModal.msgSuccess('导入成功')
        })
        .catch((res) => {
          this.uploadLoading = false
        })
    },

    // 关闭弹框
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>
