import { default as request, cloud } from '@/framework/utils/request'

export default {
// 获取密码规则配置
  getPwdRules: (data) => {
    return request({
      url: '/pwdRules/getPwdRules',
      method: 'get',
      params: { pwdId: '1234567890123456789' }
    })
  },
  // 修改密码规则配置
  updatePwdRules: (data) => {
    return request({
      url: '/pwdRules/updatePwdRules',
      method: 'post',
      data
    })
  },
  // 获取认证规则配置
  getAuthRules: (query) => {
    return request({
      url: `${cloud.auth}/authRules/getAuthRules`,
      method: 'get',
      params: query
    })
  },
  // 修改认证规则配置
  updateAuthRules: (data) => {
    return request({
      url: `${cloud.auth}/authRules/updateAuthRules`,
      method: 'post',
      data
    })
  },

  // 通知模块管理
  // 通知模板列表
  noitceManagelist: (params) => {
    return request({
      url: '/noticeTemplate/page',
      method: 'get',
      params
    })
  },
  // 获取滑块验证码配置
  getSliderKaptcha: (data) => {
    return request({
      url: `${cloud.auth}/kaptcha/getSliderKaptcha`,
      method: 'get'
      // params: { authRuleId: '1234567890123456789' }
    })
  },
  // 校验滑块验证码是否通过
  checkSliderKaptcha: (data) => {
    return request({
      url: `${cloud.auth}/kaptcha/checkSliderKaptcha`,
      method: 'post',
      data
    })
  }
}
