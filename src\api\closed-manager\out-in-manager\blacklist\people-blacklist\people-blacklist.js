/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-07-12 08:31:14
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-08-01 17:13:46
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'
import {getRequest, postRequest} from '@/framework/utils/request'

// 查询人员黑名单管理列表
export function listBlacklist(query) {
  return getRequest('/project/personnelBlacklist/page', query)
}

// 查询人员黑名单管理详细
export function getBlacklist(id) {
  return getRequest('/project/personnelBlacklist/detail?id=' + id)
}

// 新增人员黑名单管理
export function addBlacklist(data) {
  return postRequest('/project/personnelBlacklist/add', data)
}

// 修改人员黑名单管理
export function updateBlacklist(data) {
  return postRequest('/project/personnelBlacklist/edit', data)
}


export function cancelBlacklist(id) {
  return postRequest('/project/personnelBlacklist/cancel', { ids: id })
}
