/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-07-17 14:27:22
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-07-22 16:33:32
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud} from '@/framework/utils/request'

// 查询供应商管理列表
export function listSupplierManagement(query) {
  return request({
    url: cloud.dqbasic + '/maSupplierManagement/page',
    method: 'get',
    params: query
  })
}
// 根据区、县查询省、市
export function queryOwnHigherLevel(params) {
  return request({
    url: `${cloud.dqbasic}/area/queryOwnHigherLevel`,
    method: 'get',
    params
  })
}
// 查询供应商管理详细
export function getSupplierManagement(id) {
  return request({
    url: cloud.dqbasic + '/maSupplierManagement/detail?id=' + id,
    method: 'get'
  })
}

// 新增供应商管理
export function addSupplierManagement(data) {
  return request({
    url: cloud.dqbasic + '/maSupplierManagement/add',
    method: 'post',
    data: data
  })
}

// 修改供应商管理
export function updateSupplierManagement(data) {
  return request({
    url: cloud.dqbasic + '/maSupplierManagement/edit',
    method: 'post',
    data: data
  })
}

// 删除供应商管理
export function delSupplierManagement(data) {
  return request({
    url: cloud.dqbasic + '/maSupplierManagement/delete',
    method: 'post',
    data:data
  })
}
