/*
 * @Author: du<PERSON><PERSON> <EMAIL>
 * @Date: 2024-04-22 17:04:45
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-05-09 16:42:52
 * @FilePath: \isrmp_vue\src\api\AccidentSurveyPerson\accident-survey-person.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import { default as request, cloud } from '@/framework/utils/request'

// 查询业务-事故调查人员伤情列表
export function listAccidentSurveyPerson(query) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyPerson/list',
    method: 'get',
    params: query
  })
}

// 查询业务-事故调查人员伤情详细
export function getAccidentSurveyPerson(id) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyPerson/detail?id=' + id,
    method: 'get'
  })
}

// 新增业务-事故调查人员伤情
export function addAccidentSurveyPerson(data) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyPerson/add',
    method: 'post',
    data: data
  })
}

// 修改业务-事故调查人员伤情
export function updateAccidentSurveyPerson(data) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyPerson/edit',
    method: 'post',
    data: data
  })
}

// 删除业务-事故调查人员伤情
export function delAccidentSurveyPerson(id) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyPerson/delete',
    method: 'post',
    data: { ids: id }
  })
}

export function getAccidentSurveyPersonCount(query) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyPerson/count',
    method: 'get',
    params: query
  })
}
