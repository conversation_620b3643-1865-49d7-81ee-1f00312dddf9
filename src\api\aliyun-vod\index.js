import request from '@/framework/utils/request'

export function createUploadVideo({ fileName, title } = {}) {
  return request({
    method: 'GET',
    url: '/project/CreateUploadVideo',
    params: {
      fileName,
      title
    }
  })
}

export function refreshUploadVideo(videoId) {
  return request({
    method: 'GET',
    url: '/project/refreshUploadVideo',
    params: {
      videoId
    }
  })
}

export function getVideoPlayAuth(videoId) {
  return request({
    url: '/project/getVideoPlayAuth',
    method: 'GET',
    params: {
      videoId
    }
  })
}

export function getPlayInfo(videoId) {
  return request({
    url: '/project/getPlayInfo',
    method: 'GET',
    params: {
      videoId
    }
  })
}
