/*
 * @Author: miteng <EMAIL>
 * @Date: 2024-05-15 16:13:39
 * @LastEditors: miteng <EMAIL>
 * @LastEditTime: 2024-05-20 09:15:02
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request from '@/framework/utils/request'

// 查询业务--检查管理列表
export function listCheckTableManager(query) {
  return request({
    url: '/project/bizCheckTableManager/page',
    method: 'get',
    params: query
  })
}

// 查询业务-检查管理详细
export function getCheckTableManager(id) {
  return request({
    url: '/project/bizCheckTableManager/detail?id=' + id,
    method: 'get'
  })
}

// 新增业务-检查管理
export function addCheckTableManager(data) {
  return request({
    url: '/project/bizCheckTableManager/add',
    method: 'post',
    data: data
  })
}

// 修改业务-检查管理
export function updateCheckTableManager(data) {
  return request({
    url: '/project/bizCheckTableManager/edit',
    method: 'post',
    data: data
  })
}

// 删除业务-检查管理
export function delCheckTableManager(id) {
  return request({
    url: '/project/bizCheckTableManager/delete',
    method: 'post',
    data: { ids: id }
  })
}
