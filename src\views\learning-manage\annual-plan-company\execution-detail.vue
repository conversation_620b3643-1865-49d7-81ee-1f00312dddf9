<template>
  <div class="app-container">
    <div class="mainbox">
      <div class="title-container">
        <h2 class="title">
          {{ $route.query.planYear }}年培训计划执行情况
        </h2>

        <div>
          <el-form label-position="right" inline>
            <el-form-item label="计划部门">
              {{ $route.query.orgName }}
            </el-form-item>

            <el-form-item label="计划培训文件">
              <FileUploadEcho
                disabled
                :show-preview="true"
                :file-id.sync="$route.query.fileId"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div class="filter-container">
        <el-form
          ref="queryForm"
          inline
          :model="queryParams"
          label-width="100px"
          @submit.native.prevent
        >
          <el-form-item label="计划培训时间">
            <el-select v-model="queryParams.trainMonth" clearable>
              <el-option
                v-for="num in 12"
                :key="num"
                :label="`${num}月`"
                :value="num"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="培训类型">
            <el-select v-model="queryParams.trainType" clearable>
              <el-option
                v-for="item in trainingTypeOptions"
                :key="item.id"
                :value="item.dictCode"
                :label="item.dictName"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="是否完成">
            <el-select v-model="queryParams.completeStatus" clearable>
              <el-option
                v-for="item in isCompletedOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="是否延期">
            <el-select v-model="queryParams.delayStatus" clearable>
              <el-option
                v-for="item in isPostponedOptions"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="实际培训时间">
            <el-date-picker
              v-model="actualTrainingTime"
              type="datetimerange"
              start-placeholder="请选择"
              end-placeholder="请选择"
              value-format="yyyy-MM-dd HH:mm:ss"
              clearable
            />
          </el-form-item>

          <div class="fr">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">
              搜索
            </el-button>

            <el-button icon="el-icon-refresh" @click="handleReset">
              重置
            </el-button>
          </div>
        </el-form>
      </div>

      <div class="table-container table-fullscreen">
        <el-table
          ref="table"
          v-loading="loading"
          style="width: 100%;"
          border
          highlight-current-row
          :header-cell-style="{ backgroundColor: '#f2f2f2' }"
          :data="tableData"
        >
          <template slot="empty">
            <p>{{ $store.getters.dataText }}</p>
          </template>

          <el-table-column
            type="index"
            label="序号"
            width="70"
            fixed="left"
            :index="
              (index) =>
                (queryParams.pageNo - 1) * queryParams.pageSize + index + 1
            "
          />

          <el-table-column
            key="trainMonth"
            label="计划培训时间"
            show-overflow-tooltip
            align="center"
            prop="trainMonth"
            width="130"
            :formatter="trainingMonthFormatter"
          />

          <el-table-column
            show-overflow-tooltip
            label="培训类型"
            prop="trainType"
            align="center"
            width="140"
            :formatter="trainingTypeFormatter"
          />

          <el-table-column
            key="remembers"
            show-overflow-tooltip
            label="培训对象"
            align="center"
            prop="remembers"
            min-width="140"
          />

          <el-table-column
            label="计划培训人数"
            prop="planNumber"
            align="center"
            show-overflow-tooltip
            width="130"
          />

          <el-table-column
            label="责任部门"
            prop="orgName"
            align="center"
            show-overflow-tooltip
            width="180"
          />

          <el-table-column
            label="是否完成"
            prop="completeStatus"
            align="center"
            show-overflow-tooltip
            width="180"
            :formatter="isCompletedFormatter"
          />

          <el-table-column
            label="是否延期"
            prop="delayStatus"
            align="center"
            show-overflow-tooltip
            width="180"
            :formatter="isPostponedFormatter"
          />

          <el-table-column
            label="实际培训人数"
            prop="totalStaff"
            align="center"
            show-overflow-tooltip
            width="180"
          />

          <el-table-column
            label="实际培训时间"
            prop="realTimeRange"
            align="center"
            show-overflow-tooltip
            width="180"
          />

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            width="160"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="handleOpenPostponeModal(scope.row)"
              >
                延期说明
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <dt-pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <ConfirmModal
          title="延期说明"
          :visible.sync="isPostponeModalVisible"
          :submit-loading="isSubmitLoading"
          mode="view"
          :default-value="currentDelayDescription"
          @submit="handlePostponeReasonSubmit"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getAnnualPlanExecutionDetailList, submitPostponeReason } from '@/api/learning-manage/annual-plan'
import FileUploadEcho from '@/components/file-upload-echo/file-upload-echo.vue'
import ConfirmModal from '@/components/confirm-modal'

export default {
  name: 'AnnualPlanCompany',
  components: { FileUploadEcho, ConfirmModal },
  data() {
    return {
      isPostponeModalVisible: false,
      isSubmitLoading: false,
      currentRecordId: '',
      currentDelayDescription: '',
      queryParams:{
        yearPlanId: '',
        trainMonth: '',
        trainType: '',
        completeStatus: '',
        delayStatus: '',
        searchBeginTime: '',
        searchEndTime: '',
        pageNo: 1,
        pageSize:10
      },

      isCompletedOptions: [{ label: '是', value: 1 }, { label: '否', value: 0 }],
      isPostponedOptions: [{ label: '是', value: 1 }, { label: '否', value: 0 }],

      total:0,
      loading:false,
      tableData:[],
      modalMode: 'add',
      modalRecord: {},
      modalVisible: false,
      currentFileId: '',
      trainingTypeOptions: []
    }
  },

  computed: {
    actualTrainingTime: {
      set(value) {
        console.log(value)
        if (value && value.length === 2) {
          this.queryParams.searchBeginTime = value[0]
          this.queryParams.searchEndTime = value[1]
        } else {
          this.queryParams.searchBeginTime = ''
          this.queryParams.searchEndTime = ''
        }
      },

      get() {
        if (this.queryParams.searchBeginTime && this.queryParams.searchEndTime) {
          return [this.queryParams.searchBeginTime, this.queryParams.searchEndTime]
        } else {
          return []
        }
      }
    },

    yearPlanId() {
      return this.$route.query.yearPlanId
    }
  },

  watch: {
    yearPlanId: {
      handler(newVal) {
        this.queryParams.yearPlanId = newVal
      },

      immediate: true
    }
  },

  created() {
    this.handleQuery()
    this.getDicts()
  },

  mounted() {},

  methods: {
    handleOpenPostponeModal(row) {
      this.currentRecordId = row.id
      this.currentDelayDescription = row.delayDesc
      this.isPostponeModalVisible = true
    },

    handlePostponeReasonSubmit(reason) {
      this.isSubmitLoading = true
      submitPostponeReason({
        id: this.currentRecordId,
        delayDesc: reason
      }).then(() => {
        this.$message.success('提交成功')
        this.isPostponeModalVisible = false
      }).catch((err) => {
        console.log(err)
        this.$message.error('提交失败')
      }).finally(() => {
        this.isSubmitLoading = false
      })
    },

    trainingMonthFormatter(row, cell, cellValue) {
      // return dayjs().month(cellValue - 1).format('MMM')
      if ([0, 1].includes(row.timeType)) {
        return +row.timeType == 1 ? `${row.timeRange}` : `${cellValue}月`
      } else {
        return 'N/A'
      }
    },

    getDicts() {
      this.businessDictList({ dictTypeCode: 'trainType' }).then((res) => {
        this.trainingTypeOptions = res.data.rows
      })
    },

    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },

    handleReset() {
      this.queryParams = {
        trainMonth: '', // 假设 "计划培训时间" 的下拉框确实是想绑定到 trainMonth
        trainType: '',
        completeStatus: '',
        delayStatus: '',
        searchBeginTime: '', // 显式重置
        searchEndTime: '', // 显式重置
        pageNo: 1,
        pageSize: 10,
        yearPlanId: this.yearPlanId
      }
      this.getList()
    },

    transformListData(listData) {
      return listData
    },

    getList() {
      const loadData = (data) => {
        this.tableData = this.transformListData(data.data.rows)
        this.total = data.data.totalRows
      }

      this.loading = true
      getAnnualPlanExecutionDetailList(this.queryParams)
        .then(loadData)
        .finally(() => {
          this.loading = false
        })
    },

    trainingTypeFormatter(row, column, cellValue) {
      const type = this.trainingTypeOptions.find((item) => item.dictCode === cellValue)
      return type ? type.dictName : cellValue
    },

    isPostponedFormatter(row, column, cellValue) {
      const item = this.isPostponedOptions.find((item) => item.value === cellValue)
      return item ? item.label : cellValue
    },

    isCompletedFormatter(row, column, cellValue) {
      const item = this.isCompletedOptions.find((item) => item.value === cellValue)
      return item ? item.label : cellValue
    }
  }
}
</script>

<style scoped lang="scss">
.title-container {
  .el-form-item {
    margin-bottom: 16px;

    ::v-deep .el-form-item__content {
      margin-bottom: 0 !important;
    }
  }
}
.title {
  text-align: center;
  margin-top: 0;
}
.view-text {
  color: var(--primary);
  cursor: pointer;
  font-weight: 500;
}
</style>
