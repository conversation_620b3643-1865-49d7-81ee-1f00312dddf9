/*
 * @Author: duhongyan <EMAIL>
 * @Date: 2024-04-22 17:04:45
 * @LastEditors: duhongyan <EMAIL>
 * @LastEditTime: 2024-05-09 16:41:57
 * @FilePath: \isrmp_vue\src\api\AccidentSurveyEconomy\accident-survey-economy.js
 * @Description:
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved.
 */
import { default as request, cloud } from '@/framework/utils/request'

// 查询业务-事故调查经济损失列表
export function listAccidentSurveyEconomy(query) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyEconomy/list',
    method: 'get',
    params: query
  })
}

// 查询业务-事故调查经济损失详细
export function getAccidentSurveyEconomy(id) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyEconomy/detail?id=' + id,
    method: 'get'
  })
}

// 新增业务-事故调查经济损失
export function addAccidentSurveyEconomy(data) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyEconomy/add',
    method: 'post',
    data: data
  })
}

// 修改业务-事故调查经济损失
export function updateAccidentSurveyEconomy(data) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyEconomy/edit',
    method: 'post',
    data: data
  })
}

// 删除业务-事故调查经济损失
export function delAccidentSurveyEconomy(id) {
  return request({
    url: cloud.dqbasic + '/bizAccidentSurveyEconomy/delete',
    method: 'post',
    data: { ids: id }
  })
}
