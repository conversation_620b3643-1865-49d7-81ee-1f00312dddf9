/*
 * @Author: wangzexin <EMAIL>
 * @Date: 2024-08-09 11:07:55
 * @LastEditors: wangzexin <EMAIL>
 * @LastEditTime: 2024-08-09 11:08:16
 * @Description: 作业报备
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */

import request, {cloud}  from '@/framework/utils/request'

// 查询作业报备列表
export function listWorkFillPipe(query) {
  return request({
    url: cloud.dqbasic +'/workFillPipe/page',
    method: 'get',
    params: query
  })
}

// 查询作业报备详细
export function getWorkFillPipe(id) {
  return request({
    url: cloud.dqbasic +'/workFillPipe/detail?id=' + id,
    method: 'get'
  })
}

// 新增作业报备
export function addWorkFillPipe(data) {
  return request({
    url: cloud.dqbasic +'/workFillPipe/add',
    method: 'post',
    data: data
  })
}

// 修改作业报备
export function updateWorkFillPipe(data) {
  return request({
    url: cloud.dqbasic +'/workFillPipe/edit',
    method: 'post',
    data: data
  })
}

// 删除作业报备
export function delWorkFillPipe(id) {
  return request({
    url: cloud.dqbasic +'/workFillPipe/delete',
    method: 'post',
    data: { ids: id }
  })
}
