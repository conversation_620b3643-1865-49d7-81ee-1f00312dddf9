/*
 * @Author: jiadongjin <EMAIL>
 * @Date: 2024-05-14 14:51:15
 * @LastEditors: jiadongjin <EMAIL>
 * @LastEditTime: 2024-05-20 16:15:51
 * @Description: 
 * Copyright (c) 2024-present HBIS Digital Technology Co.,Ltd. All rights reserved. 
 */
import request, {cloud}  from '@/framework/utils/request'

// 查询健康数据预警列表
export function listHealthDataWarning(query) {
  return request({
    url: cloud.dqbasic + '/healthDataWarning/page',
    method: 'get',
    params: query
  })
}

// 查询健康数据预警详细
export function getHealthDataWarning(id) {
  return request({
    url: cloud.dqbasic + '/healthDataWarning/detail?id=' + id,
    method: 'get'
  })
}

// 新增健康数据预警
export function addHealthDataWarning(data) {
  return request({
    url: cloud.dqbasic + '/healthDataWarning/add',
    method: 'post',
    data: data
  })
}

// 修改健康数据预警
export function updateHealthDataWarning(data) {
  return request({
    url: cloud.dqbasic + '/healthDataWarning/edit',
    method: 'post',
    data: data
  })
}

// 删除健康数据预警
export function delHealthDataWarning(id) {
  return request({
    url: cloud.dqbasic + '/healthDataWarning/delete',
    method: 'post',
    data: { ids: id }
  })
}

// 修改--报警处理
export function deal(data) {
  return request({
    url: cloud.dqbasic + '/healthDataWarning/deal',
    method: 'post',
    data: data
  })
}
